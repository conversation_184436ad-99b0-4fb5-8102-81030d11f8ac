import { SidebarAppSDK } from '@contentful/app-sdk'
import { <PERSON><PERSON>, Paragraph, Select } from '@contentful/f36-components'
import { useAutoResizer, useCMA, useSDK } from '@contentful/react-apps-toolkit'
import axios from 'axios'
import _ from 'lodash'
import React, { useEffect, useState } from 'react'
import Cache from '../components/PageSettings/components/Cache'
import {
  getEnFieldData,
  getObject,
  replaceSymbols,
  reverseSymbols,
  setObject,
} from '../components/PageSettings/components/Translations/utils'
import PreviewUrls from '../components/Preview'
import { ENV_VARIABLES } from '../constant/variables'

const ignoreKeys = ['slug']
const defaultLocale = 'en-CA'
const defaultLocaleCode = defaultLocale.split('-')[0]
const url = `${ENV_VARIABLES.appBaseURL}/api/translate-text/`
const pattern = /domain/i
const localeForDomains = {
  domainAltusGroupCom: 'fr-CA',
  domainVerifnoCom: 'de-DE',
  domainFinanceActiveCom: 'fr-CA',
}

const Sidebar = () => {
  useAutoResizer()
  const cma: any = useCMA()
  const sdk: SidebarAppSDK = useSDK()
  const contentTypeId = sdk.entry.getSys().contentType.sys.id
  const [loading, setLoading] = useState(false)
  const [entries, setEntries] = useState([])
  const [selectedLocale, setSelectedLocale] = useState('fr-CA')
  const [localeEntries, setLocaleEntries] = useState([])

  useEffect(() => {
    const fetchLocaleEntries = async () => {
      const res: any = await cma.locale.getMany()
      setLocaleEntries(res.items.map((elem: any) => elem.code))
    }

    const fetchEntries = () => {
      Object.entries(sdk.entry.fields).forEach(([key, value]) => {
        setEntries((elem) => [...elem, { key, value }])
      })
    }

    fetchLocaleEntries()
    fetchEntries()
  }, [])

  useEffect(() => {
    const tags = sdk.entry?.getMetadata()?.tags

    const tagsArray = tags?.filter((elem: any) => pattern.test(elem.sys.id))

    // const locales = localeEntries.filter((lc) => lc !== defaultLocale);

    if (!tagsArray || tagsArray.length === 0) return

    for (const tag of tagsArray) {
      let locale = localeForDomains[tag.sys.id]
      if (locale) {
        setSelectedLocale(locale)
      }
    }
  }, [])

  /**
   * Translates content and updates Contentful entry field values
   *
   * @param locale - Target locale code (e.g., 'fr-CA', 'de-DE')
   * @param data - Array of text content to translate
   * @param key - Contentful field ID to update
   * @param type - Field type ('Object', 'Array', or text)
   * @param defaultData - Original field data structure for Object types
   * @returns Promise<boolean> - Success status of translation and field update
   *
   * Process:
   * 1. Filters out empty content from data array
   * 2. Applies symbol replacement to prevent translation API issues
   * 3. Sends translation request to external API with authentication headers
   * 4. Processes response and reverses symbol replacements
   * 5. Updates Contentful field based on field type:
   *    - Object: Reconstructs object structure with translated text
   *    - Array: Sets translated array directly
   *    - Text: Joins translated array into single string
   * 6. Uses Contentful SDK to persist changes to entry
   */
  const translateAndSetValue: Function = async (
    locale: string,
    data: any,
    key: string,
    type: string,
    defaultData: any
  ) => {
    if (_.isEmpty(data)) {
      return true
    }

    const postData = data.filter((elem: string) => !_.isEmpty(elem))

    let checkData =
      !_.isEmpty(postData) &&
      postData.map((elem: string) => replaceSymbols(elem))
    if (_.isEmpty(checkData)) {
      return true
    }

    const response = await axios.post(
      url,
      {
        source_language: defaultLocaleCode,
        target_language: locale.split('-')[0],
        text_array: checkData,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'x-vercel-protection-bypass': `${ENV_VARIABLES.vercelByPassSecret}`,
        },
      }
    )
    if (!response) return false
    const translatedData = response.data.message.map((elem: any) =>
      reverseSymbols(elem.translatedText)
    )
    if (type == 'Object') {
      const translatedDataResult = setObject(defaultData, translatedData)
      sdk.entry.fields[key].setValue(translatedDataResult, locale)
    } else if (type == 'Array') {
      sdk.entry.fields[key].setValue(translatedData, locale)
    } else {
      sdk.entry.fields[key].setValue(translatedData.join(''), locale)
    }

    return true
  }

  const handleOnclick = async () => {
    try {
      setLoading(true)

      if (selectedLocale == '') {
        return
      }

      const allFields = await getEnFieldData(sdk.ids.entry)
      let res
      for (const { key, value } of entries) {
        // let locale = selectedLocale;
        const data = allFields?.fields?.[key]
        if (
          value.locales.includes(selectedLocale) &&
          !ignoreKeys.includes(key)
        ) {
          if (value.type === 'Object') {
            if (!_.isEmpty(data)) {
              const result = getObject(data)

              res = await translateAndSetValue(
                selectedLocale,
                result,
                key,
                value.type,
                data
              )
            }
          } else if (value.type === 'Array') {
            if (!_.isEmpty(data)) {
              res = await translateAndSetValue(
                selectedLocale,
                data,
                key,
                value.type,
                data
              )
            }
          } else {
            if (!_.isEmpty(data)) {
              const textArray = [data]
              const keysArray = [key]
              res = await translateAndSetValue(
                selectedLocale,
                textArray,
                keysArray[0],
                value.type,
                data
              )
            }
          }
        }
      }

      if (res) {
        let x: { [key: string]: string | boolean } =
          sdk.entry.fields?.['configurations']?.getValue()
        x = {
          ...x,
          isTranslated: true,
        }
        sdk.entry.fields?.['configurations']?.setValue(x)
        sdk.entry.save()
      }
      setLoading(false)

      // }
      // window.location.reload();
    } catch (error: any) {
      setLoading(false)
      console.error('Error translating text:', error)
    }
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      {/*
        Translation Component - Legacy translation interface for Contentful sidebar
        - Provides quick translation functionality for individual entries
        - Supports multiple target languages (French, German, Italian, Spanish, Dutch)
        - Automatically detects domain-specific default locale from entry metadata tags
        - Processes different field types (Object, Array, Text) with appropriate handling
        - Integrates with external translation API service
        - Handles symbol replacement to prevent translation API issues
        - Updates Contentful entry fields directly via SDK
        - Marks entries as translated in configurations field
        - Excludes certain fields from translation (slug, htmlAttr, htmlAttributes)
        - Provides loading states and error handling
        - Note: This is a simplified version compared to the full Translation component in PageSettings
      */}
      <div>
        <Paragraph>Click to Translate V3.3.0</Paragraph>
        <div className='flex gap-2'>
          <Select
            className='flex-1'
            onChange={(e) => setSelectedLocale(e.target.value)}
            value={selectedLocale}
          >
            <option value={'fr-CA'}>French</option>
            <option value={'de-DE'}>German</option>
            <option value={'it'}>Italian</option>
            <option value={'es'}>Spanish</option>
            <option value={'nl'}>Dutch</option>
          </Select>
          <Button onClick={handleOnclick} isDisabled={loading}>
            {loading ? 'Loading' : 'Translate'}
          </Button>
        </div>
      </div>
      {/*
        Cache Component - Provides cache invalidation functionality for page content
        - Only rendered for 'page' content types
        - Allows users to flush/revalidate cached content on Vercel deployment branches
        - Supports multiple domains (Altus Group, Finance Active, Verifino, etc.)
        - Handles locale-specific cache invalidation for multilingual content
        - Provides separate flush options for main preview branch and production branch
        - Automatically detects domain from entry metadata tags
        - Shows loading states and success/error feedback
        - Fetches entry data for all supported locales and generates appropriate slugs
        - Special handling for French Canadian locale on Altus Group domain (/fr suffix)
      */}
      {contentTypeId === 'page' && <Cache sdk={sdk} />}

      {/*
        PreviewUrls Component - Provides quick access to preview page content on different deployment branches
        - Only rendered for 'page' content types
        - Generates preview URLs for both staging and production environments
        - Automatically detects domain from entry metadata tags or configurations field
        - Monitors entry publication status to enable/disable production preview
        - Listens for real-time changes to entry fields and metadata
        - Opens preview URLs in new browser tabs
        - Shows appropriate error messages when domain is not configured
        - Supports multiple deployment branches (main preview branch vs production branch)
        - Handles domain-specific URL generation for different brands/websites
        - Provides user-friendly branch names in button labels
      */}
      {contentTypeId === 'page' && <PreviewUrls sdk={sdk} />}
    </div>
  )
}

export default Sidebar
