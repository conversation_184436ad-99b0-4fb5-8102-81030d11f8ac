import { SidebarAppSDK } from '@contentful/app-sdk'
import { <PERSON><PERSON>, Paragraph, Select } from '@contentful/f36-components'
import { useAutoResizer, useCMA, useSDK } from '@contentful/react-apps-toolkit'
import axios from 'axios'
import _ from 'lodash'
import React, { useEffect, useState } from 'react'
import Cache from '../components/PageSettings/components/Cache'
import {
  getEnFieldData,
  getObject,
  replaceSymbols,
  reverseSymbols,
  setObject,
} from '../components/PageSettings/components/Translations/utils'
import PreviewUrls from '../components/Preview'
import { ENV_VARIABLES } from '../constant/variables'

const ignoreKeys = ['slug']
const defaultLocale = 'en-CA'
const defaultLocaleCode = defaultLocale.split('-')[0]
const url = `${ENV_VARIABLES.appBaseURL}/api/translate-text/`
const pattern = /domain/i
const localeForDomains = {
  domainAltusGroupCom: 'fr-CA',
  domainVerifnoCom: 'de-DE',
  domainFinanceActiveCom: 'fr-CA',
}

const Sidebar = () => {
  useAutoResizer()
  const cma: any = useCMA()
  const sdk: SidebarAppSDK = useSDK()
  const contentTypeId = sdk.entry.getSys().contentType.sys.id
  const [loading, setLoading] = useState(false)
  const [entries, setEntries] = useState([])
  const [selectedLocale, setSelectedLocale] = useState('fr-CA')
  const [localeEntries, setLocaleEntries] = useState([])

  useEffect(() => {
    const fetchLocaleEntries = async () => {
      const res: any = await cma.locale.getMany()
      setLocaleEntries(res.items.map((elem: any) => elem.code))
    }

    const fetchEntries = () => {
      Object.entries(sdk.entry.fields).forEach(([key, value]) => {
        setEntries((elem) => [...elem, { key, value }])
      })
    }

    fetchLocaleEntries()
    fetchEntries()
  }, [])

  useEffect(() => {
    const tags = sdk.entry?.getMetadata()?.tags

    const tagsArray = tags?.filter((elem: any) => pattern.test(elem.sys.id))

    // const locales = localeEntries.filter((lc) => lc !== defaultLocale);

    if (!tagsArray || tagsArray.length === 0) return

    for (const tag of tagsArray) {
      let locale = localeForDomains[tag.sys.id]
      if (locale) {
        setSelectedLocale(locale)
      }
    }
  }, [])

  const translateAndSetValue: Function = async (
    locale: string,
    data: any,
    key: string,
    type: string,
    defaultData: any
  ) => {
    if (_.isEmpty(data)) {
      return true
    }

    const postData = data.filter((elem: string) => !_.isEmpty(elem))

    let checkData =
      !_.isEmpty(postData) &&
      postData.map((elem: string) => replaceSymbols(elem))
    if (_.isEmpty(checkData)) {
      return true
    }

    const response = await axios.post(
      url,
      {
        source_language: defaultLocaleCode,
        target_language: locale.split('-')[0],
        text_array: checkData,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'x-vercel-protection-bypass': `${ENV_VARIABLES.vercelByPassSecret}`,
        },
      }
    )
    if (!response) return false
    const translatedData = response.data.message.map((elem: any) =>
      reverseSymbols(elem.translatedText)
    )
    if (type == 'Object') {
      const translatedDataResult = setObject(defaultData, translatedData)
      sdk.entry.fields[key].setValue(translatedDataResult, locale)
    } else if (type == 'Array') {
      sdk.entry.fields[key].setValue(translatedData, locale)
    } else {
      sdk.entry.fields[key].setValue(translatedData.join(''), locale)
    }

    return true
  }

  const handleOnclick = async () => {
    try {
      setLoading(true)

      if (selectedLocale == '') {
        return
      }

      const allFields = await getEnFieldData(sdk.ids.entry)
      let res
      for (const { key, value } of entries) {
        // let locale = selectedLocale;
        const data = allFields?.fields?.[key]
        if (
          value.locales.includes(selectedLocale) &&
          !ignoreKeys.includes(key)
        ) {
          if (value.type === 'Object') {
            if (!_.isEmpty(data)) {
              const result = getObject(data)

              res = await translateAndSetValue(
                selectedLocale,
                result,
                key,
                value.type,
                data
              )
            }
          } else if (value.type === 'Array') {
            if (!_.isEmpty(data)) {
              res = await translateAndSetValue(
                selectedLocale,
                data,
                key,
                value.type,
                data
              )
            }
          } else {
            if (!_.isEmpty(data)) {
              const textArray = [data]
              const keysArray = [key]
              res = await translateAndSetValue(
                selectedLocale,
                textArray,
                keysArray[0],
                value.type,
                data
              )
            }
          }
        }
      }

      if (res) {
        let x: { [key: string]: string | boolean } =
          sdk.entry.fields?.['configurations']?.getValue()
        x = {
          ...x,
          isTranslated: true,
        }
        sdk.entry.fields?.['configurations']?.setValue(x)
        sdk.entry.save()
      }
      setLoading(false)

      // }
      // window.location.reload();
    } catch (error: any) {
      setLoading(false)
      console.error('Error translating text:', error)
    }
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div>
        <Paragraph>Click to Translate V3.3.0</Paragraph>
        <div className='flex gap-2'>
          <Select
            className='flex-1'
            onChange={(e) => setSelectedLocale(e.target.value)}
            value={selectedLocale}
          >
            <option value={'fr-CA'}>French</option>
            <option value={'de-DE'}>German</option>
            <option value={'it'}>Italian</option>
            <option value={'es'}>Spanish</option>
            <option value={'nl'}>Dutch</option>
          </Select>
          <Button onClick={handleOnclick} isDisabled={loading}>
            {loading ? 'Loading' : 'Translate'}
          </Button>
        </div>
      </div>
      {contentTypeId === 'page' && <Cache sdk={sdk} />}
      {contentTypeId === 'page' && <PreviewUrls sdk={sdk} />}
    </div>
  )
}

export default Sidebar
