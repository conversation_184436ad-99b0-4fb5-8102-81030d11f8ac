import { HomeAppSDK } from '@contentful/app-sdk'
import { useSDK } from '@contentful/react-apps-toolkit'
import React, { useContext } from 'react'
import Dashboard from '../components/Dashboard'
import { GlobalContext } from '../contexts/globalContext'

const Home = () => {
  const sdk = useSDK<HomeAppSDK>()

  const { isConfigSettingsModalOpen, setIsConfigSettingsModalOpen } =
    useContext(GlobalContext)
  /*
   To use the cma, inject it as follows.
   If it is not needed, you can remove the next line.
   */
  // const cma = useCMA();

  return <Dashboard sdk={sdk}/>

  //return (
  //  <Box
  //    style={{
  //      padding: '24px',
  //      display: 'flex',
  //      flexDirection: 'column',
  //      gap: '24px',
  //      height: '100vh',
  //      width: '100%',
  //      justifyContent: 'center',
  //      alignItems: 'center',
  //    }}
  //  >
  //    <img
  //      src={logo}
  //      style={{
  //        cursor: 'pointer',
  //      }}
  //      alt=''
  //      onClick={() => setIsConfigSettingsModalOpen(true)}
  //    />
  //    {/* <Button
  //     variant='primary'
  //     onClick={() => setIsConfigSettingsModalOpen(true)}
  //     endIcon={<RightArrow colour='#FFF' />}
  //   >
  //     Launch Configurator
  //   </Button> */}
  //    {isConfigSettingsModalOpen && (
  //      <ConfigModal
  //        sdk={sdk}
  //        onClose={() => setIsConfigSettingsModalOpen(false)}
  //      />
  //    )}
  //  </Box>
  //)
}

export default Home
