import React, { useEffect } from 'react';
import { Paragraph } from '@contentful/f36-components';
import { /* useCMA, */ useSDK } from '@contentful/react-apps-toolkit';
import { EditorAppSDK } from '@contentful/app-sdk';

const Entry = () => {
  const sdk = useSDK<EditorAppSDK>();
  /*
     To use the cma, inject it as follows.
     If it is not needed, you can remove the next line.
  */
  // const cma = useCMA();

  useEffect(()=>{
    sdk.entry.fields["title"].setValue("test 1234")
  },[])

  console.log(sdk.contentType.fields);
  



  return <Paragraph>Hello Entry Editor Component (AppId: {sdk.ids.app})</Paragraph>;
};

export default Entry;
