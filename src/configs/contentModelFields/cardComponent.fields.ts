// const cardCommonFields = []

export const cardComponentFields = {
  CalloutInfo: [
    'heading',
    'description',
    'button',
    'isLightMode',
    'image',
    'htmlAttributes',
  ],
  CalloutIcon: [
    'heading',
    'description',
    'button',
    'icon',
    'size',
    'isLightMode',
    'image',
    'htmlAttributes',
  ],
  StatsCard: [
    'statsText',
    'symbolSuffix',
    'symbolPrefix',
    'description',
    'htmlAttributes',
  ],
  CardIcon: [
    'heading',
    'description',
    'icon',
    'size',
    'isLightMode',
    'htmlAttributes',
  ],
  CardImage: [
    'heading',
    'description',
    'image',
    'button',
    'alignment',
    'size',
    'orientation',
    'htmlAttributes',
  ],
  CardLogoPortrait: [
    'heading',
    'description',
    'image',
    'isLightMode',
    'htmlAttributes',
  ],
  CardMegaMenu: ['heading', 'description'],
  CardFeaturedRowsList: [
    'heading',
    'number',
    'button',
    'startTime',
    'isLightMode',
  ],
  CardGenericMedium: [
    'image',
    'heading',
    'description',
    'button',
    'isLightMode',
    'startTime',
    'htmlAttributes',
  ],
  CardGenericSmall: [
    'image',
    'heading',
    'button',
    'isLightMode',
    'startTime',
    'htmlAttributes',
  ],
  CardGenericNoImage: [
    'heading',
    'description',
    'button',
    'isLightMode',
    'startTime',
    'htmlAttributes',
  ],
  CardGenericHeadline: [
    'heading',
    'isLightMode',
    'button',
    'startTime',
    'isLightMode',
    'htmlAttributes',
  ],
  CardGenericFeaturedList: [
    'heading',
    'isLightMode',
    'startTime',
    'htmlAttributes',
  ],
  CardGenericMegamenu: [
    'heading',
    'button',
    'image',
    'startTime',
    'isLightMode',
    'htmlAttributes',
  ],
  CardGenericSearch: ['heading', 'description', 'image', 'isLightMode'],
  CardGenericSearchFeatured: [
    'heading',
    'description',
    'button',
    'image',
    'isLightMode',
    'startTime',
    'htmlAttributes',
  ],
  CardGenericLarge: [
    'image',
    'heading',
    'description',
    'button',
    'isLightMode',
    'startTime',
    'htmlAttributes',
  ],
  CardFeaturedPortrait: [
    'image',
    'subHeading',
    'heading',
    'button',
    'isLightMode',
    'description',
    'orientation',
  ],
  CardFeaturedLandscape: [
    'image',
    'subHeading',
    'heading',
    'button',
    'isLightMode',
    'description',
    'orientation',
  ],
  CardFeaturedPortraitDetailed: [
    'subHeading',
    'heading',
    'description',
    'image',
    'button',
    'isLightMode',
    'orientation',
  ],
  CardComparison: [
    'subHeading',
    // 'heading',
    'description',
    'checklist',
    'button',
    'isLightMode',
    'htmlAttributes'
  ],
  CardComparisonIcon: [
    'subHeading',
    // 'heading',
    'description',
    'checklist',
    'button',
    'image',
    'size',
    'orientation',
    'isLightMode',
    'htmlAttributes'
  ],
  CardLiveEventWebinar: [
    'subHeading',
    'heading',
    'startTime',
    'endTime',
    'timeZone',
    'address',
    'description',
    'button',
    'buttonGroup',
    'image',
  ],
  CardOnDemandWebinar: [
    'subHeading',
    'heading',
    'eventType',
    'description',
    'button',
    'buttonGroup',
    'image',
  ],
  CardPeoplePortrait: [
    'image',
    'fullName',
    'jobTitle',
    'companyName',
    'tags',
    'description',
    'button',
    'isLightMode',
    'orientation',
    'htmlAttributes',
  ],
  CardPeopleLandscape: [
    'image',
    'fullName',
    'jobTitle',
    'companyName',
    'tags',
    'description',
    'button',
    'isLightMode',
    'orientation',
    'htmlAttributes',
  ],
  CardPeopleSmall: [
    'image',
    'fullName',
    'jobTitle',
    'button',
    'isLightMode',
    'orientation',
    'htmlAttributes',
  ],
  CardPeopleLandscapeRow: [
    'image',
    'fullName',
    'jobTitle',
    'button',
    'companyName',
    'tags',
    'description',
    'isLightMode',
    'htmlAttributes',
  ],
  ArticleListItem: [
    'subHeading',
    'heading',
    'description',
    'externalLink',
    'openInNewTab',
    'button',
    'publishDate',
    'image',
    'isLightMode',
    'subheadingExternalLink',
  ],
  'Featured List': [
    'number',
    'heading',
    'isLightMode',
    'startTime',
    'htmlAttributes',
  ],
  CardLandscapeHome: ['heading', 'description', 'image', 'cards'],
  CardImageHome: ['heading', 'image', 'alignment', 'size'],
  CardAward: ['description', 'htmlAttributes'],
  CardFeatured2: ['description', 'image', 'orientation']
}
