export const formFields = {
  Inline: [
    'sfmcUrl',
    'header',
    'formFields',
    'hiddenFields',
    'footer',
    'loadingMessage',
    'successMessage',
    'errorMessage',
    'thankYouMessage',
    'postSuccessMessage',
    'htmlAttr',
    'formCategory',
    'isDynamicAgreement'
  ],
  Popup: [
    'sfmcUrl',
    'header',
    'formFields',
    'hiddenFields',
    'footer',
    'loadingMessage',
    'successMessage',
    'errorMessage',
    'thankYouMessage',
    'htmlAttr',
    'formCategory',
    'isDynamicAgreement'
  ],
  Floating: [
    'sfmcUrl',
    'header',
    'formFields',
    'hiddenFields',
    'footer',
    'displayAt',
    'backgroundImage',
    'loadingMessage',
    'successMessage',
    'errorMessage',
    'thankYouMessage',
    'isStatic',
    'isLightMode',
    'isBingEnabled',
    'displayInternalLink',
    'postSuccessMessage',
    'displayPostAction',
    'htmlAttr',
    'formCategory'
  ],
  FloatingComponent: [
    'sfmcUrl',
    'header',
    'formFields',
    'footer',
    'displayAt',
    'backgroundImage',
    'loadingMessage',
    'successMessage',
    'errorMessage',
    'thankYouMessage',
    'isStatic',
    'isLightMode',
    'isBingEnabled',
    'displayInternalLink',
    'postSuccessMessage',
    'displayPostAction',
    'htmlAttr',
    'formCategory'
  ],
  'Floating - Home': [
    'sfmcUrl',
    'header',
    'formFields',
    'hiddenFields',
    'footer',
    'displayAt',
    'backgroundImage',
    'loadingMessage',
    'successMessage',
    'errorMessage',
    'thankYouMessage',
    'isStatic',
    'isLightMode',
    'isBingEnabled',
    'displayInternalLink',
    'postSuccessMessage',
    'displayPostAction',
    'htmlAttr',
    'formCategory'
  ],
  MultiStep: [
    'sfmcUrl',
    'header',
    'formFields',
    'hiddenFields',
    'footer',
    'loadingMessage',
    'successMessage',
    'errorMessage',
    'thankYouMessage',
    'postSuccessMessage',
    'htmlAttr',
    'formCategory',
    'isDynamicAgreement'
  ],
  ReonomyFreeTrial: [
    'sfmcUrl',
    'header',
    'formFields',
    'hiddenFields',
    'footer',
    'loadingMessage',
    'successMessage',
    'errorMessage',
    'thankYouMessage',
    'htmlAttr',
    'formCategory'
  ],
  FormHero: [
    'sfmcUrl',
    'header',
    'formFields',
    'footer',
    'loadingMessage',
    'successMessage',
    'errorMessage',
    'thankYouMessage',
    'isLightMode',
    'isBingEnabled',
    'displayInternalLink',
    'postSuccessMessage',
    'displayPostAction',
    'formCategory'
  ],
  ExitIntent: [
    'sfmcUrl',
    'header',
    'formFields',
    'loadingMessage',
    'errorMessage',
    'thankYouMessage',
    'htmlAttr',
    'formCategory'
  ],
}
