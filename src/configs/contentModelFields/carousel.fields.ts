export const carouselFields = {
  CarouselGeneric: [
    'subheading',
    'heading',
    'button',
    'carouselItems',
    'timer',
    'isLightMode',
    'htmlAttr',
  ],
  CarouselHeroHorizontal: [
    'carouselItems',
    'tabHeadings',
    'timer',
    'heading',
    'htmlAttr',
  ],
  CarouselHeroVertical: ['carouselItems', 'timer', 'heading', 'htmlAttr'],
  CarouselTwoColGeneric: ['carouselItems', 'timer', 'heading', 'htmlAttr'],
  CarouselTwoColHeadline: ['carouselItems', 'timer', 'heading', 'htmlAttr'],
  CarouselTwoColLogo: ['carouselItems', 'timer', 'heading', 'htmlAttr'],
  CarouselHome: ['carouselItems', 'heading', 'button', 'htmlAttr'],
  CarouselTabBox: ['carouselItems', 'htmlAttr'],
  CarouselGeneric2: [
    'subheading',
    'heading',
    'description',
    'button',
    'carouselItems',
    'isLightMode',
    'isFullBleed',
    'timer',
    'htmlAttr',
  ],
  CarouselImageVideo: [
    'subheading',
    'heading',
    'description',
    'button',
    'carouselItems',
    'isLightMode',
    'isCenterItemHighlighted',
    'htmlAttr',
  ],
  CarouselTabs: [
    'carouselItems',
    'tabHeadings',
  ],
  'Carousel 2.O': [
    'subheading',
    'description',
    'carouselItems',
    'isLightMode',
    'carouselFormat',
    'htmlAttr',
  ]
}
