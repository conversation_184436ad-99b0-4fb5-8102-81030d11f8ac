interface HostedFile {
  id: string
  name: string
  source: string
  assetId: string
  destination: string
  createdAt?: string
  updatedAt?: string
  extension?: string
  isActive?: boolean
  isLegacy?: boolean
  fileId?: string
}

interface HostedFilePayload {
  data: HostedFile[]
  id: null | string
}

interface HostedFilesState {
  dataSource: HostedFile[]
  id: null | string
  loading: boolean
  error?: string
}
