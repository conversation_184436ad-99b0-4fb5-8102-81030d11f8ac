import { configureStore } from '@reduxjs/toolkit'
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'
import dvDashboard from './slices/dashboard/dvSlices'
import hostedFiles from './slices/dashboard/hostedFiles'
import tableData from './slices/dataTables/index'
import pageData from './slices/pageSettings/index'
import notificationData from './slices/dashboard/notificationsSlice'

const store = configureStore({
  reducer: { dvDashboard, pageData, hostedFiles, tableData, notificationData },
})

export type AppDispatch = typeof store.dispatch
export const useAppDispatch: () => AppDispatch = useDispatch // Export a hook that can be reused to resolve types

export type AppSelectState = ReturnType<typeof store.getState>
export const useAppSelector: TypedUseSelectorHook<AppSelectState> = useSelector

export type RootState = ReturnType<typeof store.getState>
export default store
