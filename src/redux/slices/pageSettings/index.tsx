import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { fetchGraphQLQuery } from '../../../components/Crosspost/helpers'
import { getConfigurationsCollectionQuery } from '../../../globals/queries'
import { CrosspostJson } from '../../../globals/types'
import {
  categorizeTags,
  getAllContentTypes,
  getAllTags,
  getEntryDataById,
  updateEntryTags,
} from '../../../globals/utils'

const initialState = {
  pageData: {} as any,
  isLoading: false,
  categorizedTags: {},
  nonCategorizedTags: [],
  tagsUpdateLoading: false,
  crossPostData: {} as CrosspostJson,
  contentTypes: [],
  isPageSettingsModalClosable: true,
}

export const fetchPageDataById = createAsyncThunk(
  'pageSettings/fetchPageDataById',
  async (id: string) => {
    const res = await getEntryDataById(id)
    return res
  }
)

export const getAllCategorizedTags = createAsyncThunk(
  'pageSettings/getAllCategorizedTags',
  async () => {
    const res = await getAllTags()

    const categorizedTags = await categorizeTags(res)

    return { res, categorizedTags }
  }
)

export const updateTags = createAsyncThunk(
  'pageSettings/updateEntryTags',
  async ({ newTags, entryId }: { newTags: string[]; entryId: string }) => {
    const res = await updateEntryTags(newTags, entryId)

    return res
  }
)

export const fetchCrossPostData = createAsyncThunk(
  'pageSettings/fetchCrossPostData',
  async ({
    id,
    envId,
    spaceId,
  }: {
    id: string
    spaceId: string
    envId: string
  }) => {
    const response = await fetchGraphQLQuery(
      getConfigurationsCollectionQuery(),
      spaceId,
      envId
    ).then((res: any) => res?.data?.configurationsCollection?.items)

    const matchedData = response?.find((item: any) => {
      return item.type === 'Crosspost' && item?.scope === 'MSA'
    })?.data?.json?.content?.[0]?.content?.[0]?.value

    if (!matchedData) return

    const parsedData = JSON.parse(matchedData)

    return parsedData?.[id] || {}
  }
)

export const getContentTypesForEnvironment = createAsyncThunk(
  'pageSettings/getContentTypesForEnvironment',

  async () => {
    const res = await getAllContentTypes()

    return res
  }
)

const pageSettingsSlice = createSlice({
  name: 'pageSettings',
  initialState,
  reducers: {
    setPageData: (state, { payload }) => {
      state.pageData = payload
    },

    setIsPageSettingsModalClosable: (state, { payload }) => {
      state.isPageSettingsModalClosable = payload
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchPageDataById.pending, (state) => {
      state.isLoading = true
    })

    builder.addCase(fetchPageDataById.fulfilled, (state, { payload }: any) => {
      state.pageData = payload
      state.isLoading = false
    })

    builder.addCase(fetchPageDataById.rejected, (state) => {
      state.isLoading = false
    })

    builder.addCase(
      getAllCategorizedTags.fulfilled,
      (state, { payload }: any) => {
        state.categorizedTags = payload.categorizedTags
        state.nonCategorizedTags = payload.res
      }
    )

    builder.addCase(updateTags.pending, (state) => {
      state.tagsUpdateLoading = true
    })

    builder.addCase(updateTags.fulfilled, (state) => {
      state.tagsUpdateLoading = false
    })

    builder.addCase(updateTags.rejected, (state) => {
      state.tagsUpdateLoading = false
    })

    builder.addCase(fetchCrossPostData.fulfilled, (state, { payload }: any) => {
      state.crossPostData = payload
    })

    builder.addCase(
      getContentTypesForEnvironment.fulfilled,
      (state, { payload }: any) => {
        state.contentTypes = payload
      }
    )
  },
})

export default pageSettingsSlice.reducer

export const { setPageData, setIsPageSettingsModalClosable } =
  pageSettingsSlice.actions
