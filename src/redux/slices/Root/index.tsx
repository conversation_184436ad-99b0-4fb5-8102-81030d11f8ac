import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  envId: '',
  spaceId: '',
}

export const rootSlice = createSlice({
  name: 'root',
  initialState,
  reducers: {
    setEnvId: (state, action) => {
      state.envId = action.payload
    },
    setSpaceId: (state, action) => {
      state.spaceId = action.payload
    },
  },
})

export const { setEnvId, setSpaceId } = rootSlice.actions

export default rootSlice.reducer
