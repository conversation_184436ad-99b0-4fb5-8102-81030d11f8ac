import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { Asset } from 'contentful-management'
import {
  CreateNewConfiguration,
  UpdateCurrentConfiguration,
} from '../../../components/Dashboard/DataVisualization/Utils'
import {
  getAssetData,
  getFieldsData,
  templateName,
} from '../../../components/DataVisualization/utils'
import {
  fetchGlobalConfigurationData,
  fetchMSAConfigurationData,
} from '../../../globals/utils'

const initialState = {
  contentId: '',
  globalConfigData: {} as any,
  msaConfigData: {} as any,
  dvTemplateJsonData: {} as any,
  dvTemplate: undefined as templateName,
  dvAsset: undefined as Asset | undefined,
  dvUpdatedData: {} as any,
  dvChartId: '',
}

export const fetchGlobalConfigData = createAsyncThunk(
  'dashboard-dv/fetchGlobalConfigData',
  async () => {
    const res = await fetchGlobalConfigurationData()

    return res
  }
)

export const createNewGlobalConfig = createAsyncThunk(
  'dashboard-dv/createNewGlobalConfig',
  async (payload: any) => {
    const res = await CreateNewConfiguration(
      payload,
      'Global',
      'Configuration Global'
    )

    return { id: res?.sys?.id, data: payload }
  }
)

export const updateGlobalConfigData = createAsyncThunk(
  'dashboard-dv/updateGlobalConfigData',
  async ({ contentId, payload }: any) => {
    await UpdateCurrentConfiguration(contentId, payload)

    return { id: contentId, data: payload }
  }
)

export const fetchMSAConfigData = createAsyncThunk(
  'dashboard-dv/fetchMSAConfigData',
  async (domain: string) => {
    const res = await fetchMSAConfigurationData(domain)

    return res
  }
)

export const fetchDVTemplateData = createAsyncThunk(
  'dashboard-dv/fetchDVTemplateData',
  async (params: { entryId: string; currentLocale: string }) => {
    const { entryId, currentLocale } = params
    const entryData = await getFieldsData(entryId)
    if (!entryData) {
      throw new Error('Entry data not found')
    }
    const dvChartId = entryId
    const dvTemplate = entryData.template?.[currentLocale]
    const value =
      entryData.data?.[currentLocale]?.content?.[0]?.content?.[0]?.value
    const dvUpdatedData = value && JSON.parse(value)
    const sysId = entryData?.source?.[currentLocale]?.sys?.id
    const dvAsset =
      entryData?.source && (await getAssetData(currentLocale, entryId, sysId))

    return {
      dvChartId,
      dvTemplate,
      dvUpdatedData,
      dvAsset,
    }
  }
)

const dvSlice = createSlice({
  name: 'dashboard-dv',
  initialState,
  reducers: {
    setGlobalConfigContentId: (state, { payload }: any) => {
      state.contentId = payload
    },

    setGlobalConfigData: (state, { payload }: any) => {
      state.globalConfigData = payload
    },
    setDVTemplate: (
      state,
      { payload }: { payload: templateName | undefined }
    ) => {
      state.dvTemplate = payload
    },
    setDVChartId: (state, { payload }: { payload: string }) => {
      state.dvChartId = payload
    },
    setDVAsset: (state, { payload }: { payload: Asset | undefined }) => {
      state.dvAsset = payload
    },
    setDVUpdatedData: (state, { payload }: any) => {
      state.dvUpdatedData = payload
    },
  },
  extraReducers: (builder) => {
    builder.addCase(
      fetchGlobalConfigData.fulfilled,
      (state, { payload }: any) => {
        state.contentId = payload?.contentId
        state.globalConfigData = payload?.data
      }
    )

    builder.addCase(
      createNewGlobalConfig.fulfilled,
      (state, { payload }: any) => {
        state.contentId = payload?.id
        state.globalConfigData = payload?.data
      }
    )

    builder.addCase(
      updateGlobalConfigData.fulfilled,
      (state, { payload }: any) => {
        state.contentId = payload?.id
        state.globalConfigData = payload?.data
      }
    )

    builder.addCase(fetchMSAConfigData.fulfilled, (state, { payload }: any) => {
      state.contentId = payload?.contentId
      state.msaConfigData = payload?.data
    })

    builder.addCase(
      fetchDVTemplateData.fulfilled,
      (state, { payload }: any) => {
        state.dvChartId = payload.dvChartId
        state.dvTemplate = payload.dvTemplate
        state.dvAsset = payload.dvAsset
        state.dvUpdatedData = payload.dvUpdatedData
      }
    )
  },
})

export default dvSlice.reducer

export const {
  setGlobalConfigContentId,
  setGlobalConfigData,
  setDVTemplate,
  setDVUpdatedData,
  setDVAsset,
  setDVChartId,
} = dvSlice.actions
