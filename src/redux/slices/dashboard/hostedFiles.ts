import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit'
import { getConfigurationByScopeAndType } from '../../../globals/utils'

// Define the data structure for a HostedFile
// Initial state
const initialState: HostedFilesState = {
  dataSource: [],
  id: null,
  loading: false,
}

// Fetch Hosted Files from an API (you can replace this with your actual API request logic)
export const fetchHostedFiles = createAsyncThunk(
  'hostedFiles/fetchHostedFiles',
  async (domain: string = 'MSA') => {
    const configurationData: any = await getConfigurationByScopeAndType(
      'Assets',
      domain
    )
    if (configurationData && configurationData?.data && configurationData?.id) {
      //   console.log(configurationData?.data ?? [])
      return configurationData
    }
  }
)

const hostedFilesSlice = createSlice({
  name: 'hostedFiles',
  initialState,
  reducers: {
    addHostedFile(state, action: PayloadAction<HostedFile>) {
      const { source } = action.payload
      const duplicate = state.dataSource.some((doc) => doc.source === source)

      if (duplicate) {
        state.error = `Source "${source}" already exists.`
      } else {
        state.error = undefined
        state.dataSource.unshift(action.payload)
      }
    },
    updateHostedFileState(state, action: PayloadAction<HostedFile[]>) {
      state.dataSource = [...action?.payload]
    },
    clearError(state) {
      state.error = undefined
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchHostedFiles.pending, (state) => {
        state.loading = true
        state.error = undefined
      })
      .addCase(
        fetchHostedFiles.fulfilled,
        (state, action: PayloadAction<HostedFilePayload>) => {
          state.loading = false
          state.dataSource = action.payload?.data ?? []
          state.id = action.payload?.id ?? null
        }
      )
      .addCase(fetchHostedFiles.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message
      })
  },
})
export const isSourceExists = (
  state: HostedFilesState,
  source: string
): boolean => {
  return state.dataSource.some((doc) => doc.source === source)
}

export const isUpdateTimeSourseExists = (
  state: HostedFilesState,
  id: string,
  source: string
): boolean => {
  const duplicate = state.dataSource.some(
    (doc) => doc.source === source && doc?.id !== id
  )
  return duplicate
}

export const isFileIdExists = (
  state: HostedFilesState,
  fileId: string
): boolean => {
  return state.dataSource.some((doc) => doc.fileId === fileId)
}

export const isUpdateTimeFileIdExists = (
  state: HostedFilesState,
  id: string,
  fileId: string
): boolean => {
  const duplicate = state.dataSource.some(
    (doc) => doc.fileId === fileId && doc?.id !== id
  )
  return duplicate
}

export const updateHostedFile = (
  state: HostedFilesState,
  payload: HostedFile
) => {
  const { source } = payload
  // Check if source already exists in the dataSource array
  const duplicate = state.dataSource.some(
    (doc) => doc.source === source && doc?.id !== payload?.id
  )
  if (duplicate) {
    // state.error = `Source "${source}" already exists.`;
    return state // Return early to avoid further processing
  }

  const index = state.dataSource?.findIndex((ext) => ext?.id === payload?.id)
  // console.log(index)

  if (index === -1) {
    // state.error = `Source "${source}" does not exist.`;
    return state // Return early if the index is not found
  } else {
    // Update dataSource immutably by creating a new array
    const newDataSource = [...state.dataSource]
    newDataSource[index] = payload // Update the document at the found index
    console.log(newDataSource)

    // state.error = undefined; // Reset error
    return { ...state, dataSource: newDataSource } // Return the updated state
  }
}
export const deleteHostedFile = (state: HostedFilesState, payload: string) => {
  const id = payload
  // const newData = {...state}
  const newDataSource = [...state.dataSource]
  const index = newDataSource.findIndex((doc) => doc.id === id)

  if (index === -1) {
    // state.error = `Source ID::: "${payload}" does not exist.`
  } else {
    // state.error = undefined
    newDataSource.splice(index, 1)
  }
  return { ...state, dataSource: newDataSource }
}
export const { addHostedFile, updateHostedFileState, clearError } =
  hostedFilesSlice.actions

export default hostedFilesSlice.reducer
