import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { getConfigurationByScopeAndType } from '../../../globals/utils'


const initialState = {
  dataSource: {} as any,
  id: null,
  loading: false,
  isAddNewNotification:false
}

export const fetchNotificationHistory = createAsyncThunk(
  'hostedFiles/fetchNotificationHistory',
  async (domain: string = 'MSA') => {
    const configurationData: any = await getConfigurationByScopeAndType(
      'Notifications',
      domain,
    )
    if (configurationData && configurationData?.data && configurationData?.id) {
      return configurationData
    }
  },
)

const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    setIsAddNewNotification: (state, action) => {
      state.isAddNewNotification = action.payload
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchNotificationHistory.pending, (state) => {
      state.loading = true
    })
    builder.addCase(
      fetchNotificationHistory.fulfilled,
      (state, action: any) => {
        state.loading = false
        state.dataSource = action.payload?.data ?? []
        state.id = action.payload?.id ?? null
      },
    )
    builder.addCase(fetchNotificationHistory.rejected, (state) => {
      state.loading = false
    })
  },
})

export const { setIsAddNewNotification } = notificationsSlice.actions

export default notificationsSlice.reducer