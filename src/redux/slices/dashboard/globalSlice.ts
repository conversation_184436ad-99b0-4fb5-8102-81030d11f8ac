import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { getContentTypeFieldById } from '../../../globals/utils'

const initialState = {
  dtsTemplate: [],
}

export const getDtsTemplates = createAsyncThunk(
  'dashboard-global/getDtsTemplates',
  async () => {
    const res = await getContentTypeFieldById({
      contentTypeId: 'componentDynamicTagging',
      fieldId: 'template',
    })

    return res?.validations?.[0]?.in || []
  }
)

const globalSlice = createSlice({
  name: 'dashboard-global',
  initialState,
  reducers: {},

  extraReducers: (builder) => {
    builder.addCase(getDtsTemplates.fulfilled, (state, action) => {
      state.dtsTemplate = action.payload as any
    })
  },
})

export default globalSlice.reducer
