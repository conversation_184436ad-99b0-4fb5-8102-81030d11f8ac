import { Box, FormControl, Tooltip } from '@contentful/f36-components'
import React from 'react'
import Info from '../../assets/icons/Info'
import './index.scss'

function FormControlComp({
  label,
  tooltip,
  children,
  isRequired = false,
  className,
}: {
  label?: string
  tooltip?: string
  children: React.ReactNode
  isRequired?: boolean
  className?: string
}) {
  return (
    <Box className={'w-full flex items-start justify-start'}>
      <FormControl
        className={'m-0 pl-0 w-full flex flex-col items-center' + className}
      >
        {label && (
          <div className='formLabelWithIcon'>
            <FormControl.Label className='mb-0 flex gap-1'>
              {label}

              {isRequired && (
                <span
                  style={{
                    color: 'red',
                    // paddingLeft: '0.3rem',
                  }}
                >
                  *
                </span>
              )}
              {tooltip && (
                <Tooltip placement='right' content={tooltip} className={"text-left"}>
                  <Info />
                </Tooltip>
              )}
            </FormControl.Label>
          </div>
        )}
        {children}
      </FormControl>
    </Box>
  )
}

export default FormControlComp
