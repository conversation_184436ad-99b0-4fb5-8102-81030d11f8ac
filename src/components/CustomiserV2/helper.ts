import { colorsValues } from '../ColorsInputV2/colorValues'

export type StyleObject = {
  Color?: string
  Highlight?: string
  Family?: string
  Size?: string
  'Heading type'?: string
}

export const findCodeNameByHex = (hexValue: string): string | undefined => {
  for (const category of colorsValues) {
    for (const color of category.colors) {
      // Check hex in primary/secondary/accent colors
      if ('hex' in color && color.hex === hexValue) {
        return color.codeName
      }

      // Check hex in gradient colors
      if ('gradientColors' in color) {
        const gradientMatch = color.gradientColors.find(
          (gradientColor) => gradientColor?.hex === hexValue
        )
        if (gradientMatch) {
          return color.codeName
        }
      }

      // Check hex in variants if available
      if ('variants' in color) {
        const variantMatch = color.variants.find(
          (variant) => variant?.hex === hexValue
        )
        if (variantMatch) {
          return variantMatch.codeName
        }
      }
    }
  }

  // Return undefined if no match is found
  return undefined
}

export const reorderSummaryObject = (obj: StyleObject): StyleObject => {
  const orderedKeys: (keyof StyleObject)[] = [
    'Color',
    'Highlight',
    'Family',
    'Size',
    'Heading type',
  ]

  const reorderedObj: StyleObject = {}

  orderedKeys.forEach((key) => {
    if (key in obj) reorderedObj[key] = obj[key]
  })

  return reorderedObj
}

export const KeyStringMapper = (key: string, cat: String) => {
  if (cat === 'Family') {
    switch (key) {
      case 'FName':
        return 'Name'
      case 'class':
        return 'Class Name'
      case 'type':
        return 'Fallback Fonts'
      default:
    }
  } else if (cat === 'Size') {
    switch (key) {
      case 'FName':
        return 'Size'
      case 'class':
        return 'Class Name'
      default:
    }
  } else if (cat === 'Heading type') {
    switch (key) {
      case 'label':
        return 'Name'
      case 'class':
        return 'Class Name'
      case 'size':
        return 'Size'
      default:
    }
  }
}
