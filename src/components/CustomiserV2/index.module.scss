@import '../../globals/styles/variables';

.customiserRoot {
  position: absolute;
  top: 0;
  width: 452px;
  // z-index: 2;
  border-radius: 5px;
  overflow: hidden;
  height: max-content;
  background-color: $cn8;
  box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;
}

.customiserFooter {
  display: flex;
  flex-direction: column;
}

.customiserFooterInner {
  display: flex;
  align-items: center;
  padding: 20px 25px;
  gap: 20px;
  width: 100%;

  .tags {
    color: #333;
    cursor: pointer;
    font-size: 12px;
    padding: 5px;

    &:hover {
      background: #e5e5e5;
      transition: all 0.25s ease;
      // border-radius: 5px;
    }
  }

  .active {
    background: #f2f2f2;
    outline: 1px dotted #001c99 !important;
    border-radius: 2px;
    transition: all 0.5s ease;
  }

  .active.tags {
    color: #001c99;
  }
}

.textPreview {
  cursor: pointer;
  padding: 0 20px;
  position: relative;
  //width: 100%;

  .text {
    width: 100%;
    position: relative;
    display: flex;
    padding: 5px;
    align-items: center;
    justify-content: space-between;
    transition: all 0.5s ease;
    gap: 10px;

    &:hover {
      background-color: $cn5;

      .tag {
        background-color: $cn8;
      }
    }
  }

  .active {
    background: $cn6;
    transition: all 0.25s ease;
    outline: 1px dashed #001c99;
    border-radius: 2px;
  }

  .active.text {
    //color: #001c99;
  }

  .tag {
    background-color: $cn5;
    color: $cn1;
    line-height: 10px;
    font-size: 10px;
    font-family: $fSansReg;
    text-transform: initial;
    padding: 2px 3px;
    min-width: fit-content;
    border-radius: 2px;
    letter-spacing: initial;
    display: flex;
    align-items: center;
    transition: all 0.5s ease;
  }

  &:first-child {
    padding-top: 10px;
  }

  &:last-child {
    padding-bottom: 10px;
  }
}

.tabsRoot {
  display: flex;
  background-color: $cn8;
  z-index: 3;
  position: relative;

  .tabs {
    cursor: pointer;
    padding: 10px;

    &:first-child {
      padding-left: 20px;
    }

    &:hover {
      &::after {
        content: '';
        width: calc(100% + 20px);
        height: 2px;
        left: -10px;
        display: block;
        position: relative;
        border-radius: 5px;
        bottom: -13px;
        background: linear-gradient(45deg, $cp2, $cs3);
      }
    }

    &::after {
      content: '';
      width: 0;
      transition: width 0.5s ease-in-out;
    }

    &.active {
      &::after {
        content: '';
        width: calc(100% + 20px);
        height: 2px;
        display: block;
        border-radius: 5px;
        left: -10px;
        position: relative;
        bottom: -13px;
        background: $cn1;
      }
    }
  }
}

.mainRoot {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(15deg, #fff 1%, #fcfcfc 70%, #f5f5f5);
  //gap: 10px;
}

.header,
.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 20px;
  background-color: $cn5;
}

.header {
  box-shadow: 0 -5px 50px -15px rgba(17, 12, 46, 0.15);
  font-size: $fs3;
  background: linear-gradient(30deg, #fff 1%, #fcfcfc 70%, #f5f5f5);
  border-radius: 5px 5px 0 0;

  &.closeIcon {
    &:hover svg {
      color: #e2222b;
    }
  }
}

.footer {
  background: linear-gradient(60deg, #fff 1%, #fcfcfc 70%, #f5f5f5);
  color: black;
  border-top: 1px solid #efefef;
  box-shadow: 0 -5px 50px -15px rgba(17, 12, 46, 0.15);
  padding: 0 0 0 20px;

  .footerInner {
    display: flex;
    align-items: center;
  }
}

.textEllipsis {
  text-overflow: ellipsis;
  text-wrap: nowrap;
  overflow: hidden;
  font-family: $fSansMed;
  width: 80%;

  span {
    font-family: $fSansBld;
  }
}

.textEllipsisText{
  text-overflow: ellipsis;
  text-wrap: nowrap;
  overflow: hidden;
}

.disabledState {
  cursor: not-allowed;

  svg{
    fill: #c6c6c6;
  }
}

.headingTextRoot{
  display: flex;
  align-items: center;
  gap: 8px;

  svg{
    cursor: initial;
  }

  .headingText{
  color: #e2222b;
  font-size: 12px;
}
}