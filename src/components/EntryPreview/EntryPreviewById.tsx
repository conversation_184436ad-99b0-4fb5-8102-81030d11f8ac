import { EntryCard, Flex, Spinner, Text } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { getEntryDataById } from '../../globals/utils'

type EntryPreviewProps = {
  entryId: string
}

const EntryPreviewById: React.FC<EntryPreviewProps> = ({ entryId }) => {
  const [entry, setEntry] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchEntry = async () => {
      try {
        setLoading(true)
        const fetchedEntry = await getEntryDataById(entryId)
        setEntry(fetchedEntry)
      } catch (err) {
        setError('Failed to load entry data.')
        console.error('Error fetching entry:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchEntry()
  }, [entryId])

  if (loading) {
    return (
      <Flex
        justifyContent='center'
        alignItems='center'
        style={{ height: '100%' }}
      >
        <Spinner />
      </Flex>
    )
  }

  if (error) {
    return <Text style={{ color: 'red' }}>{error}</Text>
  }

  if (!entry) {
    return <Text>No entry data available.</Text>
  }

  // Destructure relevant fields from the entry for the EntryCard
  const { sys, fields } = entry
  const internalName = fields?.internalName?.['en-CA'] || 'Unnamed Entry'
  const contentType = sys.contentType.sys.id

  return (
    <EntryCard
      title={internalName}
      contentType={contentType}
      description={`ID: ${sys.id}`}
      status={sys.publishedAt ? 'published' : 'draft'}
      onClick={() => console.log(`Entry ${sys.id} clicked`)} // Handle click if needed
    />
  )
}

export default EntryPreviewById
