import { EntryCard, MenuItem } from '@contentful/f36-components'
import React, { useEffect } from 'react'
import { fetchEntryDetails } from '../../Dashboard/Notifications/utils'

/**
 * A component that renders a card for a Contentful entry.
 *
 * @param {Object} props The component props.
 * @param {string} props.id The ID of the entry to be rendered.
 * @param {function} [props.onRemoveEntry] The function to be called when the user clicks the "Remove" button.
 *
 * @returns {React.ReactElement} The rendered component.
 */

function ContentEntryCard({
  id,
  onRemoveEntry,
}: {
  id: string
  onRemoveEntry?: any
}) {
  const [cardData, setCardData] = React.useState<any>({})

  const [isLoading, setIsLoading] = React.useState(false)

  const handleOnEdit = () => {
    let url = cardData.sys.urn.split(':::content:')[1]
    const final = 'https://app.contentful.com/' + url
    window.open(final, '_blank')
  }

  useEffect(() => {
    const fetchDataById = async (id: string) => {
      setIsLoading(true)
      const detailedEntry = await fetchEntryDetails(id)
      if (detailedEntry) setCardData(detailedEntry)
      setIsLoading(false)
    }
    if (id) {
      fetchDataById(id)
    }
  }, [id])

  return (
    <EntryCard
      status={cardData?.sys?.fieldStatus?.['*']?.['en-CA']}
      contentType={ContentTypeMapper(cardData?.sys?.contentType?.sys?.id)}
      title={cardData?.fields?.internalName?.['en-CA']}
      style={{
        position: 'inherit',
      }}
      isLoading={isLoading}
      actions={[
        <MenuItem key='copy' onClick={handleOnEdit}>
          Edit
        </MenuItem>,
        onRemoveEntry && (
          <MenuItem key='delete' onClick={() => onRemoveEntry()}>
            Remove
          </MenuItem>
        ),
      ]}
    />
  )
}

export default ContentEntryCard

/**
 * Maps a content type ID to a human-readable name.
 * @param {string} contentType The content type ID to map.
 * @returns {string} The human-readable name of the content type.
 */
const ContentTypeMapper = (contentType: string) => {
  switch (contentType) {
    case 'componentCtaRow':
      return 'CTA Row'
    case 'page':
      return 'Page'
    default:
      return contentType
  }
}
