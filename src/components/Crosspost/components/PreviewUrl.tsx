import React from 'react'
import {
  Domains,
  domains,
  getDomainShortName,
  getDomainWebsiteName,
  getLocaleFullName,
} from '../utils'

import { getDomainDataFromDomainConfig, getDomainPreview } from '../helpers'

interface Props {
  checkboxState: { [key: string]: boolean }
  slugAgainstDomains: any
  getSelectedDomainNames: () => string[]
  entryId: string
}

/**
 * Component that renders a list of preview urls for each domain that
 * a page has been crossposted to.
 *
 * @param {Props} props
 * @prop {Object} checkboxState - The state of the checkbox for each domain.
 * @prop {Object} slugAgainstDomains - The slug for each domain.
 * @prop {string} entryId - The id of the current entry.
 * @prop {() => string[]} getSelectedDomainNames - A function that returns an
 * array of selected domain names.
 */

function PreviewUrl(props: Props) {
  const { checkboxState, slugAgainstDomains, entryId, getSelectedDomainNames } =
    props

  return (
    <div className='flex flex-col p-[16px] pl-6 h-full w-full'>
      <h6
        style={{
          marginTop: 0,
          marginBottom: 10,
        }}
      >
        Preview URLs
      </h6>

      <p className='text-[14.5px] pb-2.5'>
        This Page have been successfully crossposted to the undermentioned
        Preview websites with the following slugs .
      </p>

      <div className='flex flex-col gap-3.5 pt-2.5'>
        {domains?.map((domain, index) => {
          if (!checkboxState[getDomainShortName(domain) as Domains]) return null
          return (
            <div className='flex flex-col' key={index}>
              <div className='flex gap-2.5 w-full justify-start items-start'>
                <span className='text-[15px] font-medium w-[110px] flex justify-between items-center text-black'>
                  <p>{getDomainWebsiteName(domain)}</p>
                </span>
                <div className='flex flex-col gap-2.5'>
                  {getDomainDataFromDomainConfig(
                    getDomainShortName(domain)
                  )[0]?.locales?.map((locale: string, slugIndex: number) => {
                    if (
                      !slugAgainstDomains[entryId]?.[domain]?.slug?.[
                        locale.split('-')[0]
                      ]
                    ) {
                      return null
                    }

                    return (
                      <div
                        key={slugIndex}
                        className='flex justify-start items-start'
                      >
                        <span className='font-medium w-[130px] flex justify-between items-center pr-1.5'>
                          <p className='flex justify-start items-start'>
                            {getLocaleFullName(locale.split('-')[0])} ({locale})
                          </p>{' '}
                          <p> :</p>
                        </span>

                        <a
                          className='text-[#1a1e27] underline text-sm'
                          href={getDomainPreview(
                            getDomainShortName(domain),
                            slugAgainstDomains[entryId]?.[domain]?.slug?.[
                              locale.split('-')[0]
                            ],
                            locale.split('-')[0]
                          )}
                          target='_blank'
                          rel='noreferrer'
                        >
                          {slugAgainstDomains[entryId]?.[domain]?.slug?.[
                            locale.split('-')[0]
                          ] || '404'}
                        </a>
                      </div>
                    )
                  })}
                </div>
              </div>
              {index !== getSelectedDomainNames().length - 1 && (
                <div className='h-px bg-[#cfd9e0] mt-3.5' />
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default PreviewUrl
