import { ACCESS_TOKEN, ENV_VARIABLES } from '../../../constant/variables'

/**
 * This function takes an object and removes any keys that contain an empty object or null.
 * It recursively checks each key in the object to see if it contains any values.
 * If any inner object is empty, it is removed from the outer object.
 * @param data The object to filter
 * @returns The filtered object
 */
export function filterEmptyObjects(data: any) {
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      if (typeof data[key] === 'object' && data[key] !== null) {
        let isEmpty = true

        for (const innerKey in data[key]) {
          if (data[key][innerKey]) {
            isEmpty = false
            break
          }
        }

        if (isEmpty) {
          delete data[key]
        }
      }
    }
  }
  return data
}

/**
 * Merges two arrays of tag objects, removing duplicates by tag ID.
 *
 * @param tags The initial array of tag objects.
 * @param d The array of tag objects to merge into the first array.
 * @returns A new array of merged tag objects, with no duplicates.
 */
export function mergeUniqueTags(tags: any[], d: any[]) {
  if (!d) return

  const uniqueTags = new Set()
  const mergedArray = [...tags, ...d].reduce((acc, item) => {
    const id = item.sys.id
    if (!uniqueTags.has(id)) {
      uniqueTags.add(id)
      acc.push(item)
    }
    return acc
  }, [])
  return mergedArray
}

/**
 * Returns a preview URL for a given domain, slug, and locale.
 * If the slug is given, it is appended to the domain URL.
 * If the locale is not English, it is added to the URL as a parameter.
 * If the slug is not given, the function returns a default URL for the given domain.
 * @param domain The domain to generate the preview URL for.
 * @param slug The slug to append to the domain URL.
 * @param locale The locale to add to the URL, if not English.
 * @returns The preview URL.
 */
export const getDomainPreview = (
  domain: string,
  slug: string,
  locale: string
) => {
  if (slug) return addLangParaToURL(domain, locale, slug)
  else return PreviewBranchUrl(domain)
}

/**
 * Appends a locale parameter to a URL for a given domain and slug.
 * Depending on the domain, the locale parameter is appended as a query parameter
 * or as a path prefix.
 * @param domain The domain for which to generate the URL.
 * @param locale The locale to add to the URL.
 * @param url The slug to append to the domain URL.
 * @returns The URL with the locale parameter.
 */
const addLangParaToURL = (domain: string, locale: string, url: string) => {
  if (locale !== 'en') {
    if (domain === 'agl')
      return `${PreviewBranchUrl(domain)}${url}/?lang=${locale}`

    if (domain === 'fia' || domain === 'ver')
      return `${PreviewBranchUrl(domain)}${locale}/${url}`
  }

  return `${PreviewBranchUrl(domain)}${url}`
}

/**
 * Checks if any key in the given object has an empty string value.
 *
 * @param data An object with string values.
 * @returns True if any key has an empty string as its value, otherwise false.
 */
export const checkEachKeyValue = (data: { [key: string]: string }) => {
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      if (data[key] === '') {
        return true
      }
    }
  }
  return false
}

/**
 * Returns the domain data from the domain config array based on the given domain.
 *
 * @param domain The domain to find in the domain config array.
 * @returns The domain data from the config array, or an empty array if not found.
 */
export const getDomainDataFromDomainConfig = (domain: string) => {
  // func to get the domain data from domain config
  return [
    DOMAIN_CONFIG.find(
      (item) => item.domainCode === domain || item.domainId === domain
    ),
  ]
}

/**
 * Fetches the result of a GraphQL query from Contentful's GraphQL API.
 *
 * @param query The GraphQL query to execute.
 * @param spaceId The ID of the space to query.
 * @param envId The ID of the environment to query.
 * @returns The result of the query as a JSON object.
 */
export async function fetchGraphQLQuery(
  query: string,
  spaceId: string,
  envId: string
): Promise<unknown> {
  return fetch(
    `https://graphql.contentful.com/content/v1/spaces/${spaceId}/environments/${envId}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${ACCESS_TOKEN}`,
      },
      body: JSON.stringify({ query }),
    }
  ).then((response) => response.json())
}

/**
 * Renames the keys of the given object based on the given domain.
 * If the key is 'en', it is renamed to 'en-CA'.
 * If the key is 'fr', it is renamed to 'fr-CA' if the domain is not 'fia', otherwise it is renamed to 'fr/' + the original value.
 * If the key is 'de', it is renamed to 'de-DE' if the domain is not 'fia', otherwise it is renamed to 'fr/' + the original value.
 * For all other keys, the value is copied over unchanged.
 * @param obj The object to rename the keys of.
 * @param domain The domain to use for renaming the keys.
 * @returns The new object with the renamed keys.
 */
export function renameKeys(obj: any, domain: string) {
  const newObj: any = {}
  // const isDomainFia = domain === 'fia'

  const isDomainFia = false
  for (const key in obj) {
    if (key === 'en') {
      newObj['en-CA'] = obj[key]
    } else if (key === 'fr') {
      newObj['fr-CA'] = isDomainFia ? 'fr/' + obj[key] : obj[key]
    } else if (key === 'de') {
      newObj['de-DE'] = isDomainFia ? 'fr/' + obj[key] : obj[key]
    } else {
      newObj[key] = isDomainFia ? 'fr/' + obj[key] : obj[key]
    }
  }
  return newObj
}

export const DOMAIN_CONFIG = [
  {
    domainCode: 'agl',
    domainName: 'Altus Group',
    domainId: 'domainAltusGroupCom',
    locales: ['en-CA', 'fr-CA'],
    url: 'https://www.altusgroup.com/',
    primaryLang: 'en-CA',
  },
  {
    domainCode: 'fia',
    domainName: 'Finance Active',
    domainId: 'domainFinanceActiveCom',
    locales: ['en-CA', 'fr-CA', 'de-DE', 'es', 'nl', 'it'],
    url: 'https://financeactive.com/',
    primaryLang: 'fr-CA',
  },
  {
    domainCode: 'ver',
    domainName: 'Verifino',
    domainId: 'domainVerifinoCom',
    locales: ['en-CA', 'de-DE'],
    url: 'https://verifino.com/',
    primaryLang: 'de-DE',
  },
  {
    domainCode: 'reo',
    domainName: 'Reonomy',
    domainId: 'domainReonomyCom',
    locales: ['en-CA'],
    url: 'https://reonomy.com/',
    primaryLang: 'en-CA',
  },
  {
    domainCode: 'o11',
    domainName: 'One11',
    domainId: 'domainOne11Com',
    locales: ['en-CA', 'fr-CA'],
    url: '',
    primaryLang: 'en-CA',
  },
  {
    domainCode: 'for',
    domainName: 'Forbury',
    domainId: 'domainForburyCom',
    locales: ['en-CA'],
    primaryLang: 'en-CA',
    url: 'https://www.forbury.com/',
  },
  {
    domainCode: 'adx',
    domainName: 'Ag Studio',
    domainId: 'domainAgStudio',
    locales: ['en-CA'],
    primaryLang: 'en-CA',
    url: 'https://msa-adx-v3w-git-main-altus.vercel.app/',
  },
]

/**
 * Looks up a domain's configuration data by its `domainCode` or `domainId` and returns
 * the requested data.
 *
 * @param identifier The `domainCode` or `domainId` of the domain to look up.
 * @param type The type of data to return. Can be one of `domainCode`, `label`, `domainId`,
 *   `locales`, `url`, or `primaryLang`.
 * @returns The requested data if found, otherwise an empty string.
 */
export function getDomainData(
  identifier: string,
  type:
    | 'domainCode'
    | 'domainName'
    | 'domainId'
    | 'locales'
    | 'url'
    | 'primaryLang'
): string | string[] {
  // Look for a match in `domainCode` or `domainId`
  const domain = DOMAIN_CONFIG.find(
    (item) => item.domainCode === identifier || item.domainId === identifier
  )

  // Return the requested data if found, otherwise an empty string
  return domain ? domain[type] : ''
}

export const PreviewBranchUrl = (domainCode: string) => {
  const previewBranch = ENV_VARIABLES.mainBranch

  const isMaster = previewBranch === 'main' || previewBranch === 'master'

  if (isMaster) {
    return PROD_URLS[domainCode as keyof typeof PROD_URLS]
  } else {
    return MSA_URLS[domainCode as keyof typeof MSA_URLS].replace(
      'main',
      previewBranch
    )
  }
}

export const PROD_URLS = {
  agl: 'https://www.altusgroup.com/',
  ver: 'https://www.verifino.com/',
  fia: 'https://financeactive.com/',
  reo: 'https://www.reonomy.com/',
  o11: 'https://www.one11advisors.com/',
  for: 'https://www.forbury.com/',
  adx: 'https://ag.studio/',
}

export const MSA_URLS = {
  agl: 'https://msa-agl-v3w-git-main-altus.vercel.app/',
  ver: 'https://msa-ver-v3w-git-main-altus.vercel.app/',
  fia: 'https://msa-fia-v3w-git-main-altus.vercel.app/',
  reo: 'https://msa-reo-v3w-git-main-altus.vercel.app/',
  o11: 'https://msa-o11-v3w-git-main-altus.vercel.app/',
  for: 'https://msa-for-v3w-git-main-altus.vercel.app/',
  adx: 'https://msa-adx-v3w-git-main-altus.vercel.app/',
}
