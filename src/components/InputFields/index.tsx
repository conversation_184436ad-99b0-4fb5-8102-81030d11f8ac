import React from 'react'
import AppTextInput from './AppTextInput';
import AppRichTextEditor from './AppRichTextEditor';
import { MultipleEntryReferenceEditor } from '@contentful/field-editor-reference';
import AppBoolean from './AppBoolean';
import AppReference from './AppReference';

function AppFields(props) {

    switch (props.type) {
        case "Symbol":
            return <AppTextInput {...props} />
        case "RichText":
            return <AppRichTextEditor {...props} />
        case "Link":
            return <AppReference {...props}/>
        case 'Boolean':
            return <AppBoolean {...props }/>
        default:
                break;
    }
}

export default AppFields