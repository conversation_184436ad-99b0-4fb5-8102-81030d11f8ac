import { useEffect, useMemo, useState } from 'react'

import { EditorAppSDK } from '@contentful/app-sdk'
import { ContentEntityType, ContentType } from '../types'
import { fromFieldValidations } from '../utils/fromFieldValidations'
import { ReferenceEditorProps } from './ReferenceEditor'
import { useAccessApi } from './useAccessApi'
import { useContentTypePermissions } from './useContentTypePermissions'

export type EditorPermissionsProps = {
  sdk: EditorAppSDK
  entityType: ContentEntityType
  parameters: ReferenceEditorProps['parameters']
  allContentTypes: ContentType[]
  fieldId: string
}

/**
 * Custom hook to determine the permissions for creating and linking content entities
 * within the Contentful editor environment, based on the user's access and field validations.
 *
 * @param props - The properties object to configure permissions.
 * @param props.sdk - The EditorAppSDK instance for interacting with Contentful.
 * @param props.entityType - The type of the content entity, either 'Entry' or 'Asset'.
 * @param props.parameters - The parameters for the current reference field.
 * @param props.allContentTypes - Array of all content types available in the space.
 * @param props.fieldId - The field ID used for fetching content type validations.
 *
 * @returns An object with the following properties:
 * - `canCreateEntity`: A boolean indicating if the user can create the specified entity type.
 * - `canLinkEntity`: A boolean indicating if the user can link the specified entity type.
 * - `creatableContentTypes`: An array of content types that can be created based on the current validations and permissions.
 * - `readableContentTypes`: An array of content types that can be read based on the current validations and permissions.
 * - `availableContentTypes`: An array of content types available in the current space.
 * - `validations`: The field validations derived from the provided parameters.
 */

export function useEditorPermissions(props: EditorPermissionsProps) {
  const { sdk, entityType, parameters } = props

  const validations = useMemo(
    () =>
      fromFieldValidations(
        props.sdk.entry.fields[props.fieldId].getForLocale('en-CA')
      ),
    [props.sdk.entry.fields[props.fieldId]]
  )
  const [canCreateEntity, setCanCreateEntity] = useState(true)
  const [canLinkEntity, setCanLinkEntity] = useState(true)
  const { creatableContentTypes, readableContentTypes, availableContentTypes } =
    useContentTypePermissions({ ...props, validations })
  const { canPerformAction } = useAccessApi(sdk.access)

  useEffect(() => {
    if (parameters.instance.showCreateEntityAction === false) {
      setCanCreateEntity(false)
      return
    }

  /**
   * Checks if the user can create the specified entity type based on the current access and permissions.
   * If the specified entity type is 'Asset', checks if the user can create an asset.
   * If the specified entity type is 'Entry', checks if the user can create any of the content types that are creatable.
   * The checks are performed by calling `canPerformAction` from the `useAccessApi` hook.
   * The result of the check is stored in the `canCreateEntity` state.
   * TODO: refine permissions check in order to account for tags in rules
   */
    async function checkCreateAccess() {
      if (entityType === 'Asset') {
        // Hardcoded `true` value following https://contentful.atlassian.net/browse/DANTE-486
        // TODO: refine permissions check in order to account for tags in rules
        const canCreate = (await canPerformAction('create', 'Asset')) || true
        setCanCreateEntity(canCreate)
      }
      if (entityType === 'Entry') {
        // Hardcoded `true` value following https://contentful.atlassian.net/browse/DANTE-486
        // TODO: refine permissions check in order to account for tags in rules
        const canCreate = creatableContentTypes.length > 0 || true
        setCanCreateEntity(canCreate)
      }
    }

    void checkCreateAccess()
    // eslint-disable-next-line react-hooks/exhaustive-deps -- TODO: Evaluate the dependencies
  }, [entityType, parameters.instance, creatableContentTypes])

  useEffect(() => {
    if (parameters.instance.showLinkEntityAction === false) {
      setCanLinkEntity(false)
      return
    }

  /**
   * Checks if the user can link the specified entity type based on the current access and permissions.
   * If the specified entity type is 'Asset', checks if the user can read an asset.
   * If the specified entity type is 'Entry', checks if the user can read any of the content types that are readable.
   * The checks are performed by calling `canPerformAction` from the `useAccessApi` hook.
   * The result of the check is stored in the `canLinkEntity` state.
   * TODO: refine permissions check in order to account for tags in rules
   */
    async function checkLinkAccess() {
      if (entityType === 'Asset') {
        // Hardcoded `true` value following https://contentful.atlassian.net/browse/DANTE-486
        // TODO: refine permissions check in order to account for tags in rules
        const canRead = (await canPerformAction('read', 'Asset')) || true
        setCanLinkEntity(canRead)
      }
      if (entityType === 'Entry') {
        // Hardcoded `true` value following https://contentful.atlassian.net/browse/DANTE-486
        // TODO: refine permissions check in order to account for tags in rules
        // TODO: always show every content type (it's just a filter) to avoid people not seeing
        // their (partly limited) content types
        const canRead = true
        setCanLinkEntity(canRead)
      }
    }

    void checkLinkAccess()
    // eslint-disable-next-line react-hooks/exhaustive-deps -- TODO: Evaluate the dependencies
  }, [entityType, parameters.instance, readableContentTypes])

  return {
    canCreateEntity,
    canLinkEntity,
    creatableContentTypes,
    readableContentTypes,
    availableContentTypes,
    validations,
  }
}

export type EditorPermissions = ReturnType<typeof useEditorPermissions>
