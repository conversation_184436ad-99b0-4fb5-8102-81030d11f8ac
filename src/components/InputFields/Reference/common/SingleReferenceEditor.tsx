import * as React from 'react'
import { useCallback } from 'react'

import { LinkActionsProps, LinkEntityActions } from '../components'
import { useLinkActionsProps } from '../components/LinkActions/LinkEntityActions'
import { ContentEntityType, ContentType, ReferenceValue } from '../types'
import { CustomEntityCardProps, DefaultCardRenderer } from './customCardTypes'
import { ReferenceEditor, ReferenceEditorProps } from './ReferenceEditor'
import { useEditorPermissions } from './useEditorPermissions'

type ChildProps = {
  entityId: string
  entityType: ContentEntityType
  isDisabled: boolean
  setValue: (value: ReferenceValue | null | undefined) => void
  allContentTypes: ContentType[]
  renderCustomCard?: ReferenceEditorProps['renderCustomCard']
  hasCardEditActions: boolean
  hasCardRemoveActions?: boolean
}

type EditorProps = ReferenceEditorProps &
  ChildProps & {
    children: (props: ReferenceEditorProps & ChildProps) => React.ReactElement
  }

/**
 * Editor is a React component that renders a single reference editor.
 *
 * @param props - The props for the Editor component.
 *
 * @returns - A React component that renders a single reference editor.
 *
 * The component will render a link to the referenced item.
 * If the item is not present, it will render a link action list.
 *
 * The component takes a child function that is called with the props
 * of the Editor component. The child function should return a React
 * component that renders the single reference editor.
 *
 * The component will also call the child function with a custom card
 * renderer as a prop, if the `renderCustomCard` prop is provided.
 * The custom card renderer should return a React component that renders
 * a custom card for the referenced item.
 *
 * The component will also call the child function with a custom actions
 * list as a prop, if the `renderCustomActions` prop is provided.
 * The custom actions list should return a React component that renders
 * a custom actions list for the referenced item.
 *
 * The component will also call the child function with a custom link
 * actions list as a prop, if the `renderCustomLinkActions` prop is provided.
 * The custom link actions list should return a React component that renders
 * a custom link actions list for the referenced item.
 */
function Editor(props: EditorProps) {
  const { setValue, entityType } = props
  const editorPermissions = useEditorPermissions(props)

  const onCreate = useCallback(
    (id: string) =>
      void setValue({ sys: { type: 'Link', linkType: entityType, id } }),
    [setValue, entityType]
  )
  const onLink = useCallback(
    (ids: string[]) => {
      const [id] = ids
      setValue({ sys: { type: 'Link', linkType: entityType, id } })
    },
    [setValue, entityType]
  )

  const linkActionsProps = useLinkActionsProps({
    ...props,
    canLinkMultiple: false,
    editorPermissions,
    onCreate,
    onLink,
  })
  // Inject card actions props into the given custom card renderer
  const customCardRenderer = useCallback(
    (
      cardProps: CustomEntityCardProps,
      _: LinkActionsProps,
      renderDefaultCard: DefaultCardRenderer
    ) =>
      props.renderCustomCard
        ? props.renderCustomCard(cardProps, linkActionsProps, renderDefaultCard)
        : false,
    // eslint-disable-next-line react-hooks/exhaustive-deps -- TODO: Evaluate the dependencies
    [linkActionsProps]
  )

  if (!props.entityId) {
    return (
      <LinkEntityActions
        renderCustomActions={props.renderCustomActions}
        {...linkActionsProps}
      />
    )
  }

  return props.children({
    ...props,
    renderCustomCard: props.renderCustomCard && customCardRenderer,
  })
}

/**
 * Component for editing a single reference.
 *
 * This component will render a single reference input field if the value is
 * empty or null. Otherwise, it will render a custom card for the referenced
 * entity. The custom card will be rendered with the given `renderCustomCard`
 * function.
 *
 * Props:
 * - `props` (ReferenceEditorProps): The properties required for configuring
 *   the reference editor.
 * - `entityType` (ContentEntityType): The type of the referenced entity.
 * - `children` (ChildProps => React.ReactElement): A function to render a
 *   custom card for the referenced entity.
 *
 * Returns:
 * A React component that renders the reference input field or the custom card
 * for the referenced entity.
 */
export function SingleReferenceEditor(
  props: ReferenceEditorProps & {
    entityType: ContentEntityType
    children: (props: ChildProps) => React.ReactElement
  }
) {
  const allContentTypes = props.sdk.space.getCachedContentTypes()

  return (
    <ReferenceEditor<ReferenceValue> {...props}>
      {({ value, setValue, disabled, externalReset }) => {
        return (
          <Editor
            {...props}
            key={`${externalReset}-reference`}
            entityId={value ? value.sys.id : ''}
            isDisabled={disabled}
            setValue={setValue}
            allContentTypes={allContentTypes}
          />
        )
      }}
    </ReferenceEditor>
  )
}

SingleReferenceEditor.defaultProps = {
  hasCardEditActions: true,
  hasCardRemoveActions: true,
}
