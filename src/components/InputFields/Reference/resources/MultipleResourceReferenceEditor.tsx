import * as React from 'react'
import { useCallback } from 'react'
import { SortEndHandler, SortStartHandler } from 'react-sortable-hoc'

import { FieldConnector } from '@contentful/field-editor-shared'
import { arrayMoveImmutable } from 'array-move'
import deepEqual from 'deep-equal'

import { EntityProvider } from '../common/EntityStore'
import { ReferenceEditorProps } from '../common/ReferenceEditor'
import { SortableLinkList } from '../common/SortableLinkList'
import { CombinedLinkEntityActions } from '../components/LinkActions/LinkEntityActions'
import { ResourceLink } from '../types'
import { EntryRoute } from './Cards/ContentfulEntryCard'
import { ResourceCard } from './Cards/ResourceCard'
import { useResourceLinkActions } from './useResourceLinkActions'

type ChildProps = {
  items: ResourceLink[]
  isDisabled: boolean
  setValue: (value: ResourceLink[]) => void
  onSortStart: SortStartHandler
  onSortEnd: SortEndHandler
  onMove: (oldIndex: number, newIndex: number) => void
  onRemoteItemAtIndex: (index: number) => void
}

type EditorProps = ReferenceEditorProps &
  Omit<
    ChildProps,
    'onSortStart' | 'onSortEnd' | 'onMove' | 'onRemoteItemAtIndex'
  > & {
    children: (props: ReferenceEditorProps & ChildProps) => React.ReactElement
    apiUrl: string
  }

/**
 * ResourceEditor is a React component that is used to edit a multiple reference field,
 * specific to resources.
 *
 * @param props - The props for the ResourceEditor component.
 *
 * @returns - A React component that renders a sortable list of references.
 *
 * The component takes a child function that is called with the props
 * of the ResourceEditor component. The child function should return a React
 * component that renders the multiple reference editor.
 *
 * The component will also call the child function with a custom card
 * renderer as a prop, if the `renderCustomCard` prop is provided.
 * The custom card renderer should return a React component that renders
 * a custom card for the referenced item.
 *
 * The component will also call the child function with a custom actions
 * list as a prop, if the `renderCustomActions` prop is provided.
 * The custom actions list should return a React component that renders
 * a custom actions list for the referenced item.
 */
function ResourceEditor(props: EditorProps) {
  const { setValue, items, apiUrl } = props

  const onSortStart: SortStartHandler = useCallback(
    (_, event) => event.preventDefault(),
    []
  )
  const onSortEnd: SortEndHandler = useCallback(
    ({ oldIndex, newIndex }) => {
      const newItems = arrayMoveImmutable(items, oldIndex, newIndex)
      setValue(newItems)
    },
    [items, setValue]
  )
  const onMove = useCallback(
    (oldIndex, newIndex) => {
      const newItems = arrayMoveImmutable(items, oldIndex, newIndex)
      setValue(newItems)
    },
    [items, setValue]
  )

  const onRemoteItemAtIndex = useCallback(
    (index) => {
      setValue(items.filter((_v, i) => i !== index))
    },
    [items, setValue]
  )

  const { dialogs, field } = props.sdk
  const linkActionsProps = useResourceLinkActions({
    dialogs,
    field,
    apiUrl,
  })

  return (
    <>
      {props.children({
        ...props,
        onSortStart,
        onSortEnd,
        onMove,
        onRemoteItemAtIndex,
      })}
      <CombinedLinkEntityActions
        {...linkActionsProps}
        renderCustomActions={props.renderCustomActions}
      />
    </>
  )
}
/**
 * WithPerItemCallbacks is a React component that is used to wrap a list
 * component item with callbacks for moving the item up and down in the list,
 * as well as removing the item from the list.
 *
 * The component takes as props the length of the list, the index of the
 * item in the list, a callback function to move the item, and a callback
 * function to remove the item from the list. The component will then call
 * the callback function with the appropriate arguments when the corresponding
 * action is triggered.
 *
 * The component also memoizes the callbacks using React's useMemo hook,
 * so that the callbacks are only recomputed when the dependencies change.
 *
 * The component renders null and only passes the callbacks as props to
 * its children.
 *
 * @param props - The props for the WithPerItemCallbacks component.
 *
 * @returns - A React component that renders null and passes the callbacks
 * as props to its children.
 *
 * The component takes the following props:
 * - listLength: The length of the list.
 * - index: The index of the item in the list.
 * - onMove: A callback function to move the item.
 * - onRemoteItemAtIndex: A callback function to remove the item from the list.
 * - children: A function that takes the callbacks as props and returns a
 * React component.
 */
function WithPerItemCallbacks({
  listLength,
  index,
  onMove,
  onRemoteItemAtIndex,
  children,
}: {
  listLength: number
  index: number
  onMove: (oldIndex: number, newIndex: number) => void
  onRemoteItemAtIndex: (index: number) => void
  children: (props: {
    onMoveTop: VoidFunction | undefined
    onMoveBottom: VoidFunction | undefined
    onRemove: VoidFunction | undefined
  }) => React.ReactNode
}) {
  const handleMoveTop = React.useMemo(
    () => (index > 0 ? () => onMove(index, 0) : undefined),
    [index, onMove]
  )
  const handleMoveBottom = React.useMemo(
    () =>
      index < listLength - 1 ? () => onMove(index, listLength - 1) : undefined,
    [index, onMove, listLength]
  )
  const handleRemove = useCallback(
    () => onRemoteItemAtIndex(index),
    [index, onRemoteItemAtIndex]
  )

  return (
    <>
      {children({
        onMoveBottom: handleMoveBottom,
        onMoveTop: handleMoveTop,
        onRemove: handleRemove,
      })}
    </>
  )
}

const EMPTY_ARRAY: ResourceLink[] = []

/**
 * MultipleResourceReferenceEditor is a React component for managing multiple resource references.
 *
 * This component utilizes the `FieldConnector` to bind the field data and provides the ability
 * to sort and manage multiple resource links through the `ResourceEditor` and `SortableLinkList`.
 * It allows for custom card rendering and custom actions for each resource link.
 *
 * Props:
 * - `props` (ReferenceEditorProps & { apiUrl: string, getEntryRouteHref: (entryRoute: EntryRoute) => string }): 
 *   The properties required for configuring the resource editor, including API URL and routing.
 *
 * Returns:
 * A React component that renders a sortable list of resource links with custom actions and card rendering.
 */

export function MultipleResourceReferenceEditor(
  props: ReferenceEditorProps & {
    apiUrl: string
    getEntryRouteHref: (entryRoute: EntryRoute) => string
  }
) {
  return (
    <EntityProvider sdk={props.sdk}>
      <FieldConnector<ResourceLink[]>
        throttle={0}
        field={props.sdk.field}
        isInitiallyDisabled={props.isInitiallyDisabled}
        isEqualValues={deepEqual}
      >
        {({ value, disabled, setValue, externalReset }) => {
          return (
            <ResourceEditor
              {...props}
              items={value || EMPTY_ARRAY}
              isDisabled={disabled}
              setValue={setValue}
              renderCustomActions={props.renderCustomActions}
              key={`${externalReset}-list`}
            >
              {(editorProps) => (
                <SortableLinkList<ResourceLink> {...editorProps}>
                  {({ item, isDisabled, DragHandle, index }) => (
                    <WithPerItemCallbacks
                      index={index}
                      onMove={editorProps.onMove}
                      onRemoteItemAtIndex={editorProps.onRemoteItemAtIndex}
                      listLength={value?.length || 0}
                    >
                      {({ onMoveBottom, onMoveTop, onRemove }) => (
                        <ResourceCard
                          index={index}
                          resourceLink={item}
                          isDisabled={isDisabled}
                          renderDragHandle={DragHandle}
                          onMoveTop={onMoveTop}
                          onMoveBottom={onMoveBottom}
                          onRemove={onRemove}
                          getEntryRouteHref={props.getEntryRouteHref}
                        />
                      )}
                    </WithPerItemCallbacks>
                  )}
                </SortableLinkList>
              )}
            </ResourceEditor>
          )
        }}
      </FieldConnector>
    </EntityProvider>
  )
}
