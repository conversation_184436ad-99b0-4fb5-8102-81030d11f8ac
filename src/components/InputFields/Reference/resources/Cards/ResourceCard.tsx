import * as React from 'react'
import { useInView } from 'react-intersection-observer'

import { EntryCard } from '@contentful/f36-components'
import { SetRequired } from 'type-fest'

import { useResource } from '../../common/EntityStore'
import { ResourceEntityErrorCard } from '../../components'
import { RenderDragFn, ResourceLink } from '../../types'
import {
  CardActionsHandlers,
  ContentfulEntryCard,
  EntryRoute,
} from './ContentfulEntryCard'

type ResourceCardProps = {
  index?: number
  resourceLink?: ResourceLink
  isDisabled: boolean
  renderDragHandle?: RenderDragFn
  getEntryRouteHref: (entryRoute: EntryRoute) => string
} & CardActionsHandlers

function ResourceCardSkeleton() {
  return <EntryCard size='small' isLoading />
}

/**
 * A card that is rendered when there is a value in the field.
 *
 * @param index - The index of the card in the list. Used for sorting.
 * @param resourceLink - The resource that is being rendered.
 * @param isDisabled - If true, the card will be disabled and the remove button will not be rendered.
 * @param inView - If true, the card will be rendered with a priority based on the index.
 * @param getEntryRouteHref - The function to be called when the user clicks the card. It should return the URL of the entry in the Contentful web app.
 * @param onRemove - The function to be called when the user clicks the remove button.
 */
function ExistingResourceCard(
  props: SetRequired<ResourceCardProps, 'resourceLink'> & {
    inView: boolean
  }
) {
  const { resourceLink, inView, index = 0 } = props
  const resourceOptions = { priority: index * -1, enabled: inView }
  const { data, error } = useResource(
    resourceLink.sys.linkType,
    resourceLink.sys.urn,
    resourceOptions
  )
  if (!data && !error) {
    return <ResourceCardSkeleton />
  }

  if (data) {
    // @ts-expect-error
    return <ContentfulEntryCard info={data} {...props} />
  }

  return (
    <ResourceEntityErrorCard
      linkType={resourceLink.sys.linkType}
      error={error}
      isDisabled={props.isDisabled}
      onRemove={props.onRemove}
    />
  )
}

/**
 * A wrapper component that conditionally renders an `ExistingResourceCard`.
 *
 * @param props - The component props.
 * @param props.resourceLink - The resource link to be rendered.
 * @param props.inView - Determines if the card should be rendered based on its visibility.
 * @returns The `ExistingResourceCard` component if `resourceLink` is provided, otherwise null.
 */

function ResourceCardWrapper(props: ResourceCardProps & { inView: boolean }) {
  if (!props.resourceLink) {
    return null
  }

  return (
    <ExistingResourceCard
      {...props}
      resourceLink={props.resourceLink}
      getEntryRouteHref={props.getEntryRouteHref}
    />
  )
}

/**
 * A component that renders a resource card with lazy loading.
 *
 * This component uses the `useInView` hook to determine when the card
 * should be rendered, optimizing performance by only loading the card
 * when it comes into view.
 *
 * @param props - The properties required to render the resource card.
 * @param props.index - The index of the card, used for sorting.
 * @param props.resourceLink - The link to the resource to be rendered.
 * @param props.isDisabled - If true, disables the card and its actions.
 * @param props.renderDragHandle - A function to render a drag handle for sorting.
 * @param props.getEntryRouteHref - A function to get the URL of the entry in the Contentful web app.
 *
 * @returns A JSX element representing the resource card.
 */

export function ResourceCard(props: ResourceCardProps) {
  const { ref, inView } = useInView({
    triggerOnce: true,
    rootMargin: '300px 0px 0px 300px',
  })

  // Forma does not offer us to pass refs, so we need an additional wrapper here
  return (
    <div ref={ref}>
      <ResourceCardWrapper {...props} inView={inView} />
    </div>
  )
}
