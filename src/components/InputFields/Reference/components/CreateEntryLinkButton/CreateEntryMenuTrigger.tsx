/* eslint-disable @typescript-eslint/no-explicit-any */

import React, { useEffect, useRef, useState } from 'react'

import { Menu, MenuProps, TextInput } from '@contentful/f36-components'
import { SearchIcon } from '@contentful/f36-icons'
import tokens from '@contentful/f36-tokens'
import { css } from 'emotion'
import get from 'lodash/get'

import { ContentType } from '../../types'

const MAX_ITEMS_WITHOUT_SEARCH = 5

const menuPlacementMap: {
  [key: string]: MenuProps['placement']
} = {
  'bottom-left': 'bottom-start',
  'bottom-right': 'bottom-end',
}

const styles = {
  wrapper: css({
    position: 'relative',
  }),
  inputWrapper: css({
    position: 'relative',
    padding: `0 ${tokens.spacing2Xs}`,
  }),
  searchInput: css({
    paddingRight: tokens.spacingXl,
    textOverflow: 'ellipsis',
  }),
  searchIcon: css({
    position: 'absolute',
    right: tokens.spacingM,
    top: tokens.spacingS,
    zIndex: Number(tokens.zIndexDefault),
    fill: tokens.gray600,
  }),
  separator: css({
    background: tokens.gray200,
    margin: '10px 0',
  }),
  dropdownList: css({
    borderColor: tokens.gray200,
  }),
}

type CreateEntryMenuTriggerChildProps = {
  isOpen: boolean
  isSelecting: boolean
}
export type CreateEntryMenuTriggerChild = (
  props: CreateEntryMenuTriggerChildProps
) => React.ReactElement
export type CreateCustomEntryMenuItems = ({
  closeMenu,
}: {
  closeMenu: Function
}) => React.ReactElement

interface CreateEntryMenuTrigger {
  contentTypes: ContentType[]
  suggestedContentTypeId?: string
  contentTypesLabel?: string
  onSelect: (contentTypeId: string) => Promise<unknown>
  testId?: string
  dropdownSettings?: {
    isAutoalignmentEnabled?: boolean
    position: 'bottom-left' | 'bottom-right'
  }
  customDropdownItems?: React.ReactNode
  children: CreateEntryMenuTriggerChild
  menuProps?: Omit<MenuProps, 'children'>
}

/**
 * Component to render a menu trigger for creating entries.
 *
 * This component provides a menu interface for selecting content types
 * to create new entries. It supports features like search, suggested
 * content types, and custom dropdown items.
 *
 * Props:
 * - `contentTypes` (ContentType[]): The list of available content types.
 * - `suggestedContentTypeId` (string | undefined): Optional ID of the suggested content type.
 * - `contentTypesLabel` (string | undefined): Optional label for content types section.
 * - `onSelect` (function): Callback function to be invoked when a content type is selected.
 * - `testId` (string | undefined): Optional test ID for the component.
 * - `dropdownSettings` (object): Settings for dropdown alignment and position.
 * - `customDropdownItems` (React.ReactNode | undefined): Optional custom items to be displayed in the dropdown.
 * - `children` (function): A render prop providing `isOpen` and `isSelecting` states.
 * - `menuProps` (object | undefined): Additional props for the menu component.
 *
 * State:
 * - `isOpen` (boolean): Indicates whether the menu is open.
 * - `isSelecting` (boolean): Indicates whether a selection is in progress.
 * - `searchInput` (string): The current search input value.
 * - `dropdownWidth` (number | undefined): The width of the dropdown menu.
 *
 * Functions:
 * - `closeMenu`: Closes the menu.
 * - `handleSelect`: Handles the selection of a content type.
 * - `handleMenuOpen`: Opens the menu or selects the first content type if no dropdown.
 * - `renderSearchResultsCount`: Renders the count of search results.
 *
 * Effects:
 * - Focuses the search input when the menu opens.
 * - Sets the dropdown width based on the menu content.
 * - Resets the search input when the menu closes.
 */

export const CreateEntryMenuTrigger = ({
  contentTypes,
  suggestedContentTypeId,
  contentTypesLabel,
  onSelect,
  testId,
  dropdownSettings = {
    position: 'bottom-left',
  },
  customDropdownItems,
  children,
  menuProps,
}: CreateEntryMenuTrigger) => {
  const [isOpen, setOpen] = useState(false)
  const [isSelecting, setSelecting] = useState(false)
  const [searchInput, setSearchInput] = useState('')
  const wrapper = useRef<any | null>(null)
  const textField = useRef<any | null>(null)
  const menuListRef = useRef<any | null>(null)
  /*
    By default, dropdown wraps it's content, so it's width = the width of the widest item
    During search, menu items change, and so the widest menu item can change
    This leads to menu always changing it's width
    To prevent this, we get the width of the menu item after the first mount of a dropdown (when all the content is displayed)
    And hardcode it through the class name. This way we ensure that even during search the menu will keep that max width
    That it had on initial mount and that fits any menu item in has
  */
  const [dropdownWidth, setDropdownWidth] = useState()

  const hasDropdown = contentTypes.length > 1 || !!customDropdownItems

  const closeMenu = () => setOpen(false)

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        textField.current
          ?.querySelector('input')
          ?.focus({ preventScroll: true })
      }, 200)
    }
  }, [isOpen])

  useEffect(() => {
    if (isOpen && !dropdownWidth) {
      setDropdownWidth(menuListRef.current?.clientWidth)
    }
  }, [isOpen, dropdownWidth])

/**
 * Handles the selection of a menu item.
 * 
 * @param {ContentType} item - The content type item that was selected.
 * 
 * This function closes the menu and attempts to perform a selection action
 * with the given item's `sys.id`. If the selection action returns a promise,
 * it sets the `isSelecting` state to `true` during the promise execution and
 * resets it to `false` once the promise is resolved or rejected.
 */

  const handleSelect = (item: ContentType) => {
    closeMenu()
    const res = onSelect(item.sys.id)

    // TODO: Convert to controllable component.
    if (res && typeof res.then === 'function') {
      setSelecting(true)
      res.then(
        () => setSelecting(false),
        () => setSelecting(false)
      )
    }
  }

  /**
   * Opens the dropdown menu and sets the selecting state to true.
   * If there is no dropdown (i.e. only one content type), it will select the first content type.
   */
  const handleMenuOpen = () => {
    if (hasDropdown) {
      setOpen(true)
    } else {
      handleSelect(contentTypes[0])
    }
  }

  useEffect(() => {
    if (!isOpen) {
      setSearchInput('')
    }
  }, [isOpen])

/**
 * Renders the search results count as a section title in the menu.
 *
 * @param {number} resultsLength - The number of search results.
 * @returns {JSX.Element | null} A section title displaying the results count,
 *                               or null if there are no results.
 */

  const renderSearchResultsCount = (resultsLength: number) =>
    resultsLength ? (
      <Menu.SectionTitle testId='add-entru-menu-search-results'>
        {resultsLength} result{resultsLength > 1 ? 's' : ''}
      </Menu.SectionTitle>
    ) : null

  const isSearchable = contentTypes.length > MAX_ITEMS_WITHOUT_SEARCH
  const maxDropdownHeight = suggestedContentTypeId ? 300 : 250
  const suggestedContentType = contentTypes.find(
    (ct) => ct.sys.id === suggestedContentTypeId
  )
  const filteredContentTypes = contentTypes.filter(
    (ct) =>
      !searchInput ||
      get(ct, 'name', 'Untitled')
        .toLowerCase()
        .includes(searchInput.toLowerCase())
  )

  return (
    <span className={styles.wrapper} ref={wrapper} data-test-id={testId}>
      <Menu
        placement={menuPlacementMap[dropdownSettings.position]}
        isAutoalignmentEnabled={dropdownSettings.isAutoalignmentEnabled}
        isOpen={isOpen}
        onClose={closeMenu}
        onOpen={handleMenuOpen}
        {...menuProps}
      >
        <Menu.Trigger>{children({ isOpen, isSelecting })}</Menu.Trigger>

        {isOpen && (
          <Menu.List
            className={styles.dropdownList}
            style={{
              width:
                dropdownWidth != undefined ? `${dropdownWidth}px` : undefined,
              maxHeight: `${maxDropdownHeight}px`,
            }}
            ref={menuListRef}
            testId='add-entry-menu'
          >
            {Boolean(customDropdownItems) && (
              <>
                {customDropdownItems}
                <Menu.Divider />
              </>
            )}

            {isSearchable && (
              <>
                <div ref={textField} className={styles.inputWrapper}>
                  <TextInput
                    className={styles.searchInput}
                    placeholder='Search all content types'
                    testId='add-entry-menu-search'
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                  />
                  <SearchIcon className={styles.searchIcon} />
                </div>
                <Menu.Divider />
              </>
            )}

            {searchInput &&
              renderSearchResultsCount(filteredContentTypes.length)}
            {suggestedContentType && !searchInput && (
              <>
                <Menu.SectionTitle>Suggested Content Type</Menu.SectionTitle>
                <Menu.Item
                  testId='suggested'
                  onClick={() => handleSelect(suggestedContentType)}
                >
                  {get(suggestedContentType, 'name')}
                </Menu.Item>
                <Menu.Divider />
              </>
            )}
            {!searchInput && (
              <Menu.SectionTitle>{contentTypesLabel}</Menu.SectionTitle>
            )}
            {filteredContentTypes.length ? (
              filteredContentTypes.map((contentType, i) => (
                <Menu.Item
                  testId='contentType'
                  key={`${get(contentType, 'name')}-${i}`}
                  onClick={() => handleSelect(contentType)}
                >
                  {get(contentType, 'name', 'Untitled')}
                </Menu.Item>
              ))
            ) : (
              <Menu.Item testId='add-entru-menu-search-results'>
                No results found
              </Menu.Item>
            )}
          </Menu.List>
        )}
      </Menu>
    </span>
  )
}

CreateEntryMenuTrigger.defaultProps = {
  testId: 'create-entry-button-menu-trigger',
  contentTypesLabel: 'All Content Types',
}
