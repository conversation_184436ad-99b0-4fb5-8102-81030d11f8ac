import { EditorAppSDK } from '@contentful/app-sdk'
import { createContentEntry } from '../../../../../globals/utils'
import { EditorPermissions } from '../../common/useEditorPermissions'
import { Asset, ContentEntityType, ContentType, Entry } from '../../types'

/**
 * Maps an array of content types to their respective IDs.
 *
 * @param contentTypes - Array of content types.
 * @returns Array of content type IDs.
 */
const getContentTypeIds = (contentTypes: ContentType[]) =>
  contentTypes.map((ct) => ct.sys.id)

/**
 * Creates a new Contentful entity of the specified type and optionally opens it in the editor.
 *
 * For entries, if a `contentTypeId` is provided, a new entry is created and opened in a slide-in editor.
 * For assets, a new asset creation dialog is opened.
 *
 * @param props - The properties for entity creation.
 * @param props.sdk - The EditorAppSDK instance for interacting with Contentful.
 * @param props.entityType - The type of entity to create ('Entry' or 'Asset').
 * @param props.contentTypeId - The ID of the content type for the entry to be created (required for entries).
 *
 * @returns A promise that resolves to an object containing the created entity and slide information,
 *          or an empty object if no entity is created.
 */

export async function createEntity(props: {
  sdk: EditorAppSDK
  entityType: ContentEntityType
  contentTypeId?: string
}) {
  if (props.entityType === 'Entry') {
    if (!props.contentTypeId) {
      console.log('createEntity1 retuern')

      return {}
    }
    // const { entity, slide } = await props.sdk.navigator.openNewEntry<Entry>(
    //   props.contentTypeId,
    //   {
    //     slideIn: true
    //   }
    // )
    // const entity = await props.sdk.space.createEntry(props.contentTypeId, {
    //   fields: {},
    // })

    const entity: Entry = await createContentEntry(props.contentTypeId)

    if (!entity) return

    await props.sdk.navigator.openEntry(entity.sys.id, {
      slideIn: true,
    })

    return { entity }
  } else {
    const { entity, slide } = await props.sdk.navigator.openNewAsset({
      slideIn: true,
    })
    return { entity, slide }
  }
}

  /**
   * Opens a dialog for selecting a single Contentful entity of the given entity type and returns the selected entity.
   * @param props - The props object with the following properties:
   *                - `sdk`: The HomeAppSDK instance.
   *                - `entityType`: 'Entry' or 'Asset'.
   *                - `editorPermissions`: The object with `readableContentTypes` and `validations.mimetypeGroups` properties.
   * @returns The selected entity.
   */
export async function selectSingleEntity(props: {
  sdk: EditorAppSDK
  entityType: ContentEntityType
  editorPermissions: EditorPermissions
}) {
  if (props.entityType === 'Entry') {
    return await props.sdk.dialogs.selectSingleEntry<Entry>({
      // locale: props.sdk.field.locale,
      locale: 'en-CA',
      contentTypes: getContentTypeIds(
        props.editorPermissions.readableContentTypes
      ),
    })
  } else {
    return props.sdk.dialogs.selectSingleAsset<Asset>({
      // locale: props.sdk.field.locale,
      locale: 'en-CA',
      mimetypeGroups: props.editorPermissions.validations.mimetypeGroups,
    })
  }
}

  /**
   * Opens a dialog for selecting multiple Contentful entities of the given entity type and returns the selected entities.
   * @param props - The props object with the following properties:
   *                - `fieldId`: The ID of the field to link entities to.
   *                - `sdk`: The HomeAppSDK instance.
   *                - `entityType`: 'Entry' or 'Asset'.
   *                - `editorPermissions`: The object with `readableContentTypes` and `validations.mimetypeGroups` properties.
   * @returns The selected entities.
   */
export async function selectMultipleEntities(props: {
  fieldId: string
  sdk: EditorAppSDK
  entityType: ContentEntityType
  editorPermissions: EditorPermissions
}) {
  const value = props.sdk.entry.fields[props.fieldId].getValue()

  const linkCount = Array.isArray(value) ? value.length : value ? 1 : 0

  // TODO: Why not always set `min: 1` by default? Does it make sense to enforce
  //  user to select as many entities as the field's "min" requires? What if e.g.
  // "min" is 4 and the user wants to insert 2 entities first, then create 2 new ones?
  const min = Math.max(
    (props.editorPermissions.validations.numberOfLinks?.min || 1) - linkCount,
    1
  )
  // TODO: Consider same for max. If e.g. "max" is 4, we disable the button if the
  //  user wants to select 5 but we show no information why the button is disabled.
  const max =
    (props.editorPermissions.validations.numberOfLinks?.max || +Infinity) -
    linkCount

  if (props.entityType === 'Entry') {
    return await props.sdk.dialogs.selectMultipleEntries<Entry>({
      locale: props.sdk.entry.fields[props.fieldId].locales[0],
      contentTypes: getContentTypeIds(
        props.editorPermissions.readableContentTypes
      ),
      min,
      max,
    })
  } else {
    return props.sdk.dialogs.selectMultipleAssets<Asset>({
      locale: props.sdk.entry.fields[props.fieldId].locales[0],
      mimetypeGroups: props.editorPermissions.validations.mimetypeGroups,
      min,
      max,
    })
  }
}
