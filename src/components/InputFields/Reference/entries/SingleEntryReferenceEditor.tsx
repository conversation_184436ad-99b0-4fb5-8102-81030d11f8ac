import * as React from 'react'

import { ReferenceEditorProps } from '../common/ReferenceEditor'
import { SingleReferenceEditor } from '../common/SingleReferenceEditor'
import { FetchingWrappedEntryCard } from './WrappedEntryCard/FetchingWrappedEntryCard'

/**
 * SingleEntryReferenceEditor is a React component that is used to edit a single reference field.
 * It uses the SingleReferenceEditor component to render a link to the referenced entry.
 *
 * @param props - The props for the SingleEntryReferenceEditor component.
 *
 * @returns - A React component that renders a link to the referenced entry.
 */
export function SingleEntryReferenceEditor(props: ReferenceEditorProps) {
  return (
    <SingleReferenceEditor {...props} entityType='Entry'>
      {({
        allContentTypes,
        isDisabled,
        entityId,
        setValue,
        renderCustomCard,
        hasCardRemoveActions,
        hasCardEditActions,
      }) => {
        return (
          <FetchingWrappedEntryCard
            {...props}
            allContentTypes={allContentTypes}
            isDisabled={isDisabled}
            entryId={entityId}
            renderCustomCard={renderCustomCard}
            hasCardEditActions={hasCardEditActions}
            hasCardRemoveActions={hasCardRemoveActions}
            onRemove={() => {
              setValue(null)
            }}
          />
        )
      }}
    </SingleReferenceEditor>
  )
}

SingleEntryReferenceEditor.defaultProps = {
  isInitiallyDisabled: true,
}
