import * as React from 'react'

import { EntryCard } from '@contentful/f36-components'
import { EntryProps } from 'contentful-management'
import get from 'lodash/get'

import {
  CustomEntityCardProps,
  RenderCustomMissingEntityCard,
} from '../../common/customCardTypes'
import { useEntity, useEntityLoader } from '../../common/EntityStore'
import { ReferenceEditorProps } from '../../common/ReferenceEditor'
import type { LinkActionsProps } from '../../components'
import { MissingEntityCard } from '../../components'
import {
  ContentType,
  Entry,
  FieldExtensionSDK,
  NavigatorSlideInfo,
  RenderDragFn,
} from '../../types'
import { WrappedEntryCard, WrappedEntryCardProps } from './WrappedEntryCard'

export type EntryCardReferenceEditorProps = ReferenceEditorProps & {
  entryId: string
  index?: number
  // allContentTypes?: ContentType[];
  isDisabled: boolean
  onRemove: () => void
  renderDragHandle?: RenderDragFn
  hasCardEditActions: boolean
  onMoveTop?: () => void
  onMoveBottom?: () => void
  renderCustomMissingEntityCard?: RenderCustomMissingEntityCard
  isBeingDragged?: boolean
}

/**
 * Opens an entry in the Contentful editor. If bulk editing is enabled,
 * attempts to open the bulk editor for the entry field. Falls back to
 * opening the entry in a slide-in view if bulk editing is not applicable.
 *
 * @param {FieldExtensionSDK} sdk - The SDK instance for interacting with Contentful.
 * @param {string} entryId - The ID of the entry to open.
 * @param {Object} options - Additional options for opening the entry.
 * @param {boolean} [options.bulkEditing] - Flag to indicate if the bulk editor should be opened.
 * @param {number} [options.index] - Index position for bulk editing.
 * 
 * @returns {Promise<NavigatorSlideInfo | undefined>} The slide information of the opened entry, if applicable.
 */

async function openEntry(
  sdk: FieldExtensionSDK,
  entryId: string,
  options: { bulkEditing?: boolean; index?: number }
) {
  let slide: NavigatorSlideInfo | undefined

  if (options.bulkEditing) {
    try {
      const result = await sdk.navigator.openBulkEditor(sdk.entry.getSys().id, {
        fieldId: sdk.field.id,
        locale: 'en-CA',
        index: options.index ?? 0,
      })
      slide = result.slide
      return slide
    } catch (e) {
      // we don't allow to open multiple bulk editors for performance reasons
      // proceed with a default openEntry
    }
  }

  const result = await sdk.navigator.openEntry(entryId, {
    slideIn: true,
  })
  slide = result.slide

  return slide
}

/**
 * A component that renders a card for a Contentful entry, with a loading state and a missing card state.
 *
 * @param {Object} props The component props.
 * @param {string} props.entryId The ID of the entry to be rendered.
 * @param {number} [props.index] The index of the entry in the list, if applicable.
 * @param {boolean} [props.isDisabled] Whether the card should be disabled.
 * @param {function} [props.onRemove] The function to be called when the user clicks the "Remove" button.
 * @param {function} [props.renderDragHandle] The function to be called to render the drag handle.
 * @param {boolean} [props.hasCardEditActions] Whether to render the "Edit" and "Remove" actions.
 * @param {boolean} [props.hasCardMoveActions] Whether to render the "Move up" and "Move down" actions.
 * @param {boolean} [props.hasCardRemoveActions] Whether to render the "Remove" action.
 * @param {function} [props.onAction] The function to be called when the user interacts with the card, with an action object as an argument.
 * @param {function} [props.renderCustomMissingEntityCard] The function to be called to render a custom missing entity card.
 * @param {function} [props.renderCustomCard] The function to be called to render a custom card.
 * @param {FieldExtensionSDK} props.sdk The SDK instance for interacting with Contentful.
 *
 * @returns {React.ReactElement} The rendered component.
 */
export function FetchingWrappedEntryCard(props: EntryCardReferenceEditorProps) {
  const { data: entry, status } = useEntity<Entry>('Entry', props.entryId)
  const [contentType, setContentType] = React.useState<ContentType | undefined>(
    undefined
  )
  const { getEntityScheduledActions } = useEntityLoader()
  const loadEntityScheduledActions = React.useCallback(
    () => getEntityScheduledActions('Entry', props.entryId),
    [getEntityScheduledActions, props.entryId]
  )

  const size = props.viewType === 'link' ? 'small' : 'default'
  const { getEntity } = useEntityLoader()
  const getAsset = (assetId: string) => getEntity('Asset', assetId)

  /**
   * Handler for when the user clicks the "Edit" button.
   * Opens the entry editor in a new slide and notifies the parent component
   * of the action taken.
   */
  const onEdit = async () => {
    const slide = await openEntry(props.sdk, props.entryId, {
      bulkEditing: props.parameters.instance.bulkEditing,
      index: props.index,
    })
    props.onAction &&
      props.onAction({
        entity: 'Entry',
        type: 'edit',
        id: props.entryId,
        contentTypeId: get(entry, 'sys.contentType.sys.id'),
        slide,
      })
  }

/**
 * Handles the removal of the entry card.
 * 
 * Calls the `onRemove` prop function to remove the entry and triggers the `onAction` 
 * callback with the 'delete' action type, including the entry ID and content type ID, 
 * if the `onAction` function is provided.
 */

  const onRemoveEntry = () => {
    props.onRemove()
    props.onAction &&
      props.onAction({
        entity: 'Entry',
        type: 'delete',
        id: props.entryId,
        contentTypeId: get(entry, 'sys.contentType.sys.id'),
      })
  }

  React.useEffect(() => {
    if (entry) {
      props.sdk.cma.contentType
        .get({ contentTypeId: entry.sys.contentType.sys.id })
        .then((contentType) => {
          setContentType(contentType)
        })
      props.onAction && props.onAction({ type: 'rendered', entity: 'Entry' })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- TODO: Evaluate the dependencies
  }, [entry])

  return React.useMemo(() => {
    if (status === 'error') {
      const card = (
        <MissingEntityCard
          entityType='Entry'
          isDisabled={props.isDisabled}
          onRemove={onRemoveEntry}
        />
      )
      if (props.renderCustomMissingEntityCard) {
        return props.renderCustomMissingEntityCard({
          defaultCard: card,
          entity: {
            id: props.entryId,
            type: 'Entry',
          },
        })
      }
      return card
    }
    if (status === 'loading' || !contentType) {
      return <EntryCard size={size} isLoading />
    }

    const sharedCardProps: CustomEntityCardProps = {
      index: props.index,
      entity: entry,
      entityUrl: props.getEntityUrl && props.getEntityUrl(entry.sys.id),
      contentType: contentType,
      isDisabled: props.isDisabled,
      size,
      localeCode: props.sdk.locales.default,
      defaultLocaleCode: props.sdk.locales.default,
      renderDragHandle: props.renderDragHandle,
      onEdit,
      onRemove: onRemoveEntry,
      onMoveTop: props.onMoveTop,
      onMoveBottom: props.onMoveBottom,
      isBeingDragged: props.isBeingDragged,
    }

    const { hasCardEditActions, hasCardMoveActions, hasCardRemoveActions } =
      props

  /**
   * Renders a default card for an entry using the props provided.
   *
   * Takes the props provided and combines them with the `sharedCardProps` to create a complete set of props.
   * The `hasCardEditActions`, `hasCardMoveActions` and `hasCardRemoveActions` are also applied to the props.
   * The `getAsset` function is used to fetch the asset for the entry.
   * The `getEntityScheduledActions` function is used to fetch the scheduled actions for the entry.
   * The `entry` and `entryUrl` are taken from the `props` or `sharedCardProps` if not provided.
   *
   * @param {Object} [props] The props to be used for rendering the card.
   * @returns {React.ReactElement} The rendered card.
   */
    function renderDefaultCard(props?: CustomEntityCardProps) {
      const builtinCardProps: WrappedEntryCardProps = {
        ...sharedCardProps,
        ...props,
        hasCardEditActions,
        hasCardMoveActions,
        hasCardRemoveActions,
        getAsset,
        getEntityScheduledActions: loadEntityScheduledActions,
        entry: (props?.entity as EntryProps) || sharedCardProps.entity,
        entryUrl: props?.entityUrl || sharedCardProps.entityUrl,
      }

      return <WrappedEntryCard {...builtinCardProps} />
    }

    if (props.renderCustomCard) {
      // LinkActionsProps are injected higher SingleReferenceEditor/MultipleReferenceEditor
      const renderedCustomCard = props.renderCustomCard(
        sharedCardProps,
        {} as LinkActionsProps,
        renderDefaultCard
      )
      // Only `false` indicates to render the original card. E.g. `null` would result in no card.
      if (renderedCustomCard !== false) {
        return renderedCustomCard
      }
    }

    return renderDefaultCard()
    // eslint-disable-next-line react-hooks/exhaustive-deps -- TODO: Evaluate the dependencies
  }, [props, status, entry, contentType])
}
