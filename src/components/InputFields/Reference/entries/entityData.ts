export const entityData = {
    "metadata": {
        "tags": [],
        "concepts": []
    },
    "sys": {
        "space": {
            "sys": {
                "type": "Link",
                "linkType": "Space",
                "id": "8jgyidtgyr4v"
            }
        },
        "id": "6kY1HPyd1ijnFj8T3qZGD1",
        "type": "Entry",
        "createdAt": "2024-11-28T19:36:10.606Z",
        "updatedAt": "2024-11-28T19:36:10.606Z",
        "environment": {
            "sys": {
                "id": "dev",
                "type": "Link",
                "linkType": "Environment"
            }
        },
        "createdBy": {
            "sys": {
                "type": "Link",
                "linkType": "User",
                "id": "3qQKxcILSfgpg60MK24IPo"
            }
        },
        "updatedBy": {
            "sys": {
                "type": "Link",
                "linkType": "User",
                "id": "3qQKxcILSfgpg60MK24IPo"
            }
        },
        "publishedCounter": 0,
        "version": 1,
        "automationTags": [],
        "contentType": {
            "sys": {
                "type": "Link",
                "linkType": "ContentType",
                "id": "componentLayoutColumn"
            }
        },
        "urn": "crn:contentful:::content:spaces/8jgyidtgyr4v/environments/dev/entries/6kY1HPyd1ijnFj8T3qZGDR"
    },
    "fields": {}
}