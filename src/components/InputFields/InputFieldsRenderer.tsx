/* eslint-disable react-hooks/exhaustive-deps */
import { EditorAppSDK } from '@contentful/app-sdk'
import { Note, Text, Tooltip } from '@contentful/f36-components'
import { useSDK } from '@contentful/react-apps-toolkit'
import React, { useEffect, useRef } from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import Info from '../../assets/icons/Info'
import { HelperText } from '../../configs/helpertext.config'
import { getLocaleFullName } from '../Crosspost/utils'
import { DateEditor } from '../Date'
import Tiptap from '../Tiptap'
import { BooleanEditor } from './Boolean'
import { CheckboxEditor } from './Checkbox'
import { DropdownEditor } from './Dropdown'
import { JsonEditor } from './JSON'
import { NumberEditor } from './Number'
import {
  MultipleEntryReferenceEditor,
  SingleEntryReferenceEditor,
  SingleMediaEditor,
} from './Reference'
import { SingleLineEditor } from './SingleLine'
import { TagsEditor } from './Tags'

function InputFieldsRenderer({
  fieldId,
  currentLocale,
  isPageSettingsFields = false,
  label = '',
  tooltip = '',
  isRequired = false,
  isDisabled = false,
}: {
  fieldId: string
  currentLocale: string
  isPageSettingsFields?: boolean
  label?: string
  tooltip?: string
  isRequired?: boolean
  isDisabled?: boolean
}) {
  const sdk = useSDK<EditorAppSDK>()

  const sdkFields = sdk.entry.fields

  const fieldValue = sdkFields?.[fieldId]?.getValue()
  const ContentType = sdk?.contentType?.sys?.id
  // const fieldValue = sdkFields?.[fieldId]?.getValue()

  const contentConfiguration = sdkFields?.['configurations']?.getValue() || {}

  const fieldLocales = sdkFields?.[fieldId]?.locales || []

  const isFieldLocalized = fieldLocales.length > 1

  const firstRenderRef = useRef(true)

  useEffect(() => {
    // Check if it's the first render
    if (firstRenderRef.current) {
      firstRenderRef.current = false
      return // Skip the rest of the code on the first render
    }

    if (fieldId !== 'configurations' && isFieldLocalized) {
      const unsubscribe = sdkFields?.[fieldId]?.onValueChanged((value) => {
        if (JSON.stringify(fieldValue) !== JSON.stringify(value)) {
          const x = {
            ...contentConfiguration,
            isTranslated: false,
          }

          sdkFields['configurations'].setValue(x)
          sdk.entry.save()
        }
      })

      // Cleanup function to unsubscribe on component unmount or dependency change
      return () => unsubscribe && unsubscribe()
    }
  }, [sdkFields, fieldId, isFieldLocalized, contentConfiguration, fieldValue])

  if (!sdkFields[fieldId]?.locales.includes(currentLocale)) {
    return <></>
  }

  if (['fieldMapping'].includes(fieldId)) {
    return <></>
  }

  if (sdkFields[fieldId]?.name?.includes("[HIDE_EDIT]")) {
    return <></>
  }

  let conditionalInput = null

  if (
    sdkFields[fieldId]?.validations.length > 0 &&
    sdkFields[fieldId]?.validations?.at(0)?.in?.length > 0
  ) {
    conditionalInput = (
      <DropdownEditor
        locales={sdk.locales}
        field={sdkFields[fieldId].getForLocale(currentLocale)}
      />
    )
  } else if (sdkFields[fieldId].type === 'Symbol') {
    conditionalInput = (
      <SingleLineEditor
        locales={sdk.locales}
        field={sdkFields[fieldId].getForLocale(currentLocale)}
        isDisabled={isDisabled}
      />
    )
  } else if (sdkFields[fieldId].type === 'Object') {
    if (fieldId.includes('htmlAttr') || fieldId.includes('configurations')) {
      conditionalInput = (
        <JsonEditor
          field={sdkFields[fieldId].getForLocale(currentLocale)}
          isInitiallyDisabled={false}
        />
      )
    } else {
      conditionalInput = <Tiptap fieldId={fieldId} locale={currentLocale} />
    }
  } else if (
    sdkFields[fieldId].type === 'Link' &&
    sdkFields[fieldId].linkType === 'Entry'
  ) {
    conditionalInput = (
      <SingleEntryReferenceEditor
        hasCardEditActions={true}
        viewType={'link'}
        sdk={sdk}
        parameters={{
          instance: {
            showCreateEntityAction: true,
            showLinkEntityAction: true,
          },
        }}
        fieldId={fieldId}
      />
    )
  } else if (sdkFields[fieldId].type === 'Array') {
    if (sdkFields[fieldId].items.type === 'Symbol') {
      if (sdkFields[fieldId]?.items?.validations?.[0]?.in?.length > 0) {
        conditionalInput = (
          <CheckboxEditor field={sdkFields[fieldId].getForLocale(currentLocale)} />
        )
      } else {
        conditionalInput = (
          <TagsEditor
            field={sdkFields[fieldId].getForLocale(currentLocale)}
            isInitiallyDisabled
          />
        )
      }
    } else if (sdkFields[fieldId].items.type === 'Link') {
      conditionalInput = (
        <MultipleEntryReferenceEditor
          fieldId={fieldId}
          isInitiallyDisabled
          viewType={'card'}
          sdk={sdk}
          parameters={{
            instance: {
              showCreateEntityAction: true,
              showLinkEntityAction: true,
            },
          }}
          hasCardEditActions={true}
        />
      )
    }
  } else if (sdkFields[fieldId].type === 'Boolean') {
    conditionalInput = (
      <BooleanEditor field={sdkFields[fieldId].getForLocale(currentLocale)} />
    )
  } else if (
    sdkFields[fieldId].type === 'Link' &&
    sdkFields[fieldId].linkType === 'Asset'
  ) {
    conditionalInput = (
      <SingleMediaEditor
        viewType={'card'}
        sdk={sdk}
        parameters={{
          instance: {
            showCreateEntityAction: true,
            showLinkEntityAction: true,
          },
        }}
        fieldId={fieldId}
      />
    )
  } else if (sdkFields[fieldId].type === 'Date') {
    conditionalInput = (
      <DateEditor
        field={sdkFields[fieldId].getForLocale(currentLocale)}
        parameters={
          {
            // instance: {
            //   format: 'dateonly', @TODO: make this dynamic
            // },
          }
        }
      />
    )
  } else if (sdkFields[fieldId].type === 'Integer') {
    conditionalInput = (
      <NumberEditor field={sdkFields[fieldId].getForLocale(currentLocale)} />
    )
  } else {
    conditionalInput = <Text> Not Supported</Text>
  }

  const localeLang = currentLocale.split('-')[0]

  const localeCountry = currentLocale.split('-')[1]

  return (
    <div style={{ marginTop: 15 }}>
      {/* <Text fontColor={'colorPrimary'}>{sdkFields[fieldId].name}</Text> */}
      <div
        className='flex items-center gap-1'
        style={{ margin: '24px 0 10px 0' }}
      >
        <h5 className='flex gap-2 justify-center items-center'>
          {sdkFields?.[fieldId]?.name?.includes("[HIDE]") ? null : sdkFields?.[fieldId]?.name}{' '}
          {HelperText &&
            ContentType &&
            HelperText?.[`${ContentType}`] &&
            fieldId &&
            HelperText?.[`${ContentType}`]?.[`${fieldId}`] && (
              <Tooltip
                placement='auto'
                content={`${HelperText?.[`${ContentType}`]?.[`${fieldId}`]}`}
              >
                <Info />
              </Tooltip>
            )}
        </h5>
        {isFieldLocalized && (
          <>
            <span className='font-semibold'>|</span>
            <span className='font-semibold'>
              {getLocaleFullName(localeLang)}
            </span>
            {(localeCountry === 'CA' || localeCountry === 'DE') && (
              <span className='text-xs'>
                (
                {localeCountry === 'CA'
                  ? 'Canada'
                  : localeCountry === 'DE'
                    ? 'Germany'
                    : ''}
                )
              </span>
            )}
          </>
        )}
      </div>
      <div>
        <ErrorBoundary
          fallback={
            <Note variant='warning'>
              Something went wrong. Contact developer.
            </Note>
          }
        >
          {conditionalInput}
        </ErrorBoundary>
      </div>
    </div>
  )
}

export default InputFieldsRenderer
