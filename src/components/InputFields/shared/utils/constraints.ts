/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * This module parses field validations as a constraint and checks
 * values against that constraint.
 */

import isNumber from 'lodash/isNumber'

import { ValidationType } from '../types'

/**
 * Extracts a validation constraint from a field's validations
 * and returns it as an object of type `ValidationType`.
 *
 * If no size validation is present, a default maximum value
 * of 256 characters for Symbols and 50000 characters for Text
 * is returned.
 *
 * @param validations - A list of validations to parse
 * @param fieldType - The type of the field, either 'Symbol' or 'Text'
 * @returns A `ValidationType` object
 */
export function fromFieldValidations(
  validations: Record<string, any>[] = [],
  fieldType: 'Symbol' | 'Text'
): ValidationType {
  const sizeValidation = validations.find((v) => 'size' in v)
  const size = (sizeValidation && sizeValidation.size) || {}
  const min = size.min
  const max = size.max

  if (isNumber(min) && isNumber(max)) {
    return {
      type: 'min-max',
      min,
      max,
    }
  } else if (isNumber(min)) {
    return {
      type: 'min',
      min,
    }
  } else if (isNumber(max)) {
    return {
      type: 'max',
      max,
    }
  } else {
    return {
      type: 'max',
      max: fieldType === 'Symbol' ? 256 : 50000,
    }
  }
}

/**
 * Creates a function to check if a given length satisfies a specified constraint.
 *
 * The returned function evaluates the length against the constraint type:
 * - 'max': Validates if the length does not exceed the maximum value.
 * - 'min': Validates if the length is at least the minimum value.
 * - 'min-max': Validates if the length is between the minimum and maximum values.
 *
 * @param constraint - The validation constraint object containing type and limits.
 * @returns A function that checks if a length satisfies the constraint.
 */

export function makeChecker(constraint: ValidationType) {
  return function checkConstraint(length: number) {
    if (constraint.type === 'max') {
      return length <= constraint.max
    } else if (constraint.type === 'min') {
      return length >= constraint.min
    } else {
      return length >= constraint.min && length <= constraint.max
    }
  }
}
