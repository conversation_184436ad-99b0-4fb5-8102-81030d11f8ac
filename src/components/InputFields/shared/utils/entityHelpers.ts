import get from 'lodash/get'
import isObject from 'lodash/isObject'
import isString from 'lodash/isString'

import {
  Asset,
  ContentType,
  ContentTypeField,
  Entry,
  File,
} from '../typesEntity'

function titleOrDefault(
  title: string | undefined,
  defaultTitle: string
): string {
  if (!isString(title)) {
    return defaultTitle
  }
  if (title) {
    const trimmedTitle = title.trim()
    if (trimmedTitle.length === 0) {
      return defaultTitle
    }
    return trimmedTitle
  }
  return defaultTitle
}

/**
 * Given an entity and a field ID, returns the value for the given locale code.
 * If the value does not exist in the given locale, it will return the value
 * for the default locale code. If the value does not exist in the default
 * locale, it will return the value of the first locale found in the entity.
 *
 * @param {Object} entity - The entity to get the field value from.
 * @param {string} fieldId - The ID of the field to get the value for.
 * @param {string} localeCode - The locale code to get the value for.
 * @param {string} defaultLocaleCode - The default locale code to get the value from if it does not exist in the given locale.
 * @returns {string|undefined} The value of the field, or undefined if it does not exist in any locale.
 */
export function getFieldValue({
  /**
   * Expects an entity fetched with a flag Skip-Transformation: true
   */
  entity,
  fieldId,
  localeCode,
  defaultLocaleCode,
}: {
  entity: {
    fields: { [key: string]: { [valueKey: string]: string | undefined } }
  }
  fieldId: string
  localeCode: string
  defaultLocaleCode: string
}): string | undefined {
  const values = get(entity, ['fields', fieldId])
  if (!isObject(values)) {
    return
  }

  const firstLocaleCode = Object.keys(values)[0]

  return (
    values[localeCode] || values[defaultLocaleCode] || values[firstLocaleCode]
  )
}

/**
 * Retrieves the title of an asset for a given locale.
 *
 * This function attempts to fetch the title of the asset in the specified locale.
 * If the title is not available in that locale, it falls back to the default locale.
 * If the title is not available in either locale, it returns a default title.
 *
 * @param {Object} params - The parameters object.
 * @param {Asset} params.asset - The asset object from which to retrieve the title.
 * @param {string} params.localeCode - The locale code for which to retrieve the title.
 * @param {string} params.defaultLocaleCode - The default locale code to use as a fallback.
 * @param {string} params.defaultTitle - The default title to return if no title is found.
 * @returns {string} The title of the asset or the default title if none is found.
 */

export function getAssetTitle({
  asset,
  localeCode,
  defaultLocaleCode,
  defaultTitle,
}: {
  asset: Asset
  localeCode: string
  defaultLocaleCode: string
  defaultTitle: string
}) {
  const title = getFieldValue({
    entity: { fields: { title: asset.fields?.title } },
    fieldId: 'title',
    localeCode,
    defaultLocaleCode,
  })
  return titleOrDefault(title, defaultTitle)
}

/**
 * Returns true if field is an Asset
 *
 * @param field
 * @returns {boolean}
 */
export const isAssetField = (field: ContentTypeField): boolean =>
  field.type === 'Link' && field.linkType === 'Asset'

/**
 * Returns true if field is a Title
 */
export function isDisplayField({
  field,
  contentType,
}: {
  field: ContentTypeField
  contentType: ContentType
}): boolean {
  return field.id === contentType.displayField
}

/**
 * Returns true if field is a short Description
 */
export function isDescriptionField({
  field,
  contentType,
}: {
  field: ContentTypeField
  contentType: ContentType
}) {
  const isTextField = (field: ContentTypeField) =>
    ['Symbol', 'Text'].includes(field.type)
  const isMaybeSlugField = (field: ContentTypeField) =>
    /\bslug\b/.test(field.name)
  return (
    isTextField(field) &&
    !isDisplayField({ field, contentType }) &&
    !isMaybeSlugField(field)
  )
}

/**
 * Given an entity and a content type, returns the localized short description
 * for the entity. If the description does not exist in the given locale, it
 * will return the description in the default locale. If the description does not
 * exist in the default locale, it will return an empty string.
 *
 * @param {Object} entity - The entity to get the description for.
 * @param {Object} [contentType] - The content type of the entity.
 * @param {string} localeCode - The locale code to get the description for.
 * @param {string} defaultLocaleCode - The default locale code to get the description from if it does not exist in the given locale.
 * @returns {string} The localized description for the entity.
 */
export function getEntityDescription({
  entity,
  contentType,
  localeCode,
  defaultLocaleCode,
}: {
  entity: Entry
  contentType?: ContentType
  localeCode: string
  defaultLocaleCode: string
}): string {
  if (!contentType) {
    return ''
  }

  const descriptionField = contentType.fields.find((field) =>
    isDescriptionField({ field, contentType })
  )

  if (!descriptionField) {
    return ''
  }

  return (
    getFieldValue({
      entity,
      fieldId: descriptionField.id,
      localeCode,
      defaultLocaleCode,
    }) || ''
  )
}

/**
 * Given an entry and a content type, returns the localized title for the entry.
 * If the title does not exist in the given locale, it will return the title in the default locale.
 * If the title does not exist in the default locale, it will return the value of the "defaultTitle" parameter.
 *
 * If the content type does not have a display field, it will return the value of the "defaultTitle" parameter.
 *
 * @param {Object} entry - The entry to get the title for.
 * @param {Object} [contentType] - The content type of the entry.
 * @param {string} localeCode - The locale code to get the title for.
 * @param {string} defaultLocaleCode - The default locale code to get the title from if it does not exist in the given locale.
 * @param {string} defaultTitle - The title to return if the title does not exist in any locale.
 * @returns {string} The localized title for the entry.
 */
export function getEntryTitle({
  entry,
  contentType,
  localeCode,
  defaultLocaleCode,
  defaultTitle,
}: {
  entry: Entry
  contentType?: ContentType
  localeCode: string
  defaultLocaleCode: string
  defaultTitle: string
}) {
  let title

  if (!contentType) {
    return defaultTitle
  }

  const displayField = contentType.displayField
  if (!displayField) {
    return defaultTitle
  }

  const displayFieldInfo = contentType.fields.find(
    (field) => field.id === displayField
  )

  if (!displayFieldInfo) {
    return defaultTitle
  }

  // when localization for a field is "turned off",
  // we don't clean up the "old" localized data, so it is still in the response.
  // Therefore, we're checking if displayField is localizable.
  if (displayFieldInfo.localized) {
    title = getFieldValue({
      entity: entry,
      fieldId: displayField,
      localeCode,
      defaultLocaleCode,
    })
    if (!title) {
      // Older content types may return id/apiName, but some entry lookup paths do not fetch raw data
      // In order to still return a title in this case, look for displayField as apiName in content type,
      // ...but still look for displayField as a field in the entry
      title = getFieldValue({
        entity: entry,
        fieldId: displayFieldInfo.id,
        localeCode,
        defaultLocaleCode,
      })
    }
  } else {
    title = getFieldValue({
      entity: entry,
      fieldId: displayField,
      defaultLocaleCode,
      localeCode: '',
    })
    if (!title) {
      title = getFieldValue({
        entity: entry,
        fieldId: displayFieldInfo.id,
        defaultLocaleCode,
        localeCode: '',
      })
    }
  }

  return titleOrDefault(title, defaultTitle)
}

/**
 * Given an entity's sys object, returns the status of the entity.
 *
 * @param sys - The sys object of an entity.
 * @returns The status of the entity as a string, either 'draft', 'published', 'changed', 'archived', or 'deleted'.
 *
 * @throws {TypeError} If `sys` is not an object or if `sys.type` is not either 'Entry' or 'Asset'.
 */
export function getEntryStatus(sys: Entry['sys']) {
  if (!sys || (sys.type !== 'Entry' && sys.type !== 'Asset')) {
    throw new TypeError('Invalid entity metadata object')
  }
  if (sys.deletedVersion) {
    return 'deleted'
  } else if (sys.archivedVersion) {
    return 'archived'
  } else if (sys.publishedVersion) {
    if (sys.version > sys.publishedVersion + 1) {
      return 'changed'
    } else {
      return 'published'
    }
  } else {
    return 'draft'
  }
}

/**
 * Gets a promise resolving with a localized asset image field representing a
 * given entities file. The promise may resolve with null.
 */
export const getEntryImage = async (
  {
    entry,
    contentType,
    localeCode,
  }: {
    entry: Entry
    contentType?: ContentType
    localeCode: string
    defaultLocaleCode: string
  },
  getAsset: (assetId: string) => Promise<unknown>
): Promise<null | File> => {
  if (!contentType) {
    return null
  }

  const assetLink = contentType.fields.find(isAssetField)

  if (!assetLink) {
    return null
  }

  const assetId = get(entry.fields, [assetLink.id, localeCode, 'sys', 'id'])

  if (!assetId) {
    return null
  }

  try {
    const asset = await getAsset(assetId)
    const file = get(asset, ['fields', 'file', localeCode])
    const isImage = Boolean(get(file, ['details', 'image'], false))
    return isImage ? file : null
  } catch (e) {
    return null
  }
}
