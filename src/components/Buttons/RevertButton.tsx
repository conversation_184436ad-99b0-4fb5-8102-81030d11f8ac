import { Button, Tooltip } from '@contentful/f36-components'
import React, { CSSProperties } from 'react'
import Revert from '../../assets/icons/Revert'

interface Props {
  onClick: () => void
  helpText?: string
  tooltipPlacement?: 'left' | 'right' | 'top' | 'bottom'
  btnText?: string
  type?: 'secondary' | 'positive'
  isDisabled?: boolean
  isLoading?: boolean
  style?: CSSProperties
}

function RevertButton(props: Props) {
  const {
    onClick,
    helpText,
    tooltipPlacement = 'left',
    btnText,
    type = 'secondary',
    isDisabled = false,
    isLoading = false,
    style,
  } = props

  return (
    <Tooltip
      placement={tooltipPlacement}
      content={helpText || 'Revert Changes'}
      style={style}
    >
      <Button
        variant={type}
        size='small'
        onClick={onClick}
        startIcon={<Revert />}
        isDisabled={isDisabled} // Spread the rest of the props to the Button component
        isLoading={isLoading}
      >
        {btnText}
      </Button>
    </Tooltip>
  )
}

export default RevertButton
