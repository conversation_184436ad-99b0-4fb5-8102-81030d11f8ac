import { Button, Tooltip } from '@contentful/f36-components'
import React from 'react'
import RightArrow from '../../assets/icons/RightArrow'

interface Props {
  onClick: () => void
  helpText?: string
  tooltipPlacement?: 'left' | 'right' | 'top' | 'bottom'
  btnText?: string
  type?: 'secondary' | 'positive' | 'primary'
  isDisabled?: boolean
  isLoading?: boolean
  isIcon?: boolean
}

function NextButton(props: Props) {
  const {
    onClick,
    helpText,
    tooltipPlacement = 'left',
    btnText,
    type = 'secondary',
    isDisabled = false,
    isLoading = false,
    isIcon = true
  } = props

  return (
    <Tooltip placement={tooltipPlacement} content={helpText || 'Next'}>
      <Button
        variant={type}
        size='small'
        onClick={onClick}
        startIcon={
          isIcon ? <RightArrow colour={type === 'secondary' ? '#000' : '#fff'} /> : undefined
        }
        isDisabled={isDisabled} // Spread the rest of the props to the Button component
        isLoading={isLoading}
      >
        {btnText}
      </Button>
    </Tooltip>
  )
}

export default NextButton
