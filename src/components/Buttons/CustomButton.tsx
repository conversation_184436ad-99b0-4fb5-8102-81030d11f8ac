import { Button, ButtonProps, Tooltip } from '@contentful/f36-components'
import React from 'react'

interface CustomButtonProps extends ButtonProps {
  tooltipText?: string
  tooltipPlacement?: 'left' | 'right' | 'top' | 'bottom'
}

const CustomButton: React.FC<CustomButtonProps> = ({
  tooltipText,
  tooltipPlacement = 'left',
  children,
  ...buttonProps
}) => {
  return (
    <Tooltip placement={tooltipPlacement} content={tooltipText}>
      <Button {...buttonProps}>{children}</Button>
    </Tooltip>
  )
}

export default CustomButton
