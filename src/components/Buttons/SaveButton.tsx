import { Button, Tooltip } from '@contentful/f36-components'
import React from 'react'
import Done from '../../assets/icons/Done'
interface Props {
  onClick: () => void
  helpText?: string
  tooltipPlacement?: 'left' | 'right' | 'top' | 'bottom'
  btnText?: string
  isDisabled?: boolean
  isLoading?: boolean
  isIcon?: boolean
}
function SaveButton(props: Props) {
  const {
    onClick,
    helpText,
    tooltipPlacement = 'left',
    btnText,
    isDisabled = false,
    isLoading = false,
    isIcon = true
  } = props

  return (
    <Tooltip placement={tooltipPlacement} content={helpText || 'Save'}>
      <Button
        variant='positive'
        size='small'
        onClick={onClick}
        startIcon={isIcon ? <Done /> : undefined}
        isDisabled={isDisabled} // Spread the rest of the props to the Button component
        isLoading={isLoading}
      >
        {btnText}
      </Button>
    </Tooltip>
  )
}

export default SaveButton
