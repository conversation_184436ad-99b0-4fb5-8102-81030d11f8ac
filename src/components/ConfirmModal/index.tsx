import { Button } from '@contentful/f36-components'
import React from 'react'
import './index.scss'

interface Props {
  children: React.ReactNode
  open: boolean
  handleClose: () => void
  onConfirm: () => void
  title?: string
  loading?: boolean
  isNotification?: boolean
  btn1Text?: string
  btn2Text?: string
}

export default function ModalConfirm(props: Props) {
  const {
    children,
    open,
    handleClose,
    onConfirm,
    title,
    loading,
    isNotification,
    btn1Text,
    btn2Text,
  } = props

  return (
    <>
      {open && (
        <div className='modal'>
          <div className='modal-content'>
            <div className='modal-header'>
              <h4>{title ?? 'Confirm Action'}</h4>
              <span className={`close ${loading ? "pointer-events-none opacity-55": ""}`} onClick={handleClose}>
                &times;
              </span>
            </div>
            <hr
              className='endHorizontalLine'
              style={{
                width: '100%',
              }}
            />
            <div className='modal-body'>{children}</div>
            <div className='modal-footer'>
              {!isNotification && (
                <Button variant='secondary' size='small' onClick={handleClose} isLoading={loading} isDisabled={loading} >
                  {btn1Text ?? 'Cancel'}
                </Button>
              )}
              <Button
                variant='positive'
                size='small'
                isLoading={loading}
                isDisabled={loading}
                onClick={onConfirm}
              >
                {btn2Text ?? 'Confirm'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
