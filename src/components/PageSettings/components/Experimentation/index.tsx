import { EditorAppSDK } from 'contentful-ui-extensions-sdk'
import React, { useContext, useEffect, useState } from 'react'
import { GlobalContext } from '../../../../contexts/globalContext'
import { ALLOWED_DOMAINS } from '../../../../globals/experimentation-util'
import {
  fetchEntryDetails,
  getAssetData,
  getConfigurationByScopeAndType,
} from '../../../../globals/utils'
import { Alert, Spin } from '../../../atoms'
import { experimentationSteps } from '../../../ConfigurationEngine/TabsPanels/Experimentation/experiment-const'
import ExperimentForm from '../../../ConfigurationEngine/TabsPanels/Experimentation/ExperimentForm'
import {
  getDomainShortName,
  getDomainWebsiteName,
} from '../../../Crosspost/utils'
interface Props {
  sdk: EditorAppSDK
  entryId: string
}

const Experimentation = (props: Props) => {
  const { sdk, entryId } = props
  const entryMetadata = sdk.entry.getMetadata()
  const tags = entryMetadata?.tags.map((tag: any) => tag.sys.id)
  const domainTag = tags?.find((tag: any) => tag.startsWith('domain'))
  const selectedDomain = getDomainShortName(domainTag)
  const isDomainAllowed = ALLOWED_DOMAINS?.includes(domainTag)
  const [pageData, setPageData] = useState({})
  const [loading, setloading] = useState(false)
  const [isExperimentRunning, setIsExeprimentRunning] = useState(false)
  const [contentId, setContentId] = useState('')
  const [msaConfigData, setMsaConfigData] = useState<any>({})
  const { currentLocale } = useContext(GlobalContext)
  const [dataloading, setDataLoading] = useState(false)
  const [pagedonothaveSlug, setPageDonothaveSlug] = useState(false)
  const fetchBasePage = async () => {
    setloading(true)
    const detailedEntry = await fetchEntryDetails(entryId)
    const pageThumbnailFull: any = await getAssetData(
      detailedEntry?.fields?.pageThumbnail?.[currentLocale]?.sys?.id
    ).then((res) => res)
    const pageThumbnail = {
      url: pageThumbnailFull?.fields?.file?.[currentLocale].url,
      status: pageThumbnailFull?.sys?.fieldStatus?.['*']?.[currentLocale],
    }
    setPageData({ detailedEntry, selected: detailedEntry, pageThumbnail })
    if (
      detailedEntry?.fields?.isExperimentation?.['en-CA'] &&
      !!detailedEntry?.fields?.experimentationId?.['en-CA']
    ) {
      setIsExeprimentRunning(true)
    } else {
      setIsExeprimentRunning(false)
    }
    if (!detailedEntry?.fields?.slug?.['en-CA']) {
      setPageDonothaveSlug(true)
    } else {
      setPageDonothaveSlug(false)
    }
    setloading(false)
  }
  const type = 'Experimentation'

  const fetchData = async () => {
    setDataLoading(true)
    const configurationData: any = await getConfigurationByScopeAndType(
      type,
      `${selectedDomain}`?.toUpperCase()
    )
    if (configurationData && configurationData?.data && configurationData?.id) {
      setMsaConfigData(configurationData?.data ?? {})
      setContentId(configurationData?.id)
    } else {
      setMsaConfigData({})
      setContentId('')
    }
    setDataLoading(false)
  }
  useEffect(() => {
    if (!isDomainAllowed) {
      setloading(false)
      return
    }
    fetchBasePage()
    fetchData()
  }, [])

  if (!isDomainAllowed) {
    return (
      <Alert
        type='warning'
        message={
          <>
            Experimentation is not supported for{' '}
            <b> {getDomainWebsiteName(domainTag)} </b>. Please select a valid
            domain.
          </>
        }
        showIcon
        style={{ margin: '20px' }}
        className='w-full'
        description={`Supported domains are: ${ALLOWED_DOMAINS.map(
          getDomainWebsiteName
        ).join(', ')}.`}
        closable={false}
        banner
      />
    )
  }

  return loading ? (
    <Spin className='w-full h-full flex justify-center items-center ' />
  ) : pagedonothaveSlug ? (
    <div className='w-full h-full flex justify-center items-center '>
      <Alert
        type='info'
        description='Page do not have any slug. please add slug and try again later.'
        icon
      />
    </div>
  ) : (
    <ExperimentForm
      data={msaConfigData}
      contentId={contentId}
      refetchData={fetchData}
      activeStepinitial={experimentationSteps.detailform}
      isExperimentRunning={isExperimentRunning}
      initialPageData={pageData ?? null}
      selectedDomain={selectedDomain}
      loading={dataloading}
      domainTagFullName={domainTag}
    />
  )
}

export default Experimentation
