// DuplicateModal.tsx
import React, { useCallback, useEffect, useState } from 'react'
import { getEntryDataById } from '../../../../globals/utils'
import EntryPreviewById from '../../../EntryPreview/EntryPreviewById'

interface ComponentReference {
  id: string
}

interface DuplicateModalProps {
  isOpen: boolean
  onClose: () => void
  entryId: string
  isLoading: boolean
  progress: any
  duplicateComponent: (
    entryId: string,
    type: string,
    idsToAvoid: string[]
  ) => Promise<void>
}

const DuplicateModal: React.FC<DuplicateModalProps> = ({
  isOpen,
  onClose,
  entryId,
  isLoading,
  progress,
  duplicateComponent,
}) => {
  const [componentReferences, setComponentReferences] = useState<
    ComponentReference[]
  >([])
  const [selectedIdsToDuplicate, setSelectedIdsToDuplicate] = useState<
    string[]
  >([])

  // Extract references from entry fields
  const extractReferences = (fields: any): ComponentReference[] => {
    const references: ComponentReference[] = []
    const traverseFields = (obj: any) => {
      if (Array.isArray(obj)) {
        obj.forEach(traverseFields)
      } else if (typeof obj === 'object' && obj !== null) {
        if (obj.sys?.type === 'Link' && obj.sys.linkType === 'Entry') {
          references.push({ id: obj.sys.id })
        } else {
          Object.values(obj).forEach(traverseFields)
        }
      }
    }
    traverseFields(fields)
    return references
  }

  const fetchComponentReferences = useCallback(async () => {
    try {
      const entry = await getEntryDataById(entryId)
      const references = extractReferences(entry.fields)
      setComponentReferences(references)
    } catch (error) {
      console.error('Failed to fetch component references:', error)
    }
  }, [entryId])

  useEffect(() => {
    if (entryId && isOpen) {
      fetchComponentReferences()
    }
  }, [entryId, isOpen, fetchComponentReferences])

  const performDuplication = useCallback(async () => {
    if (!entryId) return
    const selectedIdsToAvoid = componentReferences
      .filter(({ id }) => !selectedIdsToDuplicate.includes(id))
      .map((el) => el.id)
    try {
      onClose()
      await duplicateComponent(entryId, 'duplicate', selectedIdsToAvoid)
      console.log('Duplication completed')
      //   onClose()
    } catch (error) {
      console.error('Duplication failed:', error)
    }
  }, [
    entryId,
    duplicateComponent,
    selectedIdsToDuplicate,
    componentReferences,
    onClose,
  ])

  const handleCheckboxChange = (id: string) => {
    setSelectedIdsToDuplicate((prev) =>
      prev.includes(id)
        ? prev.filter((selectedId) => selectedId !== id)
        : [...prev, id]
    )
  }

  if (!isOpen) return null

  return (
    <div className='fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50'>
      <div className='bg-white p-6 rounded-lg shadow-lg w-2/3 h-[80vh] overflow-auto'>
        <h3 className='text-xl mb-4'>Select Components to Duplicate:</h3>
        {componentReferences.map((component) => (
          <div key={component.id} className='flex items-center mb-2'>
            <input
              type='checkbox'
              checked={selectedIdsToDuplicate.includes(component.id)}
              onChange={() => handleCheckboxChange(component.id)}
            />
            <span className='ml-2'>
              <EntryPreviewById entryId={component.id} />
            </span>
          </div>
        ))}
        <div className='flex justify-end mt-4'>
          <button
            onClick={performDuplication}
            disabled={isLoading}
            className='mr-2 px-4 py-2 bg-blue-500 text-white rounded'
          >
            {isLoading ? 'Duplicating...' : 'Duplicate'}
          </button>
          <button onClick={onClose} className='px-4 py-2 bg-gray-300 rounded'>
            Cancel
          </button>
        </div>
        {/* {progress && (
          <div className="mt-4">
            <p className="text-xl text-blue-600">Total Components to Create: {progress?.status?.length || 0}</p>
            <p className="text-xl text-green-500">Components Completed: {progress?.status?.filter((el) => el?.newNestedId)?.length || 0}</p>
          </div>
        )} */}
      </div>
    </div>
  )
}

export default DuplicateModal
