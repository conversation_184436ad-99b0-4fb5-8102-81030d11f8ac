import { EditorAppSDK } from '@contentful/app-sdk'
import { Button } from '@contentful/f36-components'
import React from 'react'
import LinkOut from '../../../../assets/icons/LinkOut'
import { ENV_VARIABLES } from '../../../../constant/variables'
import { getDomainShortName } from '../../../Crosspost/utils'

function PreviewLink({ sdk }: { sdk: EditorAppSDK }) {
  const pageConfigurations =
    sdk.entry.fields?.['configurations']?.getValue() || {}

  let domain = pageConfigurations?.domain

  const getDomainFromPageDomainTag = () => {
    const tags = sdk.entry.getMetadata()?.tags
    const domainArray = tags?.filter((tag) => tag.sys.id.includes('domain'))
    if (domainArray && domainArray?.length > 0) {
      return getDomainShortName(domainArray[0].sys.id)
    }
    return ''
  }

  if (!domain) {
    domain = getDomainFromPageDomainTag()
  }

  const previewUrl = `https://msa-${domain}-v3w-git-${ENV_VARIABLES.MainPreviewBranch}-altus.vercel.app/`

  const slug = sdk.entry.fields.slug.getForLocale('en-CA').getValue()

  const handlePreview = () => {
    window.open(`${previewUrl}${slug}`, '_blank')
  }

  return (
    <Button
      variant='secondary'
      endIcon={<LinkOut />}
      onClick={() => handlePreview()}
    >
      Open Preview
    </Button>
  )
}

export default PreviewLink
