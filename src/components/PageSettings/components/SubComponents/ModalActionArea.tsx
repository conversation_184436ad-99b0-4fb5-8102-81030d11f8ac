import { EditorAppSDK } from '@contentful/app-sdk'
import { Empty } from 'antd'
import React from 'react'
import { ApplyNavDefaultSettings } from '../../utils/NavigationDefaultConf'
import AFS from '../AFS'
import Cache from '../Cache'
import ComponentCloner from '../Duplicator'
import DynamicPage from '../DynamicPage'
import Experimentation from '../Experimentation'
import General from '../General'
import Migrations from '../Migratations'
import Navigation from '../Navigation'
import Search from '../Search'
import SEO from '../SEO'
import Tags from '../Tags'
import Translations from '../Translations'

interface Props {
  activeCategory: string | number
  sdk: EditorAppSDK
  allowedLocale: string[]
  selectedDomain: string
  entryId: string
  pageSettingCategories: any
}

function ModalActionArea({
  activeCategory,
  allowedLocale,
  entryId,
  sdk,
  selectedDomain,
  pageSettingCategories,
}: Props) {
  const componentMap = {
    General: (
      <General sdk={sdk} locale={allowedLocale} domain={selectedDomain} />
    ),
    SEO: <SEO sdk={sdk} locale={allowedLocale} domain={selectedDomain} />,
    Search: <Search sdk={sdk} locale={allowedLocale} domain={selectedDomain} />,
    Navigation: (
      <Navigation sdk={sdk} locale={allowedLocale} domain={selectedDomain} />
    ),
    Translations: (
      <Translations sdk={sdk} pageDomainAllowedLocale={allowedLocale} />
    ),
    Tags: <Tags sdk={sdk} locale={allowedLocale} domain={selectedDomain} />,
    Cache: <Cache sdk={sdk} />,
    Experimentation: <Experimentation sdk={sdk} entryId={entryId} />,
    Duplicator: <ComponentCloner entryId={entryId} />,
    'Dynamic Page': <DynamicPage entryId={entryId} sdk={sdk} />,
    AFS: <AFS sdk={sdk} locale={allowedLocale} domain={selectedDomain} />,
    Migrations: <Migrations />,
  }
  const isAllowded = pageSettingCategories?.find(
    (el) => el?.isEnabled && el?.title == activeCategory
  )
  if (!isAllowded) {
    return (
      <div className='w-full h-full flex justify-center items-center '>
        <Empty />
      </div>
    )
  }
  return (
    <main className={'mainRoot formRootPageSetting'}>
      <div
        className={`pageSettingPreviewRoot ${
          activeCategory === 'Tags' ? 'pl-1' : 'px-4'
        } `}
        style={{
          paddingInline: '0px !important',
        }}
      >
        <ApplyNavDefaultSettings sdk={sdk} />
        {componentMap[activeCategory as keyof typeof componentMap] || null}
      </div>
    </main>
  )
}

export default ModalActionArea
