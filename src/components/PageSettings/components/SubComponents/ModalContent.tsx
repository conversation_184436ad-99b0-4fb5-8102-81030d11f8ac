import { EditorAppSDK } from '@contentful/app-sdk'
import React from 'react'
import ModalActionArea from './ModalActionArea'
import ModalSidebar from './ModalSidebar'

interface Props {
  activeCategory: string | number
  pageSettingCategories: any
  setActiveCategory: any
  allowedLocale: string[]
  entryId: string
  sdk: EditorAppSDK
  selectedDomain: string
  actionHandler: any
}
function ModalContent({
  activeCategory,
  allowedLocale,
  entryId,
  pageSettingCategories,
  sdk,
  selectedDomain,
  setActiveCategory,
  actionHandler,
}: Props) {
  return (
    <div className={'customiserRoot'}>
      <div className={'customiserInnerRoot'}>
        <ModalSidebar
          activeCategory={activeCategory}
          pageSettingCategories={pageSettingCategories}
          setActiveCategory={setActiveCategory}
          actionHandler={actionHandler}
        />
        <ModalActionArea
          activeCategory={activeCategory}
          allowedLocale={allowedLocale}
          entryId={entryId}
          sdk={sdk}
          selectedDomain={selectedDomain}
          pageSettingCategories={pageSettingCategories}
        />
      </div>
    </div>
  )
}

export default ModalContent
