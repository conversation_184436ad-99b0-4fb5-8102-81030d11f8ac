import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import React from 'react'
import { BsGripVertical } from 'react-icons/bs'
import EntryPreviewById from '../../../EntryPreview/EntryPreviewById'

/**
 * SortableItem component for rendering a sortable item in a list.
 * @param id - The unique identifier for the item.
 * @returns The rendered sortable item.
 */
const SortableItem = ({ id }: { id: string }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id })
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    padding: 8,
    border: '1px solid #d9d9d9',
    marginBottom: 8,
    background: '#fff',
    borderRadius: 4,
    display: 'flex',
    alignItems: 'center',
    gap: 8,
  }

  /**
   * Renders the sortable item with a grip icon and entry preview.
   * The item can be dragged and dropped to reorder it within a list.
   * @returns The rendered sortable item.
   * EntryPreviewById component is used to display the entry preview based on the provided id.
   * The grip icon allows users to drag the item.
   * @see EntryPreviewById
   */
  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <span
        {...listeners}
        style={{ cursor: 'grab', padding: 4, display: 'flex' }}
      >
        <BsGripVertical size={20} />
      </span>
      <EntryPreviewById entryId={id} />
    </div>
  )
}
export default SortableItem
