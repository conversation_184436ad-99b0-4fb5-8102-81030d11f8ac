import { Button, message, Steps } from 'antd'
import { EditorAppSDK } from 'contentful-ui-extensions-sdk'
import React, { useEffect, useState } from 'react'
import { getEntryDataById } from '../../../../globals/utils'
import { Spin } from '../../../atoms'
import StepParameterNameValue from './Steps/StepParameterNameValue'
import StepReorder from './Steps/StepReorder'

const { Step } = Steps

interface Props {
  sdk: EditorAppSDK
  entryId: string
}

/**
 * DynamicPage component for managing dynamic page configurations.
 * It allows users to set parameter names, values, and reorder them.
 * @param sdk - The Contentful SDK instance.
 * @param entryId - The ID of the entry being edited.
 * @returns The rendered component.
 */
const DynamicPage = ({ sdk, entryId }: Props) => {
  const [dynamicPageConfiguration, setDynamicPageConfighuration] = useState<
    Record<string, Record<string, string[]>>
  >({})
  const [currentStep, setCurrentStep] = useState(0)
  const [refresh, setRefresh] = useState(false)
  const [parameterName, setParameterName] = useState('')
  const [parameterData, setParameterData] = useState<
    Record<string, Record<string, string[]>>
  >({})
  const [references, setReferences] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [isActiveDynamicPage, setIsActivedynamicPage] = useState(false)
  // Function to fetch entry data and configurations
  useEffect(() => {
    if (!entryId || !sdk) {
      return
    }
    const fetchData = async () => {
      setLoading(true)

      try {
        const [entry, configurations] = await Promise.all([
          getEntryDataById(entryId),
          sdk?.entry?.fields['configurations']?.getValue(),
        ])

        const refs = extractReferences(entry?.fields)
        setReferences(refs?.map((el) => el?.id) ?? [])

        const dynamicPage = configurations?.dynamicpage || {}
        setDynamicPageConfighuration(dynamicPage)
      } catch (err) {
        message.error('Failed to load entry or configurations')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [entryId, sdk, refresh])

  // Function to save the current parameter data
  const save = async () => {
    console.log('Parameter Data:', parameterData)
    const configurations =
      (await sdk.entry.fields['configurations'].getValue()) || {}
    // const existingDynamicPage = configurations.dynamicpage || {}
    const mergedDynamicPage = { ...parameterData }
    // const mergedDynamicPage = { ...existingDynamicPage, ...parameterData }

    await sdk.entry.fields['configurations'].setValue({
      ...configurations,
      dynamicpage: mergedDynamicPage,
    })
    message.success('Saved!')
    setRefresh((prev) => !prev)
  }

  // Steps configuration for the dynamic page
  // Each step corresponds to a part of the dynamic page configuration process
  // The first step is for setting the parameter name and value, the second step is for
  // reordering the parameters, and the third step is for any additional configurations
  // Note: The second and third steps are currently commented out, but they are just combined in the first step
  // You can uncomment them if you want to separate the steps again
  const steps = [
    {
      title: 'Parameter Name',
      content: (
        <StepParameterNameValue
          parameterName={parameterName}
          setParameterName={setParameterName}
          setParameterData={setParameterData}
          dynamicPageConfiguration={dynamicPageConfiguration}
          isActiveDynamicPage={isActiveDynamicPage}
          parameterData={parameterData}
          references={references}
        />
      ),
    },
    // {
    //   title: 'Parameter Name',
    //   content: (
    // <StepParameterName
    //   parameterName={parameterName}
    //   setParameterName={setParameterName}
    //   setParameterData={setParameterData}
    //   dynamicPageConfiguration={dynamicPageConfiguration}
    //   isActiveDynamicPage={isActiveDynamicPage}
    // />
    //   ),
    // },
    // {
    //   title: 'Parameter Values',
    //   content: (
    // <StepParameterValues
    //   parameterName={parameterName}
    //   parameterData={parameterData}
    //   setParameterData={setParameterData}
    //   references={references?.map((el) => el?.id)}
    // />
    //   ),
    // },

    {
      title: 'Reorder',
      content: (
        <StepReorder
          parameterName={parameterName}
          parameterData={parameterData}
          setParameterData={setParameterData}
        />
      ),
    },
  ]

  useEffect(() => {
    // Automatically set the parameter name based on the first non-empty configuration
    const val =
      Object.entries(dynamicPageConfiguration).find(
        ([key, value]) =>
          key &&
          typeof key === 'string' &&
          value &&
          Object.keys(value).length > 0
      )?.[0] || ''
    if (!val || val?.trim()?.length < 1) {
      return
    }
    setParameterName(val)

    const selectedParameter = dynamicPageConfiguration?.[val] || {}

    const updatedGroups: Record<string, string[]> = {}

    const currentRefsSet = new Set(references)

    for (const [groupName, ids] of Object.entries(selectedParameter)) {
      const filtered = ids.filter((id) => currentRefsSet.has(id))
      const additions = references.filter((id) => !filtered.includes(id))
      updatedGroups[groupName] = [...filtered, ...additions]
    }
    if (val && Object.keys(updatedGroups)?.length) {
      setParameterData({
        [val]: updatedGroups,
      })

      setCurrentStep((steps?.length ?? 1) - 1)
      setIsActivedynamicPage(true)
    }
  }, [dynamicPageConfiguration, references, refresh, steps?.length])

  // If loading, show a spinner
  // This is to ensure that the component does not render until the data is fetched
  // This prevents any flickering or incomplete data display
  if (loading) {
    return (
      <div className='w-full h-full flex justify-center items-center'>
        <Spin spinning size='large' />
      </div>
    )
  }

  // Render the dynamic page configuration steps
  // It displays the current step and allows navigation between steps
  // The steps are rendered using the Ant Design Steps component
  // The content of each step is dynamically loaded based on the current step index
  return (
    <div style={{ padding: 16 }} className='w-full'>
      {/* <GroupDataViewer data={parameterData} /> */}
      <Steps current={currentStep}>
        {steps.map((s, i) => (
          <Step key={i} title={s?.title} />
        ))}
      </Steps>

      <div style={{ marginTop: 24 }}>{steps?.[currentStep]?.content}</div>

      <div
        style={{ marginTop: 24 }}
        className='flex justify-end items-center gap-5'
      >
        {currentStep > 0 && (
          <Button onClick={() => setCurrentStep(currentStep - 1)}>Back</Button>
        )}
        {currentStep < steps.length - 1 ? (
          <Button
            type='primary'
            onClick={() => setCurrentStep(currentStep + 1)}
          >
            Next
          </Button>
        ) : (
          <Button type='primary' onClick={save}>
            Save
          </Button>
        )}
      </div>
    </div>
  )
}

export default DynamicPage

/**
 * Extracts references from the fields object of a Contentful entry.
 * This function is used to find all component references within the entry's fields.
 * It returns an array of ComponentReference objects, each containing the ID of a referenced entry.
 * @param fields - The fields object from the entry.
 * This function traverses the fields object recursively to extract all references.
 * It looks for objects with sys.type 'Link' and sys.linkType 'Entry', and
 * collects their IDs into an array of ComponentReference objects.
 * @returns An array of ComponentReference objects.
 */
export const extractReferences = (fields: any): ComponentReference[] => {
  const references: ComponentReference[] = []
  const traverseFields = (obj: any) => {
    if (Array.isArray(obj)) {
      obj.forEach(traverseFields)
    } else if (typeof obj === 'object' && obj !== null) {
      if (obj.sys?.type === 'Link' && obj.sys.linkType === 'Entry') {
        references.push({ id: obj.sys.id })
      } else {
        Object.values(obj).forEach(traverseFields)
      }
    }
  }
  traverseFields(fields)
  return references
}
