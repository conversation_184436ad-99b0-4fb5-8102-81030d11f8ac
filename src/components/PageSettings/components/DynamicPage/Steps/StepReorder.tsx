import {
  closestCenter,
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import { Select } from 'antd'
import React, { useState } from 'react'
import SortableItem from '../SortableItem'

/**
 * StepReorder component for reordering components in a dynamic page.
 * @param parameterName - The name of the parameter to reorder.
 * @param parameterData - The data for the parameter.
 * @param setParameterData - The function to update the parameter data.
 * @returns The rendered component.
 */
const StepReorder = ({
  parameterName,
  parameterData,
  setParameterData,
}: {
  parameterName: string
  parameterData: ParameterDataType
  setParameterData: SetParameterDataType
}) => {
  const parameterValues = Object.keys(parameterData[parameterName] || {})
  const [selectedValue, setSelectedValue] = useState(parameterValues[0])
  const items = parameterData[parameterName]?.[selectedValue] || []

  const sensors = useSensors(useSensor(PointerSensor))

  const handleDragEnd = (event: any) => {
    const { active, over } = event
    if (active.id !== over?.id) {
      const oldIndex = items.indexOf(active.id)
      const newIndex = items.indexOf(over.id)
      const reordered = arrayMove(items, oldIndex, newIndex)

      setParameterData((prev) => ({
        ...prev,
        [parameterName]: {
          ...prev[parameterName],
          [selectedValue]: reordered,
        },
      }))
    }
  }

  return (
    <>
      {/* Render the select dropdown for parameter values */}
      <Select
        style={{ width: 240, marginBottom: 16 }}
        value={selectedValue}
        onChange={(val) => setSelectedValue(val)}
        options={parameterValues.map((val) => ({ label: val, value: val }))}
      />

      {/* Render the sortable context with items */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={items} strategy={verticalListSortingStrategy}>
          {items.map((id) => (
            <SortableItem key={id} id={id} />
          ))}
        </SortableContext>
      </DndContext>
    </>
  )
}

export default StepReorder
