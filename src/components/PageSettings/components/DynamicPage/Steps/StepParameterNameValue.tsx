import React from 'react'
import Step<PERSON>arameterName from './StepParameterName'
import { Divider } from 'antd'
import StepParameterValues from './StepParameterValues'

/**
 * Step for setting the parameter name and value.
 * @param parameterName - The name of the parameter to be set.
 * @param setParameterName - Function to update the parameter name.
 * @param setParameterData - Function to reset the parameter data.
 * @returns
 * This component renders a step in the dynamic page configuration process.
 * @view StepParameterName for setting the parameter name.
 * @view StepParameterValues for setting the parameter values.
 */
const StepParameterNameValue = ({
  parameterName,
  setParameterName,
  setParameterData,
  dynamicPageConfiguration,
  isActiveDynamicPage,
  parameterData,
  references,
}: {
  parameterName: string
  isActiveDynamicPage: boolean
  setParameterName: (val: string) => void
  dynamicPageConfiguration: ParameterDataType
  setParameterData: SetParameterDataType
  parameterData: ParameterDataType
  references: string[]
}) => {
  return (
    <>
      <Divider></Divider>
      <StepParameterName
        parameterName={parameterName}
        setParameterName={setParameterName}
        setParameterData={setParameterData}
        dynamicPageConfiguration={dynamicPageConfiguration}
        isActiveDynamicPage={isActiveDynamicPage}
      />
      <Divider />
      <StepParameterValues
        parameterName={parameterName}
        parameterData={parameterData}
        setParameterData={setParameterData}
        references={references}
      />
    </>
  )
}

export default StepParameterNameValue
