import React from 'react'
import { Input } from '../../../../atoms'

/**
 *
 * @param parameterName - The name of the parameter to be set.
 * @param setParameterName - Function to update the parameter name.
 * @param setParameterData - Function to reset the parameter data.
 * @param isActiveDynamicPage - <PERSON>olean indicating if the dynamic page is active.
 * This component renders an input field for the parameter name.
 * It allows users to enter a name for the parameter, which is used in dynamic pages.
 * If the dynamic page is active, the input field is disabled to prevent changes.
 * When the parameter name is changed, it resets the parameter data to an empty object.
 * @returns
 */
const StepParameterName = ({
  parameterName,
  setParameterName,
  setParameterData,
  dynamicPageConfiguration,
  isActiveDynamicPage,
}: {
  parameterName: string
  isActiveDynamicPage: boolean
  setParameterName: (val: string) => void
  dynamicPageConfiguration: ParameterDataType
  setParameterData: SetParameterDataType
}) => {
  return (
    <>
      <Input
        placeholder='Enter parameter name (e.g. page)'
        value={parameterName}
        onChange={(e) => {
          setParameterName(e.target.value)
          setParameterData({})
        }}
        disabled={isActiveDynamicPage}
      />
      {isActiveDynamicPage && (
        <p className='text-xs text-gray-500 mt-1'>
          This field is disabled because campaigns rely on this parameter and
          changes could affect their accuracy.
        </p>
      )}
    </>
  )
}

export default StepParameterName
