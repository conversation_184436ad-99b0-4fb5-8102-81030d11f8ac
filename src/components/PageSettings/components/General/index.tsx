import { EditorAppSDK } from '@contentful/app-sdk'
import { Box, Checkbox, TextInput } from '@contentful/f36-components'
import { Tooltip } from 'antd'
import React, { useContext, useEffect, useState } from 'react'
import { BiInfoCircle } from 'react-icons/bi'
import { GlobalContext } from '../../../../contexts/globalContext'
import { getLocaleFullName } from '../../../Crosspost/utils'
import { DateEditor } from '../../../Date'
import { DropdownEditor } from '../../../InputFields/Dropdown'
import {
  MultipleEntryReferenceEditor,
  SingleMediaEditor,
} from '../../../InputFields/Reference'
import {
  CharCounter,
  CharValidation,
  ConstraintsUtils,
} from '../../../InputFields/shared'
import { SingleLineEditor } from '../../../InputFields/SingleLine'
import * as styles from '../../../InputFields/SingleLine/styles'
import FormControlComp from '../../../Shared/FormControlComp'
import { DYNAMIC_PAGE_PREFIX } from '../DynamicPage/utils'

interface Props {
  sdk: EditorAppSDK
  locale: string[]
  domain: string
}

function General(props: Props) {
  const { sdk, locale, domain } = props

  const { currentLocale } = useContext(GlobalContext)

  const [values, setValues] = useState<any>({})

  const sdkFields = sdk.entry.fields
  const isExperimentRunning =
    sdkFields?.['isExperimentation'].getForLocale('en-CA').getValue() &&
    sdkFields?.['experimentationId'].getForLocale('en-CA').getValue()

  const template = sdkFields['template'].getForLocale('en-CA').getValue()

  /**
   * Handler for debounced change of slug field. 
   * For AGL, the slugs are same, so we update both at the same time whenever the change is detetced in EN-CA
   * For others, we just save the slug for the locale that was changed
   * @param {string} value - New slug value
   * @param {string} locale - Locale to update
   */
  const handleDebouncedChange = async (value: string, locale: string) => {
    if (domain === 'agl' && locale === 'en-CA') {
      sdk.entry.fields['slug'].getForLocale('en-CA').setValue(value)
      sdk.entry.fields['slug'].getForLocale('fr-CA').setValue(value)
      setValues({
        ...values,
        'en-CA': value,
        'fr-CA': value,
      })
    } else {
      sdk.entry.fields['slug'].getForLocale(locale).setValue(value)
      setValues({
        ...values,
        [locale]: value,
      })
    }
    sdk.entry.save()
  }

/**
 * Updates the configurations field when a field value changes, ensuring the change is reflected.
 * If the new value differs from the current value, it updates the configurations object to set
 * isTranslated to false and saves the entry.
 *
 * @param {any} value - The new value to set for the field.
 * @param {string} fieldId - The ID of the field being updated.
 * @param {string} locale - The locale for which the field value is being changed.
 */

  const handleFieldValueChangeToUpdateConfigurations = (
    value: any,
    fieldId: string,
    locale: string
  ) => {
    const fieldValue = sdkFields[fieldId].getForLocale(locale).getValue()

    const isValueSame = JSON.stringify(fieldValue) === JSON.stringify(value)

    if (isValueSame) return

    let pageConfig = sdkFields?.['configurations'].getValue()

    pageConfig = {
      ...pageConfig,
      isTranslated: false,
    }

    sdkFields?.['configurations'].setValue(pageConfig)

    sdk.entry.save()
  }

  // For AGL, check if FR-CA slugs is empty, if so, copy from EN-CA and save
  useEffect(() => {
    if (sdk) {
      let x = locale
        ?.map((locale) => {
          return {
            [locale]: sdk.entry.fields['slug'].getForLocale(locale).getValue(),
          }
        })
        .reduce((acc, val) => ({ ...acc, ...val }), {})

      if (domain === 'agl') {
        const frSlug = sdk.entry.fields['slug'].getForLocale('fr-CA').getValue()

        if (!frSlug) {
          const enSlug = sdk.entry.fields['slug']
            .getForLocale('en-CA')
            .getValue()

          if (!enSlug) return

          sdk.entry.fields['slug'].getForLocale('fr-CA').setValue(enSlug)

          x = {
            ...x,
            'fr-CA': enSlug,
          }

          sdk.entry.save()
        }
      }

      setValues(x)
    }
  }, [domain, locale, sdk])

  const constraints = ConstraintsUtils.fromFieldValidations(
    sdk.entry.fields['slug'].validations,
    'Symbol'
  )

  const checkConstraint = ConstraintsUtils.makeChecker(constraints)
  const slugField = sdk.entry.fields['slug']
  const [isPageDynamic, setIsPageDynamic] = useState<boolean>(false)
  const [refresh, setRefresh] = useState(false)

  useEffect(() => {
    const isPageDynamicFlag = locale.every((locale) => {
      const slug = slugField?.getValue(locale) || ''
      return getNormalizedSlug(slug)?.startsWith(DYNAMIC_PAGE_PREFIX)
    })
    setIsPageDynamic(isPageDynamicFlag)
  }, [locale, refresh, slugField])
  // useEffect(() => {
  //   if (domain === 'agl') {
  //     sdk.entry.fields['slug'].getForLocale('en-CA').onValueChanged((x) => {
  //       sdk.entry.fields['slug'].getForLocale('fr-CA').setValue(x)
  //     })
  //   }
  // }, [sdk])
  const handleToggleDynamic = async (checked: boolean) => {
    console.log(JSON.stringify({ locale }, null, 2))

    const pr = locale.map(async (loc) => {
      const slugFieldPerLocale = sdkFields?.['slug']?.getForLocale(loc)

      if (!slugFieldPerLocale) {
        console.warn(`Locale '${loc}' not found or field not localized.`)
        return
      }

      const currentSlug = slugFieldPerLocale.getValue() || ''
      const normalized = getNormalizedSlug(currentSlug)

      if (checked) {
        // Add prefix if not already present
        if (!normalized?.startsWith(DYNAMIC_PAGE_PREFIX)) {
          const updatedValue = `${DYNAMIC_PAGE_PREFIX}/${normalized}`
          await slugFieldPerLocale.setValue(updatedValue)
        }
      } else {
        // Remove prefix if present
        if (normalized?.startsWith(DYNAMIC_PAGE_PREFIX)) {
          const withoutPrefix = normalized.replace(
            new RegExp(`^${DYNAMIC_PAGE_PREFIX}/?`),
            ''
          )
          await slugFieldPerLocale.setValue(withoutPrefix)
        }
      }
    })

    await Promise.all(pr)

    // Force a UI refresh (especially if isPageDynamic is derived from field)
    setRefresh((prev) => !prev)
  }

  return (
    <Box className='w-full h-auto overflow-y-auto flex gap-10 p-3'>
      <Box className='w-full flex justify-start items-start flex-col gap-5'>
        <FormControlComp
          label='Page Template'
          tooltip={`Select a relevant template to set the page's structure and layout.`}
          isRequired={sdkFields?.['template'].required}
        >
          <DropdownEditor
            locales={sdk.locales}
            field={sdkFields?.['template'].getForLocale('en-CA')}
          />
        </FormControlComp>

        <FormControlComp
          label='Publish Date'
          tooltip={`Select the page's publication date and time.`}
          isRequired={sdkFields?.['publishDate'].required}
        >
          <DateEditor
            field={sdkFields?.['publishDate'].getForLocale('en-CA')}
            parameters={{}}
          />
        </FormControlComp>

        <Box className='flex items-center justify-start w-full gap-1.5 pt-1.5'>
          <Checkbox
            isChecked={isPageDynamic}
            onChange={(e) => handleToggleDynamic(e.target.checked)}
            className='p-0'
          >
            is Dynamic Page ?
          </Checkbox>
          <Tooltip
            placement='right'
            title={`slug will be prefixed with "${DYNAMIC_PAGE_PREFIX}"`}
          >
            <BiInfoCircle />
          </Tooltip>
        </Box>
        {/***
         * comment here regarding slug field
         * slug and chcekbox will not update relatime due to sdk entry fields contenfull limation you might need to change and then exit and go to another tab and come backe it will reflect
         *
         *
         * make this here in grey text for user
         *
         *
         */}
        <p className='text-sm text-gray-500'>
          <span className='font-semibold'>Note:</span> Due to a Contentful
          limitation, the slug may not update immediately after checking the
          box. To refresh it, close the modal, switch tabs, then return.
        </p>

        {locale.map((locale) => {
          return (
            <FormControlComp
              label={`Slug (${locale.split('-')[0].toUpperCase()})`}
              tooltip={`Add the ${getLocaleFullName(
                locale.split('-')[0]
              )} version of the page slug.`}
              isRequired={locale === 'en-CA' && sdkFields?.['slug']?.required}
              key={'slug-' + locale}
            >
              <TextInput
                value={values[locale] || ''}
                onChange={(e) => handleDebouncedChange(e.target.value, locale)}
                isDisabled={Boolean(
                  Boolean(domain === 'agl' && locale === 'fr-CA') ||
                  Boolean(isExperimentRunning)
                )}
              />
              <div className={styles.validationRow}>
                <CharCounter
                  value={values[locale] || ''}
                  checkConstraint={checkConstraint}
                />
                <CharValidation constraints={constraints} />
              </div>
            </FormControlComp>
          )
        })}
        {/* {domain === 'agl' && (
          <p className='text-sm text-gray-500'>
            Slug will be auto-populated for French based on English slug
          </p>
        )}
        {isExperimentRunning && (
          <p className='text-sm text-gray-500'>
            Slug is disabled while experiment is running
          </p>
        )}
        {isPageDynamic && (
          <p className='text-sm text-gray-500'>
            Slug will be prefixed with {DYNAMIC_PAGE_PREFIX}
          </p>
        )} */}

        <FormControlComp
          label={`Canonical URL`}
          isRequired={sdkFields?.['canonicalUrl']?.required}
          tooltip='Add the canonical slug of the preferred version of the page to avoid duplicate content.'
        >
          <SingleLineEditor
            locales={sdk.locales}
            field={sdkFields['canonicalUrl'].getForLocale('en-CA')}
            onValueChange={handleFieldValueChangeToUpdateConfigurations}
          />
        </FormControlComp>
      </Box>
      <Box className='w-full flex justify-start items-start flex-col gap-5'>
        {currentLocale === 'en-CA' && (
          <FormControlComp
            label={`Thumbnail Image`}
            tooltip='Add the image that will be used to generate thumbnail previews when sharing this page URL.'
            className='items-center'
            isRequired={sdkFields?.['pageThumbnail']?.required}
          >
            <SingleMediaEditor
              viewType={'card'}
              sdk={sdk}
              parameters={{
                instance: {
                  showCreateEntityAction: true,
                  showLinkEntityAction: true,
                },
              }}
              fieldId={'pageThumbnail'}
            />
          </FormControlComp>
        )}
        {(template === 'Insight Article' || template === 'Press Release') && (
          <>
            {locale.map((locale) => (
              <FormControlComp
                label={`Author Heading (${locale.split('-')[0].toUpperCase()})`}
                tooltip={`Add the ${getLocaleFullName(
                  locale.split('-')[0]
                )} version of the author heading.`}
                key={locale}
                isRequired={
                  locale === 'en-CA' && sdkFields?.['authorsHeading']?.required
                }
              >
                <SingleLineEditor
                  locales={sdk.locales}
                  field={sdkFields['authorsHeading'].getForLocale(locale)}
                  onValueChange={handleFieldValueChangeToUpdateConfigurations}
                />
              </FormControlComp>
            ))}
            {currentLocale === 'en-CA' && (
              <FormControlComp
                label={`Authors`}
                tooltip='Add the author cards for this page.'
                isRequired={sdkFields?.['authors']?.required}
              >
                <MultipleEntryReferenceEditor
                  fieldId={'authors'}
                  isInitiallyDisabled
                  viewType={'card'}
                  sdk={sdk}
                  parameters={{
                    instance: {
                      showCreateEntityAction: true,
                      showLinkEntityAction: true,
                    },
                  }}
                  hasCardEditActions={true}
                />
              </FormControlComp>
            )}
          </>
        )}
      </Box>
    </Box>
  )
}

export default General

export const getNormalizedSlug = (slug: unknown): string => {
  if (typeof slug !== 'string') return ''
  return slug.startsWith('/') ? slug.slice(1) : slug
}
