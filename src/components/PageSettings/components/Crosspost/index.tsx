import { <PERSON>, Button } from '@contentful/f36-components'
import React, { useContext, useEffect, useState } from 'react'
import { GlobalContext } from '../../../../contexts/globalContext'
import { CrosspostJson } from '../../../../globals/types'
import CrosspostDetailTable from './CrosspostDetailTable.tsx'

function CrossPostSummary({ jsonData }: { jsonData: CrosspostJson }) {
  const { isCrossPostingModalOpen, setIsCrossPostingModalOpen } =
    useContext(GlobalContext)

  const [globalJson, setGlobalJson] = useState<any>({})

  const isCrossPostingDataAvailable = Object.keys(globalJson).length > 0

  useEffect(() => {
    jsonData && Object.keys(jsonData).length > 0 && setGlobalJson(jsonData)
  }, [jsonData])

  return (
    <div
      className={`flex flex-col w-full justify-start pt-3 gap-5 items-start`}
    >
      <Box
        className={`flex w-full  ${
          isCrossPostingDataAvailable
            ? 'justify-between items-center'
            : 'flex-col gap-5 justify-start items-start'
        }`}
      >
        {isCrossPostingDataAvailable ? (
          <div>
            <h6
              style={{
                marginTop: 0,
                marginBottom: 4,
              }}
            >
              Summary
            </h6>

            <p className='text-sm max-w-[70%]'>
              Cross posting has been setup for this page and has been summarised
              below, detailing the website and relevant slugs that have been
              setup.
            </p>
          </div>
        ) : (
          <div>
            <h6
              style={{
                marginTop: 0,
                marginBottom: 4,
              }}
            >
              No Data Available
            </h6>

            <p className='text-sm'>This Page hasn't been crossposted yet.</p>
          </div>
        )}
        <Button
          variant='primary'
          size='medium'
          onClick={() => setIsCrossPostingModalOpen(!isCrossPostingModalOpen)}
        >
          {isCrossPostingDataAvailable ? 'Edit' : 'Start'} Crossposting
        </Button>
      </Box>
      {isCrossPostingDataAvailable && (
        <CrosspostDetailTable jsonData={globalJson} />
      )}
      <div className='h-10' />
    </div>
  )
}

export default CrossPostSummary
