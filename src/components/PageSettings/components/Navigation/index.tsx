import { EditorAppSDK } from '@contentful/app-sdk'
import { Box, Checkbox, Tooltip } from '@contentful/f36-components'
import React, { useContext, useEffect, useState } from 'react'
import Info from '../../../../assets/icons/Info'
import { GlobalContext } from '../../../../contexts/globalContext'

interface Props {
  domain: string
  sdk: EditorAppSDK
  locale: string[]
}

function Navigation(props: Props) {
  const { sdk } = props

  const sdkFields = sdk.entry.fields

  const { currentLocale } = useContext(GlobalContext)

  const [values, setValues] = useState<any>({})

  /**
   * Given a field name, returns the field value for English locale.
   * We're using English locale here because it's the source of truth for navigation settings.
   * @param {string} fieldName - The name of the field.
   * @returns {any} The value of the field.
   */
  const getFieldValueByFieldName = (fieldName: string) =>
    sdkFields[fieldName].getForLocale('en-CA').getValue()

  /**
   * Handles the change of a checkbox field and updates the entry.
   * The method also marks the `isDefaultNavUpdated` flag as true if it was the first time the user updates the navigation settings.
   * @param {string} fieldName The name of the field.
   * @param {any} value The checked state of the checkbox.
   */
  const handleCheckBox = (fieldName: string, value: any) => {
    sdkFields[fieldName].getForLocale(currentLocale).setValue(value)

    setValues((prev: any) => {
      return {
        ...prev,
        [fieldName]: value,
      }
    })

    // if user manually updates the nav settings for the first time, mark it as updated
    const pageConf = sdk.entry.fields['configurations'].getValue() || {}

    sdk.entry.fields['configurations'].setValue({
      ...pageConf,
      isDefaultNavUpdated: true,
    })

    sdk.entry.save()
  }

  // Fetch the field data for each field and set the state
  useEffect(() => {
    if (sdk) {
      const fieldNames = [
        'isHeaderNavigationHidden',
        'isTranslucent',
        'isLightBgImage',
        'isFooterNavigationHidden',
        'isNavLightMode',
      ]

      const updateValues = () => {
        const updatedValues = fieldNames.reduce((acc, fieldName) => {
          const value = sdk.entry.fields[fieldName]
            .getForLocale('en-CA')
            .getValue()
          return { ...acc, [fieldName]: value }
        }, {})

        setValues(updatedValues)
      }

      // Initial fetch of data
      updateValues()

      // SDK's built-in method to listen for change in the field value
      const unsubscribeFunctions = fieldNames.map((fieldName) =>
        sdk.entry.fields[fieldName].getForLocale('en-CA').onValueChanged(() => {
          updateValues()
        })
      )

      // Cleanup all subscriptions on unmount
      return () => {
        unsubscribeFunctions.forEach((unsubscribe) => unsubscribe())
      }
    }
  }, [sdk, setValues])

  return (
    <Box className='w-full h-auto overflow-y-auto flex gap-10 p-3'>
      <Box className='w-full h-auto overflow-y-auto flex flex-col gap-10 p-3'>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['isHeaderNavigationHidden']}
            onChange={(e) =>
              handleCheckBox(
                'isHeaderNavigationHidden',
                !values?.['isHeaderNavigationHidden']
              )
            }
            className='p-0'
          >
            Hide Header Navigation
          </Checkbox>
          <Tooltip
            placement='right'
            content='Enable to hide the header navigation on this page.'
          >
            <Info />
          </Tooltip>
        </Box>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['isTranslucent']}
            onChange={(e) =>
              handleCheckBox('isTranslucent', !values?.['isTranslucent'])
            }
            className='p-0'
          >
            Is Navigation Translucent?
          </Checkbox>
          <Tooltip
            placement='right'
            content='Enable to set the navigation as translucent on this page.'
          >
            <Info />
          </Tooltip>
        </Box>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['isLightBgImage']}
            onChange={(e) =>
              handleCheckBox('isLightBgImage', !values?.['isLightBgImage'])
            }
            className='p-0'
          >
            Is light background image?
          </Checkbox>
          <Tooltip
            placement='right'
            content='Enable to display the CTA button as blue, otherwise, it will be yellow.'
          >
            <Info />
          </Tooltip>
        </Box>
      </Box>
      <Box className='w-full h-auto overflow-y-auto flex flex-col gap-10 p-3'>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['isFooterNavigationHidden']}
            onChange={(e) =>
              handleCheckBox(
                'isFooterNavigationHidden',
                !values?.['isFooterNavigationHidden']
              )
            }
            className='p-0'
          >
            Hide Footer Navigation
          </Checkbox>
          <Tooltip
            placement='right'
            content='Enable to hide the footer on this page.'
          >
            <Info />
          </Tooltip>
        </Box>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['isNavLightMode']}
            onChange={(e) =>
              handleCheckBox('isNavLightMode', !values?.['isNavLightMode'])
            }
            className='p-0'
          >
            Is Navigation Light Mode?
          </Checkbox>
          <Tooltip
            placement='right'
            content={
              'Enable to display the logo and text in black, otherwise, they will be white.'
            }
          >
            <Info />
          </Tooltip>
        </Box>
      </Box>
    </Box>
  )
}

export default Navigation
