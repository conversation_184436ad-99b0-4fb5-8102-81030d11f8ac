import { Notification } from '@contentful/f36-components'
import axios from 'axios'
import { ENV_VARIABLES } from '../../../../../constant/variables'

/**
 * Updates the robots.txt content for a given domainCode.
 *
 * @param {string} newContent The new content for the robots.txt file.
 * @param {string} domainCode The domain code to update (e.g. "altusgroup-com").
 */
export const updateRobotsTxt = async (newContent: any, domainCode: string) => {
  let url = `${ENV_VARIABLES.appBaseURL}/api/update-robots/`
  const x = url.split('-')
  x[1] = domainCode
  url = x.join('-')
  try {
    const response = await axios.post(
      url,
      { content: newContent },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )

    if (response.status === 200)
      Notification.success('robots.txt updated successfully')
    else Notification.error('Failed to update robots.txt')
  } catch (error) {
    console.error('Error updating robots.txt:', error)
    Notification.error('Failed to update robots.txt')
  }
}
