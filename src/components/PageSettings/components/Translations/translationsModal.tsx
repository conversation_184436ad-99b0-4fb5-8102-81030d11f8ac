/* eslint-disable react-hooks/rules-of-hooks */
import { EditorAppSDK, SidebarAppSDK } from '@contentful/app-sdk'
import { Box, Modal } from '@contentful/f36-components'
import React, { useContext } from 'react'
import Translations from '.'
import { GlobalContext } from '../../../../contexts/globalContext'
import { EntityProvider } from '../../../InputFields/Reference'
import './index.scss'

interface Props {
  sdk: EditorAppSDK | SidebarAppSDK
  pageDomainAllowedLocale: string[]
}

/**
 * Renders a modal for managing translations within a page setting context.
 * Utilizes the Contentful SDK to interact with the application and provides
 * a user interface for translation-related actions. The modal is controlled
 * via a global context to determine its open or closed state.
 *
 * @param {Props} props - The properties object containing configuration and state handlers.
 * @param {EditorAppSDK | SidebarAppSDK} props.sdk - The SDK instance for interacting with Contentful.
 * @param {string[]} props.pageDomainAllowedLocale - An array of allowed locales for the page domain.
 */

function TranslationsModal(props: Props) {
  const { sdk, pageDomainAllowedLocale } = props

  const { isTranslationModalOpen, setIsTranslationModalOpen } =
    useContext(GlobalContext)

  const handleClose = () => {
    setIsTranslationModalOpen(false)
  }

  return (
    <EntityProvider sdk={sdk}>
      <Modal
        onClose={() => handleClose()}
        isShown={isTranslationModalOpen}
        shouldCloseOnOverlayClick={false}
        shouldCloseOnEscapePress={false}
        className='translationsModal'
      >
        {() => (
          <>
            <Modal.Header title='' onClose={() => handleClose()}>
              <Box className='flex items-center justify-start w-full'>
                <p className='flex w-full justify-start items-start gap-1 pl-1'>
                  <span className='text-lg font-semibold'>Translations </span>
                </p>
              </Box>
            </Modal.Header>

            <Modal.Content className='translationsModalContent'>
              <Translations
                sdk={sdk}
                pageDomainAllowedLocale={pageDomainAllowedLocale}
              />
            </Modal.Content>
          </>
        )}
      </Modal>
    </EntityProvider>
  )
}

export default TranslationsModal
