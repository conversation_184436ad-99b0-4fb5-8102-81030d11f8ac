import { EditorAppSDK, SidebarAppSDK } from '@contentful/app-sdk'
import { Box, Button, Checkbox, Notification } from '@contentful/f36-components'
import _ from 'lodash'
import React, { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { Languages, publishEntry, updateSingleEntryData } from '../../../../globals/utils'
import { useTabVisibility } from '../../../../hooks/useTabVisibility'
import { RootState } from '../../../../redux/store'
import UpcomingFeatComp from '../../../ConfigurationEngine/Components/UpcomingFeatComp'
import { domainsConfig, getLocaleFullName } from '../../../Crosspost/utils'
import FormControlComp from '../../../Shared/FormControlComp'
import '../../index.scss'
import TranslationStatusTable from './components/TranslationStatusTable'
import { extractEntryIds, generateEntryKeyValueFromFields, getEnFieldData, translateData } from './utils'

interface Props {
  sdk: EditorAppSDK | SidebarAppSDK
  pageDomainAllowedLocale: string[]
}

interface Status {
  entryId: string
  internalName: string
  status: 'pending' | 'done' | 'error' | 'N/A'
  type: string
  message?: string
}

/**
 * Translations Component - Advanced translation management interface for Contentful entries
 *
 * This component provides comprehensive translation functionality for page content and nested
 * components. It supports multiple languages, real-time status tracking, and intelligent
 * handling of different content types and field structures.
 *
 * Key Features:
 * - Multi-language translation with domain-specific locale support
 * - Recursive translation of nested content references
 * - Real-time translation status tracking with detailed progress display
 * - Content type filtering (excludes layout components, includes translatable content)
 * - Field-level translation control with localization awareness
 * - Batch translation processing with parallel API calls
 * - Publishing workflow for translated nested components
 * - Error handling and retry capabilities
 * - Integration with external translation API service
 *
 * @param props - Component props containing SDK instance and domain locale configuration
 */
function Translations(props: Props) {
  const { sdk, pageDomainAllowedLocale } = props

  const sdkFields = sdk.entry.fields
  const isTabFocused = useTabVisibility()

  // Extract domain configuration from entry
  const pageConfigurations = sdkFields?.['configurations']?.getValue() || {}
  const domainCode = pageConfigurations?.domain || 'agl'

  // Get domain-specific settings including primary language
  const selectedDomainObj = domainsConfig.find(
    (item) => item?.key === domainCode,
  )
  const primaryLang = selectedDomainObj?.primaryLang || 'en-CA'

  // UI state management
  const [loading, setLoading] = useState(false)
  const [rePublishLoading, setRePublishLoading] = useState(false)
  const [notificationToShow, setNotificationToShow] = useState({
    show: false,
    message: '',
    type: 'success',
  })

  // Translation configuration state
  const [selectedLocale, setSelectedLocale] = useState<string[]>(['en-CA'])
  const [entryTranslationStatus, setEntryTranslationStatus] = useState<
    Status[]
  >([])

  // Processing tracking to prevent infinite loops and duplicate processing
  let processedEntryIds: string[] = []
  const referenceIds: Set<string> = new Set([])

  // Content type filtering configuration
  // These content types are excluded from recursive translation processing
  const contentTypesToExclude = ['page', 'componentForm']

  // These content types don't require translation (layout/structural components)
  const contentTypesWithNoTranslation = [
    'componentLayoutRow',
    'componentLayoutColumn',
    'componentLayoutContainer',
    'componentNavigationHeader',
    'componentLogo',
    'formSection',
  ]

  const tableContainerRef: any = useRef(null)

  const allContentTypes: any = useSelector(
    (state: RootState) => state.pageData.contentTypes,
  )

  /**
   * Updates the translation status for a specific entry in the status tracking array
   *
   * @param x - Status object containing entry ID, name, status, type, and optional message
   *
   * This function maintains a real-time status display by:
   * - Finding existing status entries by entry ID
   * - Updating existing entries or adding new ones
   * - Triggering UI re-render to show current translation progress
   * - Preserving status history for user feedback and debugging
   */
  const updateStatus = (x: Status) => {
    setEntryTranslationStatus((prevState) => {
      const oldData = [...prevState] // Ensure you're working with the latest state

      const index = oldData.findIndex((el) => el.entryId === x.entryId)
      if (index !== -1) oldData[index] = x
      else oldData.push(x)

      return oldData
    })
  }

  /**
   * Toggles locale selection for translation targets
   *
   * @param x - Locale code to toggle (e.g., 'fr-CA', 'de-DE')
   *
   * Manages the list of target languages for translation by:
   * - Adding locale if not currently selected
   * - Removing locale if already selected
   * - Maintaining array of selected locales for batch translation
   */
  const handleOnclick = (x: string) => {
    if (selectedLocale.includes(x))
      setSelectedLocale(selectedLocale.filter((locale) => locale !== x))
    else setSelectedLocale([...selectedLocale, x])
  }

  /**
   * Adds entry IDs to the reference tracking set for recursive processing
   *
   * @param ids - Array of Contentful entry IDs to track
   *
   * This function builds a comprehensive list of all entries that need translation
   * by tracking references found during content tree traversal. Uses a Set to
   * prevent duplicate processing of the same entry.
   */
  const updateReferenceIds = (ids: string[]) =>
    ids.forEach((id) => referenceIds.add(id))

  /**
   * Translates all the entries in the tree rooted at the current entry.
   * This includes the current entry itself, as well as any entries referenced in its fields.
   * The translation is done in the order of the tree traversal, depth-first.
   * The translation status of each entry is kept track of in the `entryTranslationStatus` state.
   * The function also takes care of updating the Contentful entry with the translated data.
   * When the translation is done, it displays a notification with the result.
   */
  const handleClick = async () => {
    setNotificationToShow({ show: false, message: '', type: 'success' })
    setEntryTranslationStatus([])
    referenceIds.clear()
    setLoading(true)

    const entryId = sdk.entry.getSys().id

    updateReferenceIds([entryId])

    const localEntryTranslationStatus: Status[] = []

    for (let i = 0; i < referenceIds.size; i++) {
      const array = Array.from(referenceIds)

      const id = array[i]
      await handleNestedTranslation(
        array[i],
        id === entryId,
        localEntryTranslationStatus,
      )
    }

    sdk.entry.save()
    setLoading(false)

    const isFailed = localEntryTranslationStatus.some((x: any) => {
      return x.status === 'error'
    })

    if (isFailed) {
      setNotificationToShow({
        show: true,
        message: 'Some Nested Entries are not translated successfully',
        type: 'error',
      })
    } else {
      setNotificationToShow({
        show: true,
        message: 'All Nested Entries are translated successfully',
        type: 'success',
      })
    }

    referenceIds.clear()
    processedEntryIds = []
  }

  /**
   * Recursively translates the entries in the tree rooted at the given entry ID.
   * If the entry has already been processed, it simply returns without doing anything.
   * If the entry has content type that is in the `contentTypesToExclude` array and is not the root page,
   * it simply returns without doing anything.
   * It fetches the entry from the Contentful API and then translates the content.
   * The translation is done by calling the `translateData` function for each locale in the `selectedLocale` array.
   * After the translation is done, it updates the Contentful entry with the translated data.
   * It also keeps track of the status of the translation in the `localEntryTranslationStatus` state.
   * If the entry has content type that is in the `contentTypesWithNoTranslation` array,
   * it simply sets the status to 'N/A' and returns true.
   * If the entry is already translated for the selected locale, it simply sets the status to 'done' and returns true.
   * @param {string} entryId - The ID of the entry to translate.
   * @param {boolean} isRootPage - Whether the entry is the root page.
   * @param {Status[]} localEntryTranslationStatus - The status of the translation of the entries in the tree rooted at the given entry ID.
   */
  const handleNestedTranslation = async (
    entryId: string,
    isRootPage: boolean,
    localEntryTranslationStatus: Status[],
  ) => {
    if (checkIsEntryProcessed(entryId)) return

    const entry = await getEnFieldData(entryId)

    const contentType = entry?.sys?.contentType?.sys?.id || ''

    if (contentTypesToExclude.includes(contentType) && !isRootPage) return

    let entryFields = entry?.fields

    const internalName = entryFields?.internalName

    let p: Status = {
      entryId,
      internalName,
      status: 'pending',
      type: contentType,
    }

    updateStatus(p)

    const entryContentModel = allContentTypes?.items?.find(
      (item: any) => item?.sys?.id === contentType,
    )

    const newEntries = generateEntryKeyValueFromFields(entryFields)

    const contentConfigurations = entryFields?.['configurations'] || {}

    const isContentTranslated = contentConfigurations?.isTranslated || false

    const translatedFor = contentConfigurations?.translatedFor || []

    const nestedReferenceId = extractEntryIds(entry)

    updateReferenceIds(nestedReferenceId)

    if (contentTypesWithNoTranslation.includes(contentType)) {
      p = {
        ...p,
        status: 'N/A',
      }
      updateStatus(p)
      localEntryTranslationStatus.push(p)

      return true
    }

    if (
      isContentTranslated &&
      JSON.stringify(translatedFor) === JSON.stringify(selectedLocale)
    ) {
      p = {
        ...p,
        status: 'done',
      }
      updateStatus(p)
      localEntryTranslationStatus.push(p)

      return true
    }

    const translationPromises = selectedLocale.map(async (locale) => {
      if (locale === 'en-CA') return // Skip 'en-CA'

      return await translateData(
        locale,
        newEntries,
        entryFields,
        entryId,
        entryContentModel,
      )
    })
    let res: any[]
    let finalRes = false
    let errorMsg = ''
    try {
      res = await Promise.all(translationPromises.filter(Boolean))
      res = res.filter((x) => x !== undefined)

      finalRes = res.every((x) => x.success === true)

      errorMsg = res[0].message

      await updateSingleEntryData(entryId, [
        {
          fieldId: 'configurations',
          locale: 'en-CA',
          value: {
            ...contentConfigurations,
            isTranslated: finalRes,
            translatedFor: selectedLocale,
          },
        },
      ])
    } catch (e) { }

    entryTranslationStatus.pop()

    p = {
      ...p,
      status: finalRes ? 'done' : 'error',
      message: errorMsg,
    }

    updateStatus(p)
    localEntryTranslationStatus.push(p)

    return finalRes
  }

  /**
   * Checks if an entry with the given ID has already been processed.
   *
   * This prevents an entry from being processed more than once.
   * @param {string} id The ID of the entry.
   * @returns {boolean} true if the entry has already been processed, false otherwise.
   */
  const checkIsEntryProcessed = (id: string) => {
    if (processedEntryIds.includes(id)) return true
    else {
      processedEntryIds.push(id)

      return false
    }
  }

  /**
   * Handles the republish action.
   *
   * It clears the previously processed entries and starts the translation process from the beginning.
   * It updates the UI to show the translation status of each entry.
   * If there's an error while publishing any of the entries, it shows an error notification.
   */
  const handleRepublish = async () => {
    setNotificationToShow({ show: false, message: '', type: 'success' })
    referenceIds.clear()
    processedEntryIds = []
    setEntryTranslationStatus([])
    setRePublishLoading(true)
    const localEntryPublishStatus: Status[] = []

    const entryId = sdk.entry.getSys().id

    updateReferenceIds([entryId])

    for (let i = 0; i < referenceIds.size; i++) {
      const array = Array.from(referenceIds)
      const id = array[i]
      await handleNestedPublish(
        array[i],
        id === entryId,
        localEntryPublishStatus,
      )
    }

    setRePublishLoading(false)

    const isFailed = localEntryPublishStatus.some((x) => {
      return x.status === 'error'
    })

    if (isFailed) {
      setNotificationToShow({
        show: true,
        message: 'Some Nested Entries are not published successfully',
        type: 'error',
      })
    } else {
      setNotificationToShow({
        show: true,
        message: 'All Nested Entries are published successfully',
        type: 'success',
      })
    }
  }

  /**
   * Recursively publishes all the entries in the tree rooted at the given entryId.
   * The entryId is the ID of the root entry of the tree.
   * The function also takes care of updating the UI to show the translation status of each entry.
   * If there's an error while publishing any of the entries, it shows an error notification.
   * @param {string} entryId The ID of the root entry of the tree.
   * @param {boolean} isRootPage Whether the entry is a root page or not.
   * @param {Status[]} localEntryPublishStatus An array of Status objects representing the status of each entry in the tree.
   */
  const handleNestedPublish = async (
    entryId: string,
    isRootPage: boolean,
    localEntryPublishStatus: Status[],
  ) => {
    if (checkIsEntryProcessed(entryId)) return

    const entry = await getEnFieldData(entryId)

    const contentType = entry?.sys?.contentType?.sys?.id || ''

    const nestedReferenceId = extractEntryIds(entry)

    if (contentType === 'page' || contentType === 'componentForm') {
      if (isRootPage) updateReferenceIds(nestedReferenceId)
      return
    }

    updateReferenceIds(nestedReferenceId)

    let entryFields = entry?.fields

    const internalName = entryFields?.internalName

    let p: Status = {
      entryId,
      internalName,
      status: 'pending',
      type: contentType,
    }

    updateStatus(p)

    let res
    res = await publishEntry(entryId)

    p = {
      ...p,
      status: res ? 'done' : 'error',
    }

    updateStatus(p)
    localEntryPublishStatus.push(p)
  }

  // Once the Bulk action is completed, we show a toast notification only when window tab is active.
  useEffect(() => {
    if (notificationToShow.show && isTabFocused) {
      if (notificationToShow.type === 'success') {
        Notification.success(notificationToShow.message)
      } else {
        Notification.error(notificationToShow.message)
      }

      setNotificationToShow({ show: false, message: '', type: 'success' })
    }
  }, [notificationToShow, isTabFocused])

  // If the primary language is not selected, we add it to the selectedLocale array.
  useEffect(() => {
    if (!primaryLang) return

    if (!selectedLocale.includes(primaryLang)) {
      setSelectedLocale([...selectedLocale, primaryLang])
    }
  }, [primaryLang])

  // If the page has already been translated, we select the previously translated locale.
  useEffect(() => {
    const previousTranslatedLocale = pageConfigurations?.translatedFor || []

    if (previousTranslatedLocale.length > 0) {
      setSelectedLocale(previousTranslatedLocale)
    } else {
      setSelectedLocale(pageDomainAllowedLocale)
    }
  }, [pageDomainAllowedLocale])

  return (
    <Box className="flex w-full p-3 h-full">
      <Box className="w-1/3 h-full flex flex-col gap-5">
        <Box className="flex items-start lg:w-4/5 w-full">
          <FormControlComp
            label="Primary Language"
            tooltip="This is the source language of the selected domain."
          >
            <p className="font-bold text-lg">
              {getLocaleFullName(primaryLang?.split('-')?.[0])} ({primaryLang})
            </p>
          </FormControlComp>
        </Box>
        <FormControlComp
          label="Translate to..."
          tooltip="Select the target languages you wish to translate the content into."
        >
          {Languages.map((locale) => (
            <Checkbox
              isDisabled={locale.value === 'en-CA'}
              isChecked={selectedLocale?.includes(locale?.value)}
              onChange={() => handleOnclick(locale?.value)}
              className='py-2'
              key={locale.value}
            >
              {locale?.title}
            </Checkbox>
          ))}
        </FormControlComp>
        <Box className="flex flex-row items-center justify-start gap-5">
          <Button
            variant="primary"
            onClick={handleClick}
            isLoading={loading}
            isDisabled={
              loading ||
              rePublishLoading ||
              (selectedLocale.length === 1 && selectedLocale[0] === 'en-CA')
            }
            className="w-32"
          >
            Translate
          </Button>
          <Button
            className="w-32"
            variant="secondary"
            onClick={handleRepublish}
            isLoading={rePublishLoading}
            isDisabled={rePublishLoading || loading}
          >
            Republish all
          </Button>
        </Box>
        <Box className="pb-5">
          <UpcomingFeatComp
            bgColor="#ffeea4"
            borderColor="#bdd8ff"
            color="#000000"
            title=""
            subtitle="Warning"
            desc="Republish all will only publish the nested components, and not the current page or form and it's sub component."
          />
        </Box>
      </Box>
      {!_.isEmpty(entryTranslationStatus) && (
        <Box
          className="w-2/3 min-w-[700px] h-full flex flex-col gap-8 overflow-y-auto max-h-[80vh]"
          ref={tableContainerRef}
        >
          <TranslationStatusTable data={entryTranslationStatus} />
        </Box>
      )}
    </Box>
  )
}

export default Translations
