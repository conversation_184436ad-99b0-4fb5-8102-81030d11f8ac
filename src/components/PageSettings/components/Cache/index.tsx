import { <PERSON>AppSDK } from '@contentful/app-sdk'
import { Box, Button } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { ENV_VARIABLES } from '../../../../constant/variables'
import { getBranchDomainByTag, getEntryFieldData } from '../../../../globals/utils'
import { getUpdatedBranchFrontName } from '../../utils'

interface Props {
  sdk: EditorAppSDK
}

function Cache(props: Props) {
  const sdk = props.sdk
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState<string>('')
  const [success, setSuccess] = useState<string | null>(null)

  // const [isOpen, setIsOpen] = useState(false)

  // const [slugs, setSlugs] = useState<string[]>([])
  // const [revalidationDomain, setRevalidationDomain] = useState<string | null>(
  //   null
  // )

  const entryMetadata = sdk.entry.getMetadata()
  const tags = entryMetadata?.tags.map((tag: any) => tag.sys.id)
  const domainTag = tags?.find((tag: any) => tag.startsWith('domain'))

  const validDomaintags = [
    'domainAltusGroupCom',
    'domainVerifinoCom',
    'domainReonomyCom',
    'domainFinanceActiveCom',
    'domainRethinkCom',
    'domainOne11Com',
    'domainForburyCom',
    'domainAgStudio',
  ]

  const localesByDomain: { [k: string]: Array<string> } = {
    domainAltusGroupCom: ['en-CA', 'fr-CA'],
    domainFinanceActiveCom: ['en-CA', 'fr-CA', 'de-DE', 'es', 'nl', 'it'],
    domainVerifinoCom: ['en-CA', 'de-DE'],
    domainOne11Com: ['en-CA', 'fr-CA'],
    domainReonomyCom: ['en-CA'],
    domainForburyCom: ['en-CA'],
    domainAgStudio: ['en-CA'],
  }
  /*
       To use the cma, inject it as follows.
       If it is not needed, you can remove the next line.
    */
  // const cma = useCMA();

  // Define an async function to process each locale and return the slug
  async function fetchAndProcessSlug(locale: string) {
    let contentfulEnvironment = sdk.entry.getSys().environment.sys.id
    if (contentfulEnvironment === 'master') {
      contentfulEnvironment = 'v3'
    }
    const entryData = await getEntryFieldData({
      environment: contentfulEnvironment,
      entryId: sdk.entry.getSys().id,
      locale: locale,
    })

    let slug = entryData?.slug || ''

    // Append "/fr" to the slug for the "fr-CA" locale under the "domainAltusGroupCom" domain
    const isFrenchCanadian =
      domainTag === 'domainAltusGroupCom' && locale === 'fr-CA'
    if (isFrenchCanadian) {
      slug += '/fr'
    }

    // Ensure the slug starts with a single "/" and does not have multiple consecutive "/"
    return `/${slug}`.replace(/\/+/g, '/')
  }

  async function revalidateCacheBySlugs(domainTag: string, branch = 'main') {
    const slugsToRevalidate = await Promise.all(
      localesByDomain[domainTag]?.map(fetchAndProcessSlug) || []
    )

    // if (domainTag === 'domainAltusGroupCom') {
    //   // replace /fr at the end, with /?lang=fr for altus
    //   const updatedSlugs = slugsToRevalidate.map((slug) => {
    //     if (slug.endsWith('/fr')) {
    //       return slug.replace('/fr', '/?lang=fr')
    //     }
    //     return slug
    //   })
    //   setSlugs(updatedSlugs)
    // } else {
    //   setSlugs(slugsToRevalidate)
    // }

    let vercelUrl = getBranchDomainByTag(domainTag, branch)

    // setRevalidationDomain(vercelUrl)

    if (vercelUrl) {
      setLoading(branch)
      fetch(`${vercelUrl}/api/revalidate/page/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          slugs: slugsToRevalidate.flatMap((singleSlug) => [
            singleSlug,
            singleSlug.endsWith('/')
              ? singleSlug.slice(0, -1)
              : singleSlug + '/',
          ]),
        }),
      })
        .then((res) => res.json())
        .then((data) => {
          if (data.revalidated) {
            setError(null)
            setSuccess('Page updated successfully')
            console.log('Page revalidation succeeded', data)
          } else {
            setError('Page revalidation failed')
            console.error('Page revalidation failed', data)
          }
        })
        .catch((err) => {
          setError('Page revalidation failed')
          console.error('Page revalidation failed', err)
        })
        .finally(() => {
          setLoading('')
        })
    } else {
      setError('Vercel revalidation URL not found')
    }
  }

  function revalidate(branch = 'main') {
    setError(null)
    setSuccess(null)

    if (!domainTag) {
      setError('Domain tag not found')
      return
    } else if (!validDomaintags.includes(domainTag)) {
      setError('Invalid domain tag')
      return
    }

    revalidateCacheBySlugs(domainTag, branch)
  }

  useEffect(() => {
    if (success || error) {
      setTimeout(() => {
        setSuccess(null)
        setError(null)
      }, 5000)
    }
  }, [success, error])

  return (
    <Box className='flex flex-col gap-3'>
      <h2
        style={{
          width: '100%',
          marginBottom: '0.5rem',
          fontSize: '0.75rem',
          display: 'flex',
          justifyContent: 'space-between',
          borderBottom: '1px solid rgb(174, 193, 204)',
          alignItems: 'center',
          fontWeight: 600,
          color: '#67728a',
          lineHeight: '1.5rem',
        }}
      >
        Flush Cache
      </h2>
      <Button
        variant='secondary'
        className='w-full'
        isDisabled={loading === ENV_VARIABLES?.MainPreviewBranch}
        isLoading={loading === ENV_VARIABLES?.MainPreviewBranch}
        onClick={() => revalidate(ENV_VARIABLES?.MainPreviewBranch)}
      >
        Flush for {getUpdatedBranchFrontName(ENV_VARIABLES?.MainPreviewBranch)}
      </Button>
      <Button
        variant='secondary'
        className='w-full'
        isDisabled={loading === ENV_VARIABLES.mainBranch}
        isLoading={loading === ENV_VARIABLES.mainBranch}
        onClick={() => revalidate(ENV_VARIABLES.mainBranch)}
      >
        Flush for {ENV_VARIABLES?.mainBranch}
      </Button>
      <div>
        <p>
          {error && <span className='text-red-500'>{error}</span>}
          {success && <span className='text-green-500'>{success}</span>}
        </p>
      </div>
    </Box>
  )
}

export default Cache
