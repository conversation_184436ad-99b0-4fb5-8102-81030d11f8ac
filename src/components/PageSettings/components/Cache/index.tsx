import { <PERSON>AppSDK } from '@contentful/app-sdk'
import { Box, Button } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { ENV_VARIABLES } from '../../../../constant/variables'
import { getBranchDomainByTag, getEntryFieldData } from '../../../../globals/utils'
import { getUpdatedBranchFrontName } from '../../utils'

interface Props {
  sdk: EditorAppSDK
}

/**
 * Cache Component - Provides cache invalidation functionality for Contentful page entries
 *
 * This component allows content editors to flush/revalidate cached content on Vercel deployments
 * when page content is updated. It handles multi-locale cache invalidation and domain-specific
 * URL generation for different brands/websites.
 *
 * Key Features:
 * - Supports multiple domains (Altus Group, Finance Active, Verifino, etc.)
 * - Handles locale-specific cache invalidation for multilingual content
 * - Provides separate flush options for staging and production branches
 * - Automatically detects domain from entry metadata tags
 * - Shows loading states and success/error feedback
 * - Special handling for French Canadian locale on Altus Group domain
 *
 * @param props - Component props containing Contentful SDK instance
 */
function Cache(props: Props) {
  const sdk = props.sdk
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState<string>('')
  const [success, setSuccess] = useState<string | null>(null)

  // const [isOpen, setIsOpen] = useState(false)

  // const [slugs, setSlugs] = useState<string[]>([])
  // const [revalidationDomain, setRevalidationDomain] = useState<string | null>(
  //   null
  // )

  // Extract domain information from entry metadata tags
  const entryMetadata = sdk.entry.getMetadata()
  const tags = entryMetadata?.tags.map((tag: any) => tag.sys.id)
  const domainTag = tags?.find((tag: any) => tag.startsWith('domain'))

  // List of valid domain tags that support cache invalidation
  const validDomaintags = [
    'domainAltusGroupCom',
    'domainVerifinoCom',
    'domainReonomyCom',
    'domainFinanceActiveCom',
    'domainRethinkCom',
    'domainOne11Com',
    'domainForburyCom',
    'domainAgStudio',
  ]

  // Mapping of domain tags to their supported locales for cache invalidation
  // Each domain has different locale requirements based on their target markets
  const localesByDomain: { [k: string]: Array<string> } = {
    domainAltusGroupCom: ['en-CA', 'fr-CA'],
    domainFinanceActiveCom: ['en-CA', 'fr-CA', 'de-DE', 'es', 'nl', 'it'],
    domainVerifinoCom: ['en-CA', 'de-DE'],
    domainOne11Com: ['en-CA', 'fr-CA'],
    domainReonomyCom: ['en-CA'],
    domainForburyCom: ['en-CA'],
    domainAgStudio: ['en-CA'],
  }
  /*
       To use the cma, inject it as follows.
       If it is not needed, you can remove the next line.
    */
  // const cma = useCMA();

  /**
   * Fetches and processes slug for a specific locale to prepare for cache invalidation
   *
   * @param locale - The locale code to fetch slug for (e.g., 'en-CA', 'fr-CA')
   * @returns Promise<string> - Processed slug with proper formatting and locale-specific modifications
   *
   * Process:
   * 1. Determines correct Contentful environment (maps 'master' to 'v3')
   * 2. Fetches entry field data for the specific locale
   * 3. Applies domain-specific slug modifications:
   *    - Altus Group French Canadian: appends '/fr' suffix
   * 4. Ensures proper slug formatting (single leading slash, no consecutive slashes)
   */
  async function fetchAndProcessSlug(locale: string) {
    let contentfulEnvironment = sdk.entry.getSys().environment.sys.id
    if (contentfulEnvironment === 'master') {
      contentfulEnvironment = 'v3'
    }
    const entryData = await getEntryFieldData({
      environment: contentfulEnvironment,
      entryId: sdk.entry.getSys().id,
      locale: locale,
    })

    let slug = entryData?.slug || ''

    // Append "/fr" to the slug for the "fr-CA" locale under the "domainAltusGroupCom" domain
    // This handles Altus Group's specific URL structure for French content
    const isFrenchCanadian =
      domainTag === 'domainAltusGroupCom' && locale === 'fr-CA'
    if (isFrenchCanadian) {
      slug += '/fr'
    }

    // Ensure the slug starts with a single "/" and does not have multiple consecutive "/"
    return `/${slug}`.replace(/\/+/g, '/')
  }

  /**
   * Performs cache invalidation for all locales of a domain on a specific branch
   *
   * @param domainTag - Domain identifier (e.g., 'domainAltusGroupCom')
   * @param branch - Deployment branch to invalidate cache for (default: 'main')
   *
   * Process:
   * 1. Fetches and processes slugs for all supported locales of the domain
   * 2. Gets the appropriate Vercel deployment URL for the domain and branch
   * 3. Sends revalidation request to Next.js API with all locale-specific slugs
   * 4. Updates UI state based on success/failure of cache invalidation
   * 5. Provides user feedback through success/error messages
   */
  async function revalidateCacheBySlugs(domainTag: string, branch = 'main') {
    const slugsToRevalidate = await Promise.all(
      localesByDomain[domainTag]?.map(fetchAndProcessSlug) || []
    )

    // if (domainTag === 'domainAltusGroupCom') {
    //   // replace /fr at the end, with /?lang=fr for altus
    //   const updatedSlugs = slugsToRevalidate.map((slug) => {
    //     if (slug.endsWith('/fr')) {
    //       return slug.replace('/fr', '/?lang=fr')
    //     }
    //     return slug
    //   })
    //   setSlugs(updatedSlugs)
    // } else {
    //   setSlugs(slugsToRevalidate)
    // }

    let vercelUrl = getBranchDomainByTag(domainTag, branch)

    // setRevalidationDomain(vercelUrl)

    if (vercelUrl) {
      setLoading(branch)
      fetch(`${vercelUrl}/api/revalidate/page/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          slugs: slugsToRevalidate.flatMap((singleSlug) => [
            singleSlug,
            singleSlug.endsWith('/')
              ? singleSlug.slice(0, -1)
              : singleSlug + '/',
          ]),
        }),
      })
        .then((res) => res.json())
        .then((data) => {
          if (data.revalidated) {
            setError(null)
            setSuccess('Page updated successfully')
            console.log('Page revalidation succeeded', data)
          } else {
            setError('Page revalidation failed')
            console.error('Page revalidation failed', data)
          }
        })
        .catch((err) => {
          setError('Page revalidation failed')
          console.error('Page revalidation failed', err)
        })
        .finally(() => {
          setLoading('')
        })
    } else {
      setError('Vercel revalidation URL not found')
    }
  }

  function revalidate(branch = 'main') {
    setError(null)
    setSuccess(null)

    if (!domainTag) {
      setError('Domain tag not found')
      return
    } else if (!validDomaintags.includes(domainTag)) {
      setError('Invalid domain tag')
      return
    }

    revalidateCacheBySlugs(domainTag, branch)
  }

  useEffect(() => {
    if (success || error) {
      setTimeout(() => {
        setSuccess(null)
        setError(null)
      }, 5000)
    }
  }, [success, error])

  return (
    <Box className='flex flex-col gap-3'>
      <h2
        style={{
          width: '100%',
          marginBottom: '0.5rem',
          fontSize: '0.75rem',
          display: 'flex',
          justifyContent: 'space-between',
          borderBottom: '1px solid rgb(174, 193, 204)',
          alignItems: 'center',
          fontWeight: 600,
          color: '#67728a',
          lineHeight: '1.5rem',
        }}
      >
        Flush Cache
      </h2>
      <Button
        variant='secondary'
        className='w-full'
        isDisabled={loading === ENV_VARIABLES?.MainPreviewBranch}
        isLoading={loading === ENV_VARIABLES?.MainPreviewBranch}
        onClick={() => revalidate(ENV_VARIABLES?.MainPreviewBranch)}
      >
        Flush for {getUpdatedBranchFrontName(ENV_VARIABLES?.MainPreviewBranch)}
      </Button>
      <Button
        variant='secondary'
        className='w-full'
        isDisabled={loading === ENV_VARIABLES.mainBranch}
        isLoading={loading === ENV_VARIABLES.mainBranch}
        onClick={() => revalidate(ENV_VARIABLES.mainBranch)}
      >
        Flush for {ENV_VARIABLES?.mainBranch}
      </Button>
      <div>
        <p>
          {error && <span className='text-red-500'>{error}</span>}
          {success && <span className='text-green-500'>{success}</span>}
        </p>
      </div>
    </Box>
  )
}

export default Cache
