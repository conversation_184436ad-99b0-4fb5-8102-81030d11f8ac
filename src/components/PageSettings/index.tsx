/* eslint-disable react-hooks/exhaustive-deps */
import { EditorAppSDK } from '@contentful/app-sdk'
import { Box, Modal } from '@contentful/f36-components'
import React, { useContext, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import Info from '../../assets/icons/Info'
import logo from '../../assets/page_settings.png'
import { GlobalContext } from '../../contexts/globalContext'
import { getConfigurationsCollectionQuery } from '../../globals/queries'
import { fetchGraphQL } from '../../globals/utils'
import {
  fetchPageDataById,
  getAllCategorizedTags,
  getContentTypesForEnvironment,
} from '../../redux/slices/pageSettings'
import { RootState, useAppDispatch } from '../../redux/store'
import { Tooltip } from '../atoms'
import { getDomainShortName } from '../Crosspost/utils'
import { EntityProvider } from '../InputFields/Reference'
import AFS from './components/AFS'
import Cache from './components/Cache'
import ComponentCloner from './components/Duplicator'
import DynamicPage from './components/DynamicPage'
import { DYNAMIC_PAGE_PREFIX } from './components/DynamicPage/utils'
import Experimentation from './components/Experimentation'
import General from './components/General'
import Navigation from './components/Navigation'
import Search from './components/Search'
import SEO from './components/SEO'
import Tags from './components/Tags'
import Translations from './components/Translations'
import './index.scss'
import { ApplyNavDefaultSettings } from './utils/NavigationDefaultConf'

interface PageSettingsModalProps {
  sdk: EditorAppSDK
  entryId: string
}

function PageSettingsModal(props: PageSettingsModalProps) {
  const { entryId, sdk } = props

  const sdkFields = sdk.entry.fields

  const pageConfigurations = sdkFields?.['configurations']?.getValue() || {}

  let domainCode = pageConfigurations?.domain || ''

  const entryCrossPostData = useSelector(
    (state: RootState) => state.pageData.crossPostData
  )

  // get domain code from page domain tag
  const getDomainFromPageDomainTag = () => {
    const tags = sdk.entry.getMetadata()?.tags
    const domainArray = tags?.filter((tag) => tag.sys.id.includes('domain'))
    if (domainArray && domainArray?.length > 0)
      return getDomainShortName(domainArray[0].sys.id)

    return ''
  }

  if (!domainCode) {
    // if domain code not found in page config then get it from page domain tag
    domainCode = getDomainFromPageDomainTag()
  }

  const [selectedDomain, setSelectedDomain] = useState<string>('')

  const [isCloseClicked, setIsCloseClicked] = useState(false)

  const { isPageSettingModalOpen, setIsPageSettingModalOpen } =
    useContext(GlobalContext)

  const [activeCategory, setActiveCategory] = useState<string | number>(
    'General'
  )

  const [allowedLocale, setAllowedLocale] = useState<string[]>(['en-CA'])

  const slugField = sdk.entry.fields['slug']
  // const isPageDynamic = normalizedSlug?.startsWith(DYNAMIC_PAGE_PREFIX)
  const isPageDynamic = allowedLocale?.every((locale) => {
    const slug = slugField.getValue(locale) || ''
    return getNormalizedSlug(slug).startsWith(DYNAMIC_PAGE_PREFIX)
  })
  const dispatch = useAppDispatch()

  const isClosable = useSelector(
    (state: RootState) => state.pageData.isPageSettingsModalClosable
  )

  // func to set if modal is closable or not (curr using for tag validations)
  const handleClose = () => {
    if (isClosable) {
      setIsPageSettingModalOpen(false)
      sdk.entry.save()
    }
    setIsCloseClicked(!isClosable)
  }

  // fetch allowed locale by select domain code
  const fetchData = async (domainCode: string) => {
    const res = await fetchGraphQL(getConfigurationsCollectionQuery()).then(
      (res: any) => res?.data?.configurationsCollection?.items
    )

    const matchedData = res?.find((item: any) => {
      return (
        item?.scope === domainCode?.toUpperCase() && item?.type === 'Locale'
      )
    })?.data?.json?.content?.[0]?.content?.[0]?.value

    if (!matchedData) return

    const data = JSON.parse(matchedData)

    setAllowedLocale(data?.allowedLanguages || ['en-CA'])
  }

  useEffect(() => {
    setIsCloseClicked(!isClosable)
  }, [isClosable])

  useEffect(() => {
    selectedDomain && fetchData(selectedDomain)
  }, [selectedDomain])

  useEffect(() => {
    dispatch(fetchPageDataById(entryId))
    dispatch(getAllCategorizedTags())
    dispatch(getContentTypesForEnvironment())
  }, [])

  useEffect(() => {}, [sdk])

  useEffect(() => {
    domainCode && setSelectedDomain(domainCode)
  }, [domainCode])

  const pageSettingCategories = [
    {
      title: 'General',
      value: 1,
      isEnabled: true,
    },
    {
      title: 'SEO',
      value: 2,
      isEnabled: true,
    },
    {
      title: 'AFS',
      value: 3,
      isEnabled: true,
    },
    {
      title: 'Search',
      value: 4,
      isEnabled: true,
    },
    {
      title: 'Navigation',
      value: 5,
      isEnabled: true,
    },
    {
      title: 'Translations',
      value: 6,
      isEnabled: true,
    },
    {
      title: 'Tags',
      value: 7,
      isEnabled: true,
    },
    {
      title: 'Cache',
      value: 8,
      isEnabled: false,
    },
    {
      title: 'Crosspost',
      value: 9,
      isEnabled: false,
    },
    {
      title: 'Experimentation',
      value: 10,
      isEnabled: true,
    },
    {
      title: 'Duplicator',
      value: 11,
      isEnabled: true,
    },
    {
      title: 'Migrations',
      value: 12,
      isEnabled: false,
    },
    {
      title: 'Dynamic Page',
      isEnabled: true,
      isDisabled: !isPageDynamic,
      disabledtooltip:
        'Dynamic Page settings are unavailable. Please check the "Is Dynamic Page" checkbox in the General Settings.',
      type: 'category',
    },
  ]

  return (
    <EntityProvider sdk={sdk}>
      <Modal
        onClose={() => handleClose()}
        isShown={isPageSettingModalOpen}
        shouldCloseOnOverlayClick={false}
        shouldCloseOnEscapePress={false}
        className='pageSettingModal'
      >
        {() => (
          <>
            <Modal.Header title='' onClose={() => handleClose()}>
              <Box className='flex items-center justify-between w-full'>
                <Box
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'start',
                    gap: '2px',
                  }}
                >
                  <img
                    src={logo}
                    height={38}
                    width={38}
                    className='aspect-auto'
                    alt='configurator logo'
                  />

                  <p className='flex w-full justify-start items-start gap-1 pl-1'>
                    <span className='subtitleHeading'>Page Settings: </span>
                    <span>{sdk.entry.fields['seoTitle'].getValue()}</span>
                  </p>
                </Box>

                {isCloseClicked && (
                  <Box className='flex items-center'>
                    <p className='text-[#E2222B]'>
                      Please resolve required tags before saving
                    </p>
                  </Box>
                )}
              </Box>
            </Modal.Header>

            <Modal.Content className='pageSettingModalContent'>
              <>
                <div className={'customiserRoot'}>
                  <div className={'customiserInnerRoot'}>
                    <aside className='customiserSidebar'>
                      <div className={'listRoot'}>
                        <div className={'categoryRoot'}>
                          <ul>
                            {pageSettingCategories.map((item) => {
                              if (!item.isEnabled) return null
                              return (
                                <li
                                  className={`${
                                    activeCategory === item?.title
                                      ? 'active'
                                      : ''
                                  } ${
                                    item?.isDisabled ? 'disabled' : 'pointer'
                                  } catList categoryListItem py-2 pl-4`}
                                  onClick={() => {
                                    if (item?.isDisabled) {
                                      return
                                    }
                                    setActiveCategory(item?.title)
                                  }}
                                  key={item.value}
                                >
                                  {item?.isDisabled && item?.disabledtooltip ? (
                                    <Tooltip
                                      title={item?.disabledtooltip}
                                      className=' !flex !flex-row items-center gap-2'
                                    >
                                      {item?.title}
                                      <Info className='ml-1 text-gray-500' />
                                    </Tooltip>
                                  ) : (
                                    item?.title
                                  )}
                                </li>
                              )
                            })}
                          </ul>
                        </div>
                      </div>
                    </aside>

                    <main className={'mainRoot formRootPageSetting'}>
                      <div
                        className={`pageSettingPreviewRoot ${
                          activeCategory === 'Tags' ? 'pl-1' : 'px-4'
                        } `}
                        style={{
                          paddingInline: '0px !important',
                        }}
                      >
                        <ApplyNavDefaultSettings sdk={sdk} />
                        {activeCategory === 'General' && (
                          <General
                            sdk={sdk}
                            locale={allowedLocale}
                            domain={selectedDomain}
                          />
                        )}
                        {activeCategory === 'SEO' && (
                          <SEO
                            sdk={sdk}
                            locale={allowedLocale}
                            domain={selectedDomain}
                          />
                        )}
                        {activeCategory === 'Search' && (
                          <Search
                            sdk={sdk}
                            locale={allowedLocale}
                            domain={selectedDomain}
                          />
                        )}
                        {activeCategory === 'Navigation' && (
                          <Navigation
                            sdk={sdk}
                            locale={allowedLocale}
                            domain={selectedDomain}
                          />
                        )}
                        {activeCategory === 'Translations' && (
                          <Translations
                            sdk={sdk}
                            pageDomainAllowedLocale={allowedLocale}
                          />
                        )}
                        {activeCategory === 'Tags' && (
                          <Tags
                            sdk={sdk}
                            locale={allowedLocale}
                            domain={selectedDomain}
                          />
                        )}
                        {activeCategory === 'Cache' && <Cache sdk={sdk} />}
                        {activeCategory === 'Experimentation' && (
                          <Experimentation sdk={sdk} entryId={entryId} />
                        )}
                        {activeCategory === 'Duplicator' && (
                          <ComponentCloner entryId={entryId} />
                        )}
                        {activeCategory === 'AFS' && (
                          <AFS
                            sdk={sdk}
                            locale={allowedLocale}
                            domain={selectedDomain}
                          />
                        )}
                        {activeCategory === 'Dynamic Page' && (
                          <DynamicPage sdk={sdk} entryId={entryId} />
                        )}
                      </div>
                    </main>
                  </div>
                </div>
              </>
            </Modal.Content>
          </>
        )}
      </Modal>
    </EntityProvider>
  )
}

export default PageSettingsModal

export const getNormalizedSlug = (slug: unknown): string => {
  if (typeof slug !== 'string') return ''
  return slug.startsWith('/') ? slug.slice(1) : slug
}
