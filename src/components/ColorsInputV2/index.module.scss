//Default SCSS variables are prepended in next.config.js

.colorPickerRoot {
  width: 460px;
  height: max-content;
  background: linear-gradient(60deg, rgba(255, 255, 255, 1) 1%, rgba(252, 252, 252, 1) 70%, rgba(245, 245, 245, 1) 100%);
  box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;
  border-radius: 10px;
  position: absolute;
  overflow: hidden;
  z-index: 1;

  .colorPickerHeader {
    padding: 15px;
    background: white;
    box-shadow: rgba(17, 12, 46, 0.15) 0px -5px 50px -15px;
    border-radius: 10px 10px 0 0;
    border-bottom: 1px solid #efefef;
    color: #333;
    font-size: 14px;
  }

  .closeIcon {
    position: absolute;
    top: 15px;
    right: 20px;
    cursor: pointer;

    &:hover {
      fill: #e2222b;
    }
  }

  .colorPickerRootInner {
    padding-bottom: 20px;

    .colorPickerTop {
      display: flex;
    }
  }
}

.colorRoot {
  position: relative;
  max-width: fit-content;
  padding: 0 15px 0 20px;
}


.disabled {
  cursor: not-allowed;
  opacity: .5;
  filter: saturate(0);

  &:hover {
    cursor: not-allowed !important;
  }

  .cursorPointerNone {
    pointer-events: none;
  }
}


.colorPickerBottom,
.colorPickerTop {
  padding-top: 15px;
}

// .colorRootGradient {
//   margin-left: -8px;;
//   }
//
// .colorRootMargin {
//   margin: 0 -7px;
//   }


.colorTitle {
  font-size: 11px;
  font-weight: 600;
  margin-bottom: 5px;
  font-family: "Aeonik Regular", sans-serif;
  color: #757575;
  margin-left: 20px;
}

.colorTiles {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  // border: 1px solid rgba(0, 0, 0, .1);
  // border-radius: 5px;
}

.colorTilesNeutral,
.colorTilesGradient {
  display: block;
  width: 35px;
  height: 35px;
  // margin-left: -1px;
  position: relative;
  // border-radius: 5px;
  border: none;
}

.colorSwatch {
  width: 35px;
  display: flex;
  height: 35px;
  transition: all 0.1s ease-in-out;
  cursor: pointer;
  position: relative;
  z-index: 1;

  // Styles when it's the only child
  &.onlyChild {
    // border-radius: 4px;
    outline: 1px solid rgba(0, 0, 0, .1);
  }


  // Styles on hover
  &:hover {
    // flex-grow: 2;
    // outline: 3px solid rgba(0, 0, 0, 0.7);
    outline: 1px solid #000;
    transform: scale(1.25);
    border-radius: 1px;
    transition: all .25s ease;
    z-index: 3;

    // Tooltip appears on hover
    .tooltipRoot {
      display: block;
    }
  }

}

.colorPickerContent {
  padding: 15px 20px 20px 20px;
  background: white;
  // box-shadow: rgba(17, 12, 46, 0.15) 0px -5px 100px 0;
  border-radius: 0 0 10px 10px;

  .colorPickerFooter {
    display: flex;
    align-items: end;
    justify-content: space-between;
  }


  .colorPickerContentInner {
    justify-content: space-between;
    display: flex;


    .colorPickerValue {
      display: flex;
      gap: 20px;
    }


    .colorPickerText {
      width: 340px;

      .colorPickerTextInner {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .active {
          background: #f2f2f2;
          outline: 1px solid #001c99 !important;
          border-radius: 2px;
          transition: all .5s ease;
        }

        .active.value {
          color: #001c99;
        }

        .value {
          font-family: "Aeonik Regular", sans-serif;;
          text-align: left;
          color: #333;
          cursor: pointer;
          font-size: 12px;
          position: relative;
          padding: 5px;
          outline: 1px solid transparent;
          transition: all .5s ease;

          &:hover {
            background: #e5e5e5;
            transition: all .25s ease;
            // border-radius: 5px;
          }
        }
      }

      .gradientText {
        font-size: 10px !important;
      }

    }
  }

  .colorPreviewBox {
    height: 60px;
    width: 60px;
    border-radius: 50%;
    // border: 1px solid #fff;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
    cursor: pointer;
  }
}

.search {
  // margin-top: 6px;
  // margin-left: 5px;
  display: flex;
  align-items: end;
  gap: 5px;
  width: fit-content;

  div {
    margin-bottom: 4px;
    cursor: initial;
  }


  input {
    display: block;
    width: 110px;
    padding: 4px 8px 0 0;
    font-weight: 400;
    // margin-bottom: 5px;
    line-height: 1.5;
    background-color: transparent;
    border: none;
    border-bottom: 1px solid #333;
    outline: none;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;

    /* Optional hover and focus states */
    &:focus {
      background-color: #fff;
      border-bottom-color: #86b7fe;
      box-shadow: none;
    }
  }
}

.colorPickerSearchRoot {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .search {
    position: absolute;
    bottom: 17px;
  }

  .button {
    display: flex;
    right: 20px;
    bottom: 15px;
    position: absolute;

    span {
      padding-left: 5px;
    }

    button {
      color: #fff;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      border-radius: 5px;
      // width: 45px;
      // border-radius: 15px 0 0 0;
      background-color: #000;
      height: 30px;
      font-size: 14px;

      div {
        color: #2cde93;
      }

      .icon {
        fill: #fff
      }

      &:hover {
        color: #2cde93;
        transition: all 0.25s ease;

        div {
          color: #fff;
        }

        .icon {
          fill: #2cde93
        }
      }

    }
  }
}


.tooltipTriangle {
  background: #000;
  border-left: 0;
  border-top: 0;
  clip-path: polygon(52% 100%, 0 0, 100% 0);
  content: "";
  height: 10px;
  position: absolute;
  box-shadow: 5px 5px 8px rgba(0, 0, 0, 0.15);
  width: 12px;
  z-index: 4;
  bottom: 35px;
}

//tooltip style

.tooltipRoot {
  position: absolute;
  width: fit-content;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  // box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;
  // border-radius: 5px;
  z-index: 5;
  top: -44px !important;
}


.visible {
  display: block;
}

.tooltipContent {
  display: flex;
  flex-wrap: wrap;
}

.activeStateBorder {
  // outline: 3px solid rgba(0, 0, 0, 1);
  outline: 1px solid #000;
  transform: scale(1.15);
  border-radius: 1px;
  z-index: 2;
}