const primary1Variants = [
  {
    name    : 'Primary 1-50',
    codeName: 'p1-50',
    hex     : '#e6e7ec',
    rgb     : 'rgb(230, 231, 236)',
  },
  {
    name    : 'Primary 1-100',
    codeName: 'p1-100',
    hex     : '#b0b3c3',
    rgb     : 'rgb(176, 179, 195)',
  },
  {
    name    : 'Primary 1-200',
    codeName: 'p1-200',
    hex     : '#8a8fa6',
    rgb     : 'rgb(138, 143, 166)',
  },
  {
    name    : 'Primary 1-300',
    codeName: 'p1-300',
    hex     : '#545c7d',
    rgb     : 'rgb(84, 92, 125)',
  },
  {
    name    : 'Primary 1-400',
    codeName: 'p1-400',
    hex     : '#333c64',
    rgb     : 'rgb(51, 60, 100)',
  },
  {
    name    : 'Primary 1-600',
    codeName: 'p1-600',
    hex     : '#000a38',
    rgb     : 'rgb(0, 10, 56)',
  },
  {
    name    : 'Primary 1-700',
    codeName: 'p1-700',
    hex     : '#00082b',
    rgb     : 'rgb(0, 8, 43)',
  },
  {
    name    : 'Primary 1-800',
    codeName: 'p1-800',
    hex     : '#000622',
    rgb     : 'rgb(0, 6, 34)',
  },
  {
    name    : 'Primary 1-900',
    codeName: 'p1-900',
    hex     : '#00051a',
    rgb     : 'rgb(0, 5, 26)',
  },
]

const primary2Variants = [
  {
    name    : 'Primary 2-50',
    codeName: 'p2-50',
    hex     : '#e6eafb',
    rgb     : 'rgb(230, 234, 251)',
  },
  {
    name    : 'Primary 2-100',
    codeName: 'p2-100',
    hex     : '#b0bcf3',
    rgb     : 'rgb(176, 188, 243)',
  },
  {
    name    : 'Primary 2-200',
    codeName: 'p2-200',
    hex     : '#8a9ced',
    rgb     : 'rgb(138, 156, 237)',
  },
  {
    name    : 'Primary 2-300',
    codeName: 'p2-300',
    hex     : '#546fe4',
    rgb     : 'rgb(84, 111, 228)',
  },
  {
    name    : 'Primary 2-400',
    codeName: 'p2-400',
    hex     : '#3353df',
    rgb     : 'rgb(51, 83, 223)',
  },
  {
    name    : 'Primary 2-600',
    codeName: 'p2-600',
    hex     : '#0024c4',
    rgb     : 'rgb(0, 36, 196)',
  },
  {
    name    : 'Primary 2-700',
    codeName: 'p2-700',
    hex     : '#001c99',
    rgb     : 'rgb(0, 28, 153)',
  },
  {
    name    : 'Primary 2-800',
    codeName: 'p2-800',
    hex     : '#001676',
    rgb     : 'rgb(0, 22, 118)',
  },
  {
    name    : 'Primary 2-900',
    codeName: 'p2-900',
    hex     : '#00115a',
    rgb     : 'rgb(0, 17, 90)',
  },
]

const secondary1Variants = [
  {
    name    : 'Secondary 1-50',
    codeName: 's1-50',
    hex     : '#fafeec',
    rgb     : 'rgb(250, 254, 236)',
  },
  {
    name    : 'Secondary 1-100',
    codeName: 's1-100',
    hex     : '#eefbc5',
    rgb     : 'rgb(238, 251, 197)',
  },
  {
    name    : 'Secondary 1-200',
    codeName: 's1-200',
    hex     : '#e6f9a9',
    rgb     : 'rgb(230, 249, 169)',
  },
  {
    name    : 'Secondary 1-300',
    codeName: 's1-300',
    hex     : '#dbf782',
    rgb     : 'rgb(219, 247, 130)',
  },
  {
    name    : 'Secondary 1-400',
    codeName: 's1-400',
    hex     : '#dbf782',
    rgb     : 'rgb(212, 245, 105)',
  },
  {
    name    : 'Secondary 1-600',
    codeName: 's1-600',
    hex     : '#b7dd3e',
    rgb     : 'rgb(183, 221, 62)',
  },
  {
    name    : 'Secondary 1-700',
    codeName: 's1-700',
    hex     : '#8fad30',
    rgb     : 'rgb(143, 173, 48)',
  },
  {
    name    : 'Secondary 1-800',
    codeName: 's1-800',
    hex     : '#6f8625',
    rgb     : 'rgb(111, 134, 37)',
  },
  {
    name    : 'Secondary 1-900',
    codeName: 's1-900',
    hex     : '#54661d',
    rgb     : 'rgb(84, 102, 29)',
  },
]

const secondary2Variants = [
  {
    name    : 'Secondary 2-50',
    codeName: 's2-50',
    hex     : '#e6f3f7',
    rgb     : 'rgb(230, 243, 247)',
  },
  {
    name    : 'Secondary 2-100',
    codeName: 's2-100',
    hex     : '#b0d8e5',
    rgb     : 'rgb(176, 216, 229)',
  },
  {
    name    : 'Secondary 2-200',
    codeName: 's2-200',
    hex     : '#8ac6d8',
    rgb     : 'rgb(138, 198, 216)',
  },
  {
    name    : 'Secondary 2-300',
    codeName: 's2-300',
    hex     : '#54abc7',
    rgb     : 'rgb(84, 171, 199)',
  },
  {
    name    : 'Secondary 2-400',
    codeName: 's2-400',
    hex     : '#339bbc',
    rgb     : 'rgb(51, 155, 188)',
  },
  {
    name    : 'Secondary 2-600',
    codeName: 's2-600',
    hex     : '#00769c',
    rgb     : 'rgb(0, 118, 156)',
  },
  {
    name    : 'Secondary 2-700',
    codeName: 's2-700',
    hex     : '#005c79',
    rgb     : 'rgb(0, 92, 121)',
  },
  {
    name    : 'Secondary 2-800',
    codeName: 's2-800',
    hex     : '#00485e',
    rgb     : 'rgb(0, 72, 94)',
  },
  {
    name    : 'Secondary 2-900',
    codeName: 's2-900',
    hex     : '#00374b',
    rgb     : 'rgb(0, 55, 72)',
  },
]

const neutralVariants = [
  {
    name    : 'Neutral 2',
    codeName: 'n2',
    hex     : '#333',
    rgb     : 'rgb(51, 51, 51)',
  },
  {
    name    : 'Neutral 3',
    codeName: 'n3',
    hex     : '#757575',
    rgb     : 'rgb(117, 117, 117)',
  },
  {
    name    : 'Neutral 4',
    codeName: 'n4',
    hex     : '#c6c6c6',
    rgb     : 'rgb(198, 198, 198)',
  },
  {
    name    : 'Neutral 5',
    codeName: 'n5',
    hex     : '#e5e5e5',
    rgb     : 'rgb(229, 229, 229)',
  },
  {
    name    : 'Neutral 6',
    codeName: 'n6',
    hex     : '#f2f2f2',
    rgb     : 'rgb(242, 242, 242)',
  },
  {
    name    : 'Neutral 7',
    codeName: 'n7',
    hex     : '#f9f9f9',
    rgb     : 'rgb(249, 249, 249)',
  },
  {
    name    : 'Neutral 8',
    codeName: 'n8',
    hex     : '#fff',
    rgb     : 'rgb(255, 255, 255)',
  },
]

const accent1Variants = [
  {
    name    : 'Accent 1-50',
    codeName: 'a1-50',
    hex     : '#eafcf4',
    rgb     : 'rgb(234, 252, 244)',
  },
  {
    name    : 'Accent 1-100',
    codeName: 'a1-100',
    hex     : '#bef5de',
    rgb     : 'rgb(190, 245, 222)',
  },
  {
    name    : 'Accent 1-200',
    codeName: 'a1-200',
    hex     : '#9ef0cd',
    rgb     : 'rgb(158, 240, 205)',
  },
  {
    name    : 'Accent 1-300',
    codeName: 'a1-300',
    hex     : '#72e9b7',
    rgb     : 'rgb(114, 233, 183)',
  },
  {
    name    : 'Accent 1-400',
    codeName: 'a1-400',
    hex     : '#56e5a9',
    rgb     : 'rgb(86, 229, 169)',
  },
  {
    name    : 'Accent 1-600',
    codeName: 'a1-600',
    hex     : '#28ca86',
    rgb     : 'rgb(40, 202, 134)',
  },
  {
    name    : 'Accent 1-700',
    codeName: 'a1-700',
    hex     : '#1f9e68',
    rgb     : 'rgb(31, 158, 104)',
  },
  {
    name    : 'Accent 1-800',
    codeName: 'a1-800',
    hex     : '#187a51',
    rgb     : 'rgb(24, 122, 81)',
  },
  {
    name    : 'Accent 1-900',
    codeName: 'a1-900',
    hex     : '#125d3e',
    rgb     : 'rgb(18, 93, 62)',
  },
]

const accent2Variants = [
  {
    name    : 'Accent 2-50',
    codeName: 'a2-50',
    hex     : '#e6f5f2',
    rgb     : 'rgb(230, 245, 242)',
  },
  {
    name    : 'Accent 2-100',
    codeName: 'a2-100',
    hex     : '#b0dfd8',
    rgb     : 'rgb(176, 223, 216)',
  },
  {
    name    : 'Accent 2-200',
    codeName: 'a2-200',
    hex     : '#8ad0c5',
    rgb     : 'rgb(138, 208, 197)',
  },
  {
    name    : 'Accent 2-300',
    codeName: 'a2-300',
    hex     : '#54bbaa',
    rgb     : 'rgb(84, 187, 170)',
  },
  {
    name    : 'Accent 2-400',
    codeName: 'a2-400',
    hex     : '#33ad99',
    rgb     : 'rgb(51, 173, 153)',
  },
  {
    name    : 'Accent 2-600',
    codeName: 'a2-600',
    hex     : '#008b74',
    rgb     : 'rgb(0, 139, 116)',
  },
  {
    name    : 'Accent 2-700',
    codeName: 'a2-700',
    hex     : '#006d5b',
    rgb     : 'rgb(0, 109, 91)',
  },
  {
    name    : 'Accent 2-800',
    codeName: 'a2-800',
    hex     : '#005446',
    rgb     : 'rgb(0, 84, 70)',
  },
  {
    name    : 'Accent 2-900',
    codeName: 'a2-900',
    hex     : '#004036',
    rgb     : 'rgb(0, 64, 54)',
  },
]

const accent3Variants = [
  {
    name    : 'Accent 3-50',
    codeName: 'a3-50',
    hex     : '#e6f7e9',
    rgb     : 'rgb(230, 247, 233)',
  },
  {
    name    : 'Accent 3-100',
    codeName: 'a3-100',
    hex     : '#b0e6bb',
    rgb     : 'rgb(176, 230, 187)',
  },
  {
    name    : 'Accent 3-200',
    codeName: 'a3-200',
    hex     : '#8ad99a',
    rgb     : 'rgb(138, 217, 154)',
  },
  {
    name    : 'Accent 3-300',
    codeName: 'a3-300',
    hex     : '#54c86c',
    rgb     : 'rgb(84, 200, 108)',
  },
  {
    name    : 'Accent 3-400',
    codeName: 'a3-400',
    hex     : '#33bd4f',
    rgb     : 'rgb(51, 189, 79)',
  },
  {
    name    : 'Accent 3-600',
    codeName: 'a3-600',
    hex     : '#009d20',
    rgb     : 'rgb(0, 157, 32)',
  },
  {
    name    : 'Accent 3-700',
    codeName: 'a3-700',
    hex     : '#007b19',
    rgb     : 'rgb(0, 123, 25)',
  },
  {
    name    : 'Accent 3-800',
    codeName: 'a3-800',
    hex     : '#005f13',
    rgb     : 'rgb(0, 95, 19)',
  },
  {
    name    : 'Accent 3-900',
    codeName: 'a3-900',
    hex     : '#00490f',
    rgb     : 'rgb(0, 73, 15)',
  },
]

const accent4Variants = [
  {
    name    : 'Accent 4-50',
    codeName: 'a4-50',
    hex     : '#e6eaf5',
    rgb     : 'rgb(230, 234, 245)',
  },
  {
    name    : 'Accent 4-100',
    codeName: 'a4-100',
    hex     : '#b0bddf',
    rgb     : 'rgb(176, 189, 223)',
  },
  {
    name    : 'Accent 4-200',
    codeName: 'a4-200',
    hex     : '#8a9dd0',
    rgb     : 'rgb(138, 157, 208)',
  },
  {
    name    : 'Accent 4-300',
    codeName: 'a4-300',
    hex     : '#5470bb',
    rgb     : 'rgb(84, 112, 187)',
  },
  {
    name    : 'Accent 4-400',
    codeName: 'a4-400',
    hex     : '#3355ad',
    rgb     : 'rgb(51, 85, 173)',
  },
  {
    name    : 'Accent 4-600',
    codeName: 'a4-600',
    hex     : '#00268b',
    rgb     : 'rgb(0, 38, 139)',
  },
  {
    name    : 'Accent 4-700',
    codeName: 'a4-700',
    hex     : '#001e6d',
    rgb     : 'rgb(0, 30, 109)',
  },
  {
    name    : 'Accent 4-800',
    codeName: 'a4-800',
    hex     : '#001754',
    rgb     : 'rgb(0, 23, 84)',
  },
  {
    name    : 'Accent 4-900',
    codeName: 'a4-900',
    hex     : '#001240',
    rgb     : 'rgb(0, 18, 64)',
  },
]

const accent5Variants = [
  {
    name    : 'Accent 5-50',
    codeName: 'a5-50',
    hex     : '#f3edff',
    rgb     : 'rgb(243, 237, 255)',
  },
  {
    name    : 'Accent 5-100',
    codeName: 'a5-100',
    hex     : '#d9c8ff',
    rgb     : 'rgb(217, 200, 255)',
  },
  {
    name    : 'Accent 5-200',
    codeName: 'a5-200',
    hex     : '#c7adff',
    rgb     : 'rgb(199, 173, 255)',
  },
  {
    name    : 'Accent 5-300',
    codeName: 'a5-300',
    hex     : '#ad88ff',
    rgb     : 'rgb(173, 136, 255)',
  },
  {
    name    : 'Accent 5-400',
    codeName: 'a5-400',
    hex     : '#9d71ff',
    rgb     : 'rgb(157, 113, 255)',
  },
  {
    name    : 'Accent 5-600',
    codeName: 'a5-600',
    hex     : '#7946e8',
    rgb     : 'rgb(121, 70, 232)',
  },
  {
    name    : 'Accent 5-700',
    codeName: 'a5-700',
    hex     : '#5e37b5',
    rgb     : 'rgb(94, 55, 181)',
  },
  {
    name    : 'Accent 5-800',
    codeName: 'a5-800',
    hex     : '#492a8c',
    rgb     : 'rgb(73, 42, 140)',
  },
  {
    name    : 'Accent 5-900',
    codeName: 'a5-900',
    hex     : '#38206b',
    rgb     : 'rgb(56, 32, 107)',
  },
]

const accent6Variants = [
  {
    name    : 'Accent 6-50',
    codeName: 'a6-50',
    hex     : '#faedff',
    rgb     : 'rgb(250, 237, 255)',
  },
  {
    name    : 'Accent 6-100',
    codeName: 'a6-100',
    hex     : '#f0c8ff',
    rgb     : 'rgb(240, 200, 255)',
  },
  {
    name    : 'Accent 6-200',
    codeName: 'a6-200',
    hex     : '#e9adff',
    rgb     : 'rgb(233, 173, 255)',
  },
  {
    name    : 'Accent 6-300',
    codeName: 'a6-300',
    hex     : '#e088ff',
    rgb     : 'rgb(224, 136, 255)',
  },
  {
    name    : 'Accent 6-400',
    codeName: 'a6-400',
    hex     : '#d971ff',
    rgb     : 'rgb(217, 113, 255)',
  },
  {
    name    : 'Accent 6-600',
    codeName: 'a6-600',
    hex     : '#bd46e8',
    rgb     : 'rgb(189, 70, 232)',
  },
  {
    name    : 'Accent 6-700',
    codeName: 'a6-700',
    hex     : '#9437b5',
    rgb     : 'rgb(148, 55, 181)',
  },
  {
    name    : 'Accent 6-800',
    codeName: 'a6-800',
    hex     : '#722a8c',
    rgb     : 'rgb(114, 42, 140)',
  },
  {
    name    : 'Accent 6-900',
    codeName: 'a6-900',
    hex     : '#57206b',
    rgb     : 'rgb(87, 32, 107)',
  },
]

const accent7Variants = [
  {
    name    : 'Accent 7-50',
    codeName: 'a7-50',
    hex     : '#ffedfb',
    rgb     : 'rgb(255, 237, 251)',
  },
  {
    name    : 'Accent 7-100',
    codeName: 'a7-100',
    hex     : '#ffc8f2',
    rgb     : 'rgb(255, 200, 242)',
  },
  {
    name    : 'Accent 7-200',
    codeName: 'a7-200',
    hex     : '#ffadec',
    rgb     : 'rgb(255, 173, 236)',
  },
  {
    name    : 'Accent 7-300',
    codeName: 'a7-300',
    hex     : '#ff88e3',
    rgb     : 'rgb(255, 136, 227)',
  },
  {
    name    : 'Accent 7-400',
    codeName: 'a7-400',
    hex     : '#ff71dd',
    rgb     : 'rgb(255, 113, 221)',
  },
  {
    name    : 'Accent 7-600',
    codeName: 'a7-600',
    hex     : '#e846c2',
    rgb     : 'rgb(232, 70, 194)',
  },
  {
    name    : 'Accent 7-700',
    codeName: 'a7-700',
    hex     : '#b53797',
    rgb     : 'rgb(181, 55, 151)',
  },
  {
    name    : 'Accent 7-800',
    codeName: 'a7-800',
    hex     : '#8c2a75',
    rgb     : 'rgb(140, 42, 117)',
  },
  {
    name    : 'Accent 7-900',
    codeName: 'a7-900',
    hex     : '#6b2059',
    rgb     : 'rgb(107, 32, 89)',
  },
]

const accent8Variants = [
  {
    name    : 'Accent 8-50',
    codeName: 'a8-50',
    hex     : '#ffedf2',
    rgb     : 'rgb(255, 237, 242)',
  },
  {
    name    : 'Accent 8-100',
    codeName: 'a8-100',
    hex     : '#ffc8d6',
    rgb     : 'rgb(255, 200, 214)',
  },
  {
    name    : 'Accent 8-200',
    codeName: 'a8-200',
    hex     : '#ffadc3',
    rgb     : 'rgb(255, 173, 195)',
  },
  {
    name    : 'Accent 8-300',
    codeName: 'a8-300',
    hex     : '#ff88a7',
    rgb     : 'rgb(255, 136, 167)',
  },
  {
    name    : 'Accent 8-400',
    codeName: 'a8-400',
    hex     : '#ff7196',
    rgb     : 'rgb(255, 113, 150)',
  },
  {
    name    : 'Accent 8-600',
    codeName: 'a8-600',
    hex     : '#e84671',
    rgb     : 'rgb(232, 70, 113)',
  },
  {
    name    : 'Accent 8-700',
    codeName: 'a8-700',
    hex     : '#b53758',
    rgb     : 'rgb(181, 55, 88)',
  },
  {
    name    : 'Accent 8-800',
    codeName: 'a8-800',
    hex     : '#8c2a44',
    rgb     : 'rgb(140, 42, 68)',
  },
  {
    name    : 'Accent 8-900',
    codeName: 'a8-900',
    hex     : '#6b2034',
    rgb     : 'rgb(107, 32, 52)',
  },
]

const accent9Variants = [
  {
    name    : 'Accent 9-50',
    codeName: 'a9-50',
    hex     : '#ffefe6',
    rgb     : 'rgb(255, 239, 230)',
  },
  {
    name    : 'Accent 9-100',
    codeName: 'a9-100',
    hex     : '#ffcdb2',
    rgb     : 'rgb(255, 205, 178)',
  },
  {
    name    : 'Accent 9-200',
    codeName: 'a9-200',
    hex     : '#ffb58c',
    rgb     : 'rgb(255, 181, 140)',
  },
  {
    name    : 'Accent 9-300',
    codeName: 'a9-300',
    hex     : '#ff9458',
    rgb     : 'rgb(255, 148, 88)',
  },
  {
    name    : 'Accent 9-400',
    codeName: 'a9-400',
    hex     : '#ff7f37',
    rgb     : 'rgb(255, 127, 55)',
  },
  {
    name    : 'Accent 9-600',
    codeName: 'a9-600',
    hex     : '#e85605',
    rgb     : 'rgb(232, 86, 5)',
  },
  {
    name    : 'Accent 9-700',
    codeName: 'a9-700',
    hex     : '#b54304',
    rgb     : 'rgb(181, 67, 4)',
  },
  {
    name    : 'Accent 9-800',
    codeName: 'a9-800',
    hex     : '#8c3403',
    rgb     : 'rgb(140, 52, 3)',
  },
  {
    name    : 'Accent 9-900',
    codeName: 'a9-900',
    hex     : '#6b2802',
    rgb     : 'rgb(107, 40, 2)',
  },
]

const accent10Variants = [
  {
    name    : 'Accent 10-50',
    codeName: 'a10-50',
    hex     : '#fce9ea',
    rgb     : 'rgb(252, 233, 234)',
  },
  {
    name    : 'Accent 10-100',
    codeName: 'a10-100',
    hex     : '#f6babd',
    rgb     : 'rgb(246, 186, 189)',
  },
  {
    name    : 'Accent 10-200',
    codeName: 'a10-200',
    hex     : '#f2999d',
    rgb     : 'rgb(242, 153, 157)',
  },
  {
    name    : 'Accent 10-300',
    codeName: 'a10-300',
    hex     : '#ec6b71',
    rgb     : 'rgb(236, 107, 113)',
  },
  {
    name    : 'Accent 10-400',
    codeName: 'a10-400',
    hex     : '#e84e55',
    rgb     : 'rgb(232, 78, 85)',
  },
  {
    name    : 'Accent 10-600',
    codeName: 'a10-600',
    hex     : '#ce1f27',
    rgb     : 'rgb(206, 31, 39)',
  },
  {
    name    : 'Accent 10-700',
    codeName: 'a10-700',
    hex     : '#a0181f',
    rgb     : 'rgb(160, 24, 31)',
  },
  {
    name    : 'Accent 10-800',
    codeName: 'a10-800',
    hex     : '#7c1318',
    rgb     : 'rgb(124, 19, 24)',
  },
  {
    name    : 'Accent 10-900',
    codeName: 'a10-900',
    hex     : '#5f0e12',
    rgb     : 'rgb(94, 14, 18)',
  },
]

const gradientVariants = [
  {
    name          : 'Gradient 2',
    codeName      : 'bs1s2h',
    gradientType  : 'linear',
    angle         : 90,
    gradientColors: [
      {
        hex: '#c9f344',
        rgb: 'rgb(201, 243, 68)',
      },
      {
        hex: '#0082ab',
        rgb: 'rgb(0, 130, 171)',
      },
    ],
  },
  {
    name          : 'Gradient 3',
    codeName      : 'bp2s2d',
    gradientType  : 'linear',
    angle         : 45,
    gradientColors: [
      {
        hex: '#0028d7',
        rgb: 'rgb(0, 40, 215)',
      },
      {
        hex: '#0082ab',
        rgb: 'rgb(0, 130, 171)',
      },
    ],
  },
  {
    name          : 'Gradient 4',
    codeName      : 'bp1p2d',
    gradientType  : 'linear',
    angle         : 45,
    gradientColors: [
      {
        hex: '#000b3d',
        rgb: 'rgb(0, 11, 61)',
      },
      {
        hex: '#0028d7',
        rgb: 'rgb(0, 40, 215)',
      },
    ],
  },
]

export const colorsValues = [
  {
    name  : 'Primaries',
    colors: [
      {
        name    : 'Primary 1',
        codeName: 'p1',
        hex     : '#000b3d',
        rgb     : 'rgb(0, 11, 61)',
        variants: primary1Variants,
      },
      {
        name    : 'Primary 2',
        codeName: 'p2',
        hex     : '#0028d7',
        rgb     : 'rgb(0, 40, 215)',
        variants: primary2Variants,
      },
    ],
  },
  {
    name  : 'Secondaries',
    colors: [
      {
        name    : 'Secondary 1',
        codeName: 's1',
        hex     : '#c9f344',
        rgb     : 'rgb(201, 243, 68)',
        variants: secondary1Variants,
      },
      {
        name    : 'Secondary 2',
        codeName: 's2',
        hex     : '#0082ab',
        rgb     : 'rgb(0, 130, 171)',
        variants: secondary2Variants,
      },
    ],
  },
  {
    name  : 'Neutrals',
    colors: [
      {
        name    : 'Neutral 1',
        codeName: 'n1',
        hex     : '#000',
        rgb     : 'rgb(0, 0, 0)',
        variants: neutralVariants,
      },
    ],
  },
  {
    name  : 'Gradients',
    colors: [
      {
        name          : 'Gradient 1',
        codeName      : 'bs1s2d',
        gradientType  : 'linear',
        angle         : 45,
        gradientColors: [
          { hex: '#c9f344', rgb: 'rgb(201, 243, 68)' },
          { hex: '#0082ab', rgb: '0, 130, 171' },
        ],
        variants      : gradientVariants,
      },
    ],
  },
  {
    name  : 'Accents',
    colors: [
      {
        name    : 'Accent 1',
        codeName: 'a1',
        hex     : '#2cde93',
        rgb     : 'rgb(44, 222, 147)',
        variants: accent1Variants,
      },

      {
        name    : 'Accent 2',
        codeName: 'a2',
        hex     : '#009980',
        rgb     : 'rgb(0, 153, 128)',
        variants: accent2Variants,
      },
      {
        name    : 'Accent 3',
        codeName: 'a3',
        hex     : '#00ad23',
        rgb     : 'rgb(0, 173, 35)',
        variants: accent3Variants,
      },
      {
        name    : 'Accent 4',
        codeName: 'a4',
        hex     : '#002a99',
        rgb     : 'rgb(0, 42, 153)',
        variants: accent4Variants,
      },
      {
        name    : 'Accent 5',
        codeName: 'a5',
        hex     : '#854dff',
        rgb     : 'rgb(133, 77, 255)',
        variants: accent5Variants,
      },
      {
        name    : 'Accent 6',
        codeName: 'a6',
        hex     : '#d04dff',
        rgb     : 'rgb(208, 77, 255)',
        variants: accent6Variants,
      },
      {
        name    : 'Accent 7',
        codeName: 'a7',
        hex     : '#ff4dd5',
        rgb     : 'rgb(255, 77, 213)',
        variants: accent7Variants,
      },
      {
        name    : 'Accent 8',
        codeName: 'a8',
        hex     : '#ff4d7c',
        rgb     : 'rgb(255, 77, 124)',
        variants: accent8Variants,
      },
      {
        name    : 'Accent 9',
        codeName: 'a9',
        hex     : '#ff5f05',
        rgb     : 'rgb(255, 95, 5)',
        variants: accent9Variants,
      },
      {
        name    : 'Accent 10',
        codeName: 'a10',
        hex     : '#e2222b',
        rgb     : 'rgb(226, 34, 43)',
        variants: accent10Variants,
      },
    ],
  },
]