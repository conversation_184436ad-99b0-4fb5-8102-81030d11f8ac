import { useRef } from 'react'
import TheAnimatedNode from '../../../TheAnimatedNode'
import styles from '../../index.module.scss'
import { getColorStyles } from '../../utils'

interface ColorPickerTooltipProps {
  variants: {
    hex: string
  }[]
  setSelectedColor: (color: any) => void
  setActiveColorIndex: (index: {
    categoryIndex: number
    colorIndex: number
    variantIndex: number | null
  }) => void
  setSearchValue: (value: string) => void
  activeColorIndex: {
    categoryIndex: number
    colorIndex: number
    variantIndex: number | null
  }
  left?: number
  top?: number
}

export default function ColorPickerTooltip(props: ColorPickerTooltipProps) {
  const colorBoxes = props.variants
  const activeColorIndex = props.activeColorIndex
  const ref = useRef(null)

  // remove tooltip if clicking outside
  // useEffect(() => {
  //   function handleClickOutside(event: any) {
  //     if (ref.current && !ref.current.contains(event.target)) {
  //       props.setActiveColorIndex((prev) => {
  //         return {
  //           categoryIndex: null,
  //           colorIndex: null,
  //           variantIndex: null
  //         }
  //       })
  //     }
  //   }
  //   document.addEventListener("mousedown", handleClickOutside)
  //   return () => {
  //     document.removeEventListener("mousedown", handleClickOutside)
  //   }
  // }, [ref])

  //animation variants
  const variants = {
    active: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.15,
        ease: "easeIn",
      },
    },
    inactive: {
      opacity: 0,
      y: 10,
      transition: {
        duration: 0.3,
      },
    },
  };

  return colorBoxes ? (
    <TheAnimatedNode
      as={'div'} animatedProps={{
        variants,
        initial: "inactive",
        animate: "active",
        exit: "inactive",
      }}
      htmlAttr={{
        ref: ref,
        style: { left: props.left ?? 0, top: props.top ?? -50 },
        className: styles.tooltipRoot
      }} >

      {colorBoxes.map((color, i) => (
        <div
          onClick={() => {
            props.setSearchValue('')
            props.setSelectedColor(color)
            props.setActiveColorIndex((prev) => {
              return { ...prev, variantIndex: i }
            })
          }}
          className={`${styles.colorSwatch} ${activeColorIndex?.variantIndex === i ? styles.activeStateBorder : ''}`}
          style={getColorStyles(color)}
        ></div>
      ))}
    </TheAnimatedNode>
  ) : (
    <></>
  )
}