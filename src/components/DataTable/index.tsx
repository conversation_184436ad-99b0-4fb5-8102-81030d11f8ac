import {
  Box,
  Button,
  Flex,
  Form,
  FormControl,
  Modal,
  ModalControls,
  Skeleton,
  Tooltip
} from '@contentful/f36-components'
import { notification } from 'antd'
import { Asset } from 'contentful-management'
import React, { useContext, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import Download from '../../assets/icons/Download'
import { GlobalContext } from '../../contexts/globalContext'
import { publishEntry } from '../../globals/utils'
import { setTableAsset, setTableData } from '../../redux/slices/dataTables'
import { AppDispatch, RootState } from '../../redux/store'
import NextButton from '../Buttons/NextButton'
import PrevButton from '../Buttons/PrevButton'
import SaveButton from '../Buttons/SaveButton'
import ModalConfirm from '../ConfirmModal'
import FileUpload from '../DataVisualization/@Core/FileUpload'
import { removeFieldFromEntry } from '../DataVisualization/utils'
import { EntityProvider } from '../InputFields/Reference'
import ProgressBar from '../ProgressBar'
import DataTablePreview from './DataTablePreview'
import './index.scss'
import TableConfig from './TableConfig'
import {
  TableModalPropsI,
  UpdateEntryData
} from './utils'

const DataTable = (props: TableModalPropsI) => {
  const { sdk, entryId } = props
  const sdkFields = sdk?.entry?.fields
  const entry = sdk?.entry
  const internalName = sdkFields?.['internalName']?.getValue()
  const { isDataTableModalOpen, currentLocale, setIsDataTableModalOpen } =
    useContext(GlobalContext)
    const tableId: any = useSelector(
        (state: RootState) => state?.tableData?.tableId
      )
      const tableAsset: any = useSelector(
        (state: RootState) => state?.tableData?.tableAsset
      )
      const tableData: any = useSelector(
        (state: RootState) => state?.tableData?.tableData
      )
  const [activeStep, setActiveStep] = useState(0)
  const [confirmApplyModal, setConfirmApplyModal] = useState<boolean>(false)
  const [confirmCloseModal, setConfirmCloseModal] = useState<boolean>(false)
 const [dataLoading, setDataLoading] = useState<boolean>(false)
  const [published, setPublished] = useState(false)
  const filename = tableAsset?.fields?.title?.[currentLocale]?.split(' ')
  const formattedFileName = tableAsset?.fields?.title?.[currentLocale]

  const dispatch = useDispatch<AppDispatch>()
  useEffect(() => {
    tableAsset ? setActiveStep(1) : setActiveStep(0)
  }, [])

  const handleFileRevert = async () => {
    await removeFieldFromEntry(entryId, 'source')
    dispatch(setTableAsset(undefined))
    setActiveStep(0)
  }

  //Handle new or existing asset choose
  const handleChatFileAction = async (assetData: Asset | undefined) => {
    setDataLoading(true)
     dispatch(setTableAsset(assetData))
    if (assetData) {
      setActiveStep(1)
    } else {
      await removeFieldFromEntry(entryId, 'source')
      setActiveStep(0)
    }
    setDataLoading(false)
  }
  const handleDataApply = async (data: any) => {
    setDataLoading(true)
    const payLoad = {
      ...tableData,
      ...data
    }
    dispatch(setTableData(payLoad))
    setDataLoading(false)
  }
  const handleSave = async () => {
    setDataLoading(true)
    const payload = {
      tableAsset,
      tableData,
    }

    await UpdateEntryData(entryId, payload, currentLocale)
    setDataLoading(false)
    setActiveStep(3)
  }
  const handleModalClose = async () => {
    setDataLoading(true)
    const payload = {
      tableAsset,
      tableData,
    }
    await UpdateEntryData(entryId, payload, currentLocale)
    setDataLoading(false)
    setIsDataTableModalOpen(false)
    setConfirmCloseModal(false)
  }

  const handlePublish = async () => {
    setDataLoading(true)
    let ispublished = await publishEntry(props?.entryId)
    setPublished(ispublished)
    setDataLoading(false)
    openNotification()
  }

  const openNotification = () => {
    notification.success({
      key: 'table-publish-success-notification',
      message: 'Table was successfully created.',
      placement: 'bottom',
      duration: 3, // Closes after 3 seconds
      onClose: () => { setIsDataTableModalOpen(false) }, // Close modal when notification closes
      closeIcon: false,
      style: { position: 'absolute', left: '-150px', bottom: '100px', width: '500px', backgroundColor: '#b0dfd8', color: '#005446' }
    });
  };
  const handleClose = () => {
    notification.destroy('table-publish-success-notification');
    setIsDataTableModalOpen(false)
  }

  let title = (
    <p className='mdlTitle'>
      <strong>Data Table</strong>
    </p>
  )
  if (activeStep === 0) {
    title = (
      <p className='mdlTitle'>
        <span>
          <strong>Data Table: </strong>{'Please add a data source for'} {internalName}.
        </span>
        <span> Step {activeStep + 1} of 4</span>
      </p>
    )
  } else  if (activeStep === 1) {
    title = (
      <p className='mdlTitle'>
        <span>
          <strong>Data Table: </strong>Preview generated from {internalName}.
        </span>
        <span> Step {activeStep + 1} of 4</span>
      </p>
    )
  } else if (activeStep === 2) {
    title = (
      <p className='mdlTitle'>
        <span>
          <strong>Data Table: </strong>Customise the table for {internalName}
        </span>
        <span> Step {activeStep + 1} of 4</span>
      </p>
    )
  } else if (activeStep === 3) {
    title = (
      <p className='mdlTitle'>
        <span>
          <strong>Data Table: </strong>Preview of the table from {formattedFileName ?? ''}..
        </span>
        <span> Step {activeStep + 1} of 4</span>
      </p>
    )
  }


  const dataSourceDiv = (
    <FormControl key={'step 1'}>
      <Flex
        gap='20px'
        style={{
          width: `${tableAsset ? '200px' : '100%'}`,
          paddingLeft: `${tableAsset ? '0' : '15px'}`,
          justifyContent: 'start'
        }}
      >
        <FileUpload
          sdk={sdk}
          asset={tableAsset}
          onAction={handleChatFileAction}
          setDataLoading={setDataLoading}
          dataLoading={dataLoading}
          usedFor='DT'
        />
        {!tableAsset && (
          <Tooltip
            placement='top'
            id='download-tool-tip'
            content={'Download the reference asset for Data table'}
          >
            <Button
              as='a'
              variant='secondary'
              startIcon={<Download />}
              size='small'
              isDisabled={dataLoading}
              href='https://assets.ctfassets.net/8jgyidtgyr4v/3aEghTB82hKmYfHnjVOeyC/7c8085b20c44b03d6f5019daa26f44d9/Data-table.xlsx'
            >Download reference data source</Button>
          </Tooltip>
        )} 
      </Flex>
    </FormControl> 
  )

  return (
    <EntityProvider sdk={sdk}>
      <Modal
        className='dtModalRoot'
        onClose={() => {
          setIsDataTableModalOpen(true)
        }}
        isShown={isDataTableModalOpen}
        shouldCloseOnOverlayClick={false}
        shouldCloseOnEscapePress={false}
        position='center'
      >
        {() => (
          <>
            <Modal.Header
              className='header'
              title=''
              onClose={() => activeStep === 4 ? (published ? handleClose() : setConfirmCloseModal(true)) : setConfirmCloseModal(true) 
              }
            >
              <Box className={'headerBox'}>{title}</Box>
            </Modal.Header>
            <Box style={{ width: '100%' }}>
              <ProgressBar completedSteps={activeStep} totalSteps={3} />
            </Box>
            <Modal.Content className='dtModalContent'>
              <Form className='dt-content-root'>
                {activeStep === 0 && <Flex flexDirection={'column'} gap={'30px'} className='w-100'>
                  {dataLoading ?
                    <Skeleton.Container>
                      <Skeleton.BodyText numberOfLines={4} />
                    </Skeleton.Container> : dataSourceDiv}
                </Flex>}

                {activeStep === 1 && <Flex flexDirection={'row'} gap={'30px'} className='w-100'>
                  {dataSourceDiv}
                  <div
                    className='vertical-line'
                    style={{ left: '200px' }}
                  ></div>
                  {tableAsset ? <DataTablePreview asset={tableAsset} data={tableData} internalName={internalName} /> : <Skeleton.Container>
                    <Skeleton.BodyText numberOfLines={4} />
                  </Skeleton.Container>}
                </Flex>}
                {activeStep === 2 && (
                  <Flex flexDirection='row' gap={'30px'} className='w-100'>
                    <Flex flexDirection='column'
                      style={{
                        width: '250px',
                        paddingLeft: '15px',
                        justifyContent: 'start'
                      }}>
                      {dataSourceDiv}
                      <TableConfig changedData={tableData} onChange={handleDataApply} />
                    </Flex>
                    <div
                      className='vertical-line'
                      style={{ left: '250px' }}
                    ></div>
                    <DataTablePreview asset={tableAsset} data={tableData} internalName={internalName} />
                  </Flex>
                )}
                {activeStep === 3 && (
                  <Flex flexDirection='row' gap={'30px'} className='w-100'>
                    <DataTablePreview asset={tableAsset} data={tableData} internalName={internalName} />
                  </Flex>
                )}

                <ModalConfirm
                  open={confirmCloseModal}
                  btn1Text='Resume'
                  btn2Text='End the world'
                  title='Hold up, wait a minute...'
                  onConfirm={handleModalClose}
                  children={
                    <>
                      <Box className='summery'>
                        <p>
                          All changes made will be saved as a draft and you can choose to resume later. Or the world might end.
                          <br />
                          <br />
                          Are you sure you want to exit?
                        </p>
                      </Box>
                    </>
                  }
                  handleClose={() => setConfirmCloseModal(false)}
                />
              </Form>

            </Modal.Content>
              <ModalControls className='modelControl'>
                <>
                  {activeStep === 0 ? (
                    <>
                      <NextButton
                        helpText='Continue'
                        onClick={() => setActiveStep(1)}
                        btnText='Continue'
                        type='primary'
                        tooltipPlacement='top'
                        isIcon={false}
                      isDisabled={!tableAsset}
                      isLoading={dataLoading}
                      />
                    </>
                ) : (activeStep === 1 ? (
                  <>

                      {tableAsset && <PrevButton
                        btnText='Back'
                        type='secondary'
                        helpText={'Amend/Update'}
                        onClick={handleFileRevert}
                        tooltipPlacement='top'
                        isIcon={false}
                        isDisabled={dataLoading}
                      />}
                      <NextButton
                        helpText='Continue'
                        onClick={() => setActiveStep(2)}
                        btnText='Continue'
                        type='primary'
                        tooltipPlacement='top'
                        isIcon={false}
                        isLoading={dataLoading}
                      />
                    </>
                  ) : (
                    activeStep === 2 ? (
                      <>
                          <PrevButton
                            btnText='Back'
                            type='secondary'
                            helpText={'Amend/Update'}
                            onClick={() => setActiveStep(1)}
                            tooltipPlacement='top'
                            isIcon={false}
                            isDisabled={dataLoading}
                          />
                          <NextButton
                            helpText='Continue'
                            onClick={handleSave}
                            btnText='Continue'
                            type='primary'
                            tooltipPlacement='top'
                            isIcon={false}
                            isLoading={dataLoading}
                          />
                        </>
                      ) :
                        (!published && <><PrevButton
                                btnText='Back'
                                type='secondary'
                        helpText={'Amend/Update'}
                          onClick={() => setActiveStep(2)}
                        tooltipPlacement='top'
                                isIcon={false}
                          isDisabled={dataLoading}
                              />
                      <SaveButton
                                  btnText={'Publish'}
                            onClick={handlePublish}
                        tooltipPlacement='top'
                                  helpText={'Publish'}
                                  isIcon={false}
                            isLoading={dataLoading}
                            isDisabled={dataLoading}
                          /></>))
                  )}
                </>
              </ModalControls>
          </>
        )}
      </Modal>
    </EntityProvider>
  )
}

export default DataTable
