.w-100{
    width: 100%;
}
.zebra-row .ant-table-column-sort {
    background-color: #e5e5e5;
}
/* Hide the sort icon by default */
.ant-table-wrapper .ant-table-thead th .sortIcon {
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* Show the sort icon only when the column is actively sorted */
.ant-table-wrapper .ant-table-thead th.ant-table-column-sort .sortIcon {
    opacity: 1;
}

/* show sort icon on hover */
.ant-table-wrapper .ant-table-thead th:hover .sortIcon {
    opacity: 1;
}


.ant-table-tbody >tr:not(.ant-table-expanded-row):has(input[type="checkbox"])  {
    cursor: pointer;
  }
  
  /* Disable pointer for the expand column */
  .ant-table-row-expand-icon-cell {
    cursor: default !important;
  }
  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #1677ff;
    border-color: #1677ff;
}
  
.dtModalRoot {
    width: 100vw !important;
    min-height: 90vh !important;
    height: fit-content !important;
    max-height: 90vh !important;
    position: relative;
  }
  
  .dtModalContent {
    overflow: auto !important;
    width: 100% !important;
    height: calc(90vh - 107px) !important;
    padding: 0 !important;
  }
  .dt-content-root {
    margin:10px 20px 10px 1rem;
  }
  