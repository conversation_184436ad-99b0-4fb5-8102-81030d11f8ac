import { EditorAppSDK } from "contentful-ui-extensions-sdk"
import { getFieldsData, updateDvJsonData } from "../DataVisualization/utils"

export interface TableModalPropsI {
  sdk: EditorAppSDK
  entryId: string
}

export const tableDefaults={
    size:'lg',
    isExpandable:true,
    isSelectable:true,
    isZebraStrip:false,
  isSortable: true,
  isPagination: true,
  noDataErrorMsg: 'No data to view'
}


export const UpdateEntryData = async (
  id: string,
  data: any,
  currentLocale: string
) => {
  if (id) {
    const entryData = await getFieldsData(id)
    let jsonData = { ...entryData }
    jsonData = {
      ...jsonData,
      data: data?.tableData && {
        [currentLocale]: {
          content: [
            {
              content: [
                {
                  data: {},
                  marks: [],
                  nodeType: 'text',
                  value: JSON.stringify(data?.tableData),
                },
              ],
              data: {},
              nodeType: 'paragraph',
            },
          ],
          data: {},
          nodeType: 'document',
        },
      },
      source: data?.tableAsset && {
        [currentLocale]: {
          sys: {
            type: 'Link',
            linkType: 'Asset',
            id: data?.tableAsset?.sys?.id,
          },
        },
      },
    }
   
    if (!data?.tableData) {
      delete jsonData.data
    }
    if (!data?.tableAsset) {
      delete jsonData.source
    }
    await updateDvJsonData(jsonData, id)
  }
}

export const formatTabledata = (parsedData: any) => {
  if (!parsedData || parsedData.length < 1) return { columns: [], dataSource: [] }

  // Extract column headers from the first row (case-sensitive)
  const allColumns = parsedData?.[0]?.map((col) => col?.trim())

  // Filter columns: Exclude those starting with "Exp" for display
  const columns = allColumns?.filter((col) => !col?.startsWith("Exp"))

  // Extract rows from the rest of the data
  const dataSource = parsedData?.slice(1)?.map((row, index) => {
    const rowData: Record<string, any> = { key: (index + 1).toString() }

    // Map data and ensure keys are lowercase
    allColumns?.forEach((col, colIndex) => {
      const key = col?.startsWith("Exp") ? col?.replace(/^Exp-?/i, "")?.trim()?.toLowerCase() : col?.trim().toLowerCase()
      let value = row?.[colIndex]
      // Ensure empty values and "-" are replaced with an empty string
      rowData[key] = (!value || value?.toString().trim() === '-') ? '' : value
    })

    return rowData
  })

  return { columns, dataSource }
}
