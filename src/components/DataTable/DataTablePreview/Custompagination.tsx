import React from "react";
import GenericIcon from "../../Icons/SysIcon";
import styles from './index.module.scss';
interface CustomPaginationProps {
    total: number;
    pageSize: number;
    current: number;
    onChange: (page: number, pageSize: number) => void;
  }
export const CustomPagination : React.FC<CustomPaginationProps>= ({ total, pageSize, current, onChange }) => {
    const totalPages = Math.ceil(total / pageSize);
  const active = current >= totalPages ? totalPages : current
    return (
      <div className={`${styles.customPagination} fs3`}>
            <div className={styles.paginationCol}>
        <span className={styles.pageSizeSelector}>
          Items per page: 
          <select onChange={(e) => onChange(1, Number(e.target.value))}>
            {[10, 20, 50,100].map(size => (
              <option key={size} value={size}>{size}</option>
            ))}
          </select>
        </span>
        <span className={styles.pageInfo}>
            {`${(active - 1) * pageSize + 1} - ${Math.min(active * pageSize, total)} of ${total} items`}
        </span>
            </div>
            <div className={styles.paginationCol}>
                <span className={styles.pageNav}>
          <button onClick={() => onChange(current - 1,pageSize)} disabled={current === 1}>
              <GenericIcon iconColour="cn2" as='span' size="sm" icon="LeftChevron" htmlAttr={{ style: { cursor: 'inherit' } }} />
          </button>
          <span className={styles.pageOfTotal}> Page {active} of {totalPages}</span>
                    <button className={styles.rightChevron} onClick={() => onChange(current + 1, pageSize)} disabled={current === totalPages}>
              <GenericIcon iconColour="cn2" as='span' size="sm" icon="RightChevron" htmlAttr={{ style: { cursor: 'inherit' } }} />
          </button>
        </span>
            </div>
      </div>
    );
  };
  