'use client'
import { ConfigProvider, Input, Table } from 'antd'
import React, { useContext, useEffect, useRef, useState } from 'react'
import Share from '../../../assets/icons/Share'
import { GlobalContext } from '../../../contexts/globalContext'
import { ICONS } from '../../../globals/types'
import { getParsedData } from '../../DataVisualization/utils'
import GenericIcon from '../../Icons/SysIcon'
import Kernel from '../../Kernel'
import '../index.scss'
import { formatTabledata, tableDefaults } from '../utils'
import { CustomPagination } from './Custompagination'
import { DataTableD } from './defaults'
import styles from './index.module.scss'
import { DataTableI } from './interface'
export default function DataTablePreview(props: DataTableI) {

  const { Search } = Input;
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: DataTableI = {
    ...DataTableD,
    ...props,
    isPresent: true,
    isVisible: true,
    isInteractive: true,
    isEnabled: true,
    htmlAttr: {
      ...props.htmlAttr,
      className: 'w-100'
    },
  }

  const [searchText, setSearchText] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const inputRef = useRef(null);
  const { currentLocale } = useContext(GlobalContext)
  const [data, setData] = useState<any>({})
  const [isDataLoaded, setIsDataLoaded] = useState(false)

  useEffect(() => {
    const fetch = async () => {
      const assetData = props?.asset
      let fileUrl = assetData?.fields?.file?.[currentLocale].url
      const fileData = fileUrl ? await getParsedData(fileUrl) : []
      const { columns, dataSource } = formatTabledata(fileData)
      setData((pre) => {
        return {
          ...pre,
          columns: columns,
          dataSource: dataSource,
          config: {
            ...tableDefaults,
            ...props.data
          },
        }
      })

      setIsDataLoaded(true)
    }

    fetch()
  }, [props])

  const generatedColumns = data?.columns?.map((item, index) => ({
    title: item.charAt(0).toUpperCase() + item.slice(1),
    dataIndex: item.toLowerCase(),
    ...(data?.config?.isSortable && {
      sortIcon: ({ sortOrder }) => {
      let icon: ICONS = 'ChevronExpand'
      if (sortOrder === "ascend") {
        icon = 'UpChevron'
      } else if (sortOrder === "descend") {
        icon = 'DownChevron'
      }
        return <GenericIcon as={'span'} icon={icon} size='sm' htmlAttr={{ className: 'sortIcon' }} />;
    },
    sorter: (a, b) => {
      const valueKey = item.toLowerCase();

      const parseValue = (val) => {
        if (typeof val === 'number') return val;

        if (typeof val === 'string') {
          const currencyLike = val.trim().match(/^[-\s₹$€£¥₩₽₺₪₫฿₴₦]?\s*-?\s?[\d,]+(\.\d+)?%?$/)

          if (currencyLike) {
            const cleaned = val.replace(/[^0-9.-]/g, '') // keep only digits, dot, dash
            const parsed = parseFloat(cleaned);
            return isNaN(parsed) ? val.toLowerCase() : parsed
          }
          // Otherwise, treat as plain string
          return val.toLowerCase();
        }

        return 0; // Fallback
      };

      const valA = parseValue(a[valueKey])
      const valB = parseValue(b[valueKey])

      if (typeof valA === 'number' && typeof valB === 'number') {
        return valA - valB;
      }

      if (typeof valA === 'string' && typeof valB === 'string') {
        return valA.localeCompare(valB);
      }

      return 0;
      },
    }),
    ellipsis: true,
    showSorterTooltip: false
    //width: `${100 / data?.columns?.length}%`,
  }));

  // Expand search input when clicking the icon
  const handleSearchClick = () => {
    setIsExpanded(true);
    setTimeout(() => inputRef?.current?.focus(), 100); // Auto-focus input
  };

  // Collapse when clicking outside
  const handleBlur = () => {
    setTimeout(() => {
      if (inputRef?.current && inputRef.current !== document.activeElement && searchText.trim() === '') {
        setIsExpanded(false);
      }
    }, 100)
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };

  const getRowClassName = (record, index) => {
    return `${styles[`${data?.config?.size}Row`]} ${data?.config?.isZebraStrip ? index % 2 === 0 ? `${styles.evenRow} zebra-row` : styles.oddRow : styles.tableRow} `;
  };

  const handleSearch = (value) => {
    setSearchText(value);
    setCurrentPage(1); 
  };

  // Filter logic for all columns based on the global search
  const filteredData = data?.dataSource?.filter((item) => {
    return Object.values(item)?.join(' ')?.toLowerCase()?.includes(searchText?.toLowerCase()); 
  });

  const shareContainer = (
    <div className='shareContainer  right-auto'>
      <p>Sharing options are only available on the front end.</p>
    </div>
  )

  return <Kernel {...updatedProps}>
    <div className={styles.TableToolBar}>
      {/* Search Input & Icon */}
      <div className={styles.searchConatiner}>
          <Input
          className={`${styles.searchInput} ${(isExpanded || searchText) ? styles.expanded : ''}`}
            ref={inputRef}
            placeholder="Filter table"
            onBlur={handleBlur}
          onChange={(e) => handleSearch(e.target.value)}
          prefix={<GenericIcon icon='Search' />}
          value={searchText}
          suffix={searchText && <GenericIcon as={'span'} size='md' icon='Close' htmlAttr={{ onClick: () => setSearchText('') }} />}
          />
        <GenericIcon
          icon='Search'
          size='sm'
          htmlAttr={{ onClick: handleSearchClick, className: `${styles.searchIcon} ${(isExpanded || searchText) ? styles.expanded : ''}` }}
        />
      </div>
      <div className='flex gap-3 cursor-pointer hoverContainer'>
        <div>
          <Share />
        </div>{' '}
        <span style={{ fontSize: '18px' }}>Share</span>
        {shareContainer}
      </div>
    </div>
    <ConfigProvider
      theme={{
        components: {
          Table: {
            headerBg: "#000B3D",
            headerSortHoverBg: '#545c7d',
            headerSortActiveBg: '#333c64',
            headerColor: '#fff',
            rowHoverBg: '#e5e5e5',
            borderColor: '#c6c6c6',
            rowSelectedBg: '#e6eafb',
            rowSelectedHoverBg: '#b0bcf3',
            rowExpandedBg: '#fff',
            selectionColumnWidth: '30px',
            bodySortBg: data?.config?.isZebraStrip ? '' : '#f9f9f9'
          },
        },
      }}
    >
    <Table
        className={`${styles.table}`}
        locale={{
          emptyText: data?.config?.noDataErrorMsg,
        }}
      scroll={{ x: 'max-content' }}
        rowSelection={data?.config?.isSelectable ? {
        type: 'checkbox', // Toggle between checkbox and radio
        ...rowSelection, // Additional row selection properties
        } : undefined}
        onRow={(record) => ({
          onClick: (event) => {
            const target = event?.target as HTMLElement;
            // Prevent selection when clicking on the expand icon column
            if (target?.closest(".ant-table-row-expand-icon-cell")) return;
            setSelectedRowKeys((prevKeys) =>
              prevKeys.includes(record.key)
                ? prevKeys.filter((key) => key !== record.key) // Deselect if already selected
                : [...prevKeys, record.key] // Select if not selected
            );
          },
        })}
      dataSource={filteredData}
      columns={generatedColumns}
        expandable={data?.config?.isExpandable ? {
        columnWidth: '30px',
          expandedRowRender: (record) => (<div className={data?.config?.isSelectable ? styles.expContentWSel : styles.expContent}>
            <h6 style={{ margin: 0 }}> {record?.heading} </h6>
          <p>{record?.description} </p>
          </div>
          ),
        expandIcon: ({ expanded, onExpand, record }) =>
        (
          (!!record.heading || !!record.description) ? <GenericIcon as={'span'} size='sm' icon={expanded ? 'UpChevron' : 'DownChevron'} htmlAttr={{ onClick: e => onExpand(record, e), className: styles.expandedIcon }} /> : null
        ),
          rowExpandable: (record) => !!record.heading || !!record.description,
          showExpandColumn: true,
        } : undefined}
      rowClassName={getRowClassName}
        pagination={data?.config?.isPagination ? {
        current: currentPage,
        pageSize: pageSize,
        total: filteredData?.length,
        onChange: (page, size) => {
          setCurrentPage(page);
          setPageSize(size);
        },
        position: ['none', 'none'],
        defaultPageSize: 10,
        } : false} 
    />
    </ConfigProvider>
    {filteredData?.length > 0 && (data?.config?.isPagination ? <CustomPagination
      total={filteredData?.length}
      pageSize={pageSize}
      current={currentPage}
      onChange={(page, size) => {
        setCurrentPage(page);
        setPageSize(size);
      }}
    /> : <></>)}
 
  </Kernel>
}
