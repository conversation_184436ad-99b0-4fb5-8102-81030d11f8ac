import { EditorAppSDK } from '@contentful/app-sdk'
import { Autocomplete } from '@contentful/f36-components'
import { useSDK } from '@contentful/react-apps-toolkit'
import * as React from 'react'

export default function AutocompleteContentType() {
  const sdk = useSDK<EditorAppSDK>()
  const contentTypes = sdk.space.getCachedContentTypes()
  const [filteredItems, setFilteredItems] = React.useState(contentTypes)

  // we use 'toLowerCase()' to make the search case insensitive
  const handleInputValueChange = (value) => {
    const newFilteredItems = contentTypes.filter((item) =>
      item.name.toLowerCase().includes(value.toLowerCase())
    )
    setFilteredItems(newFilteredItems)
  }

  // This function will be called once the user selects an item in the list of options
  const handleSelectItem = (item) => {
    if (item?.sys?.id) {
      sdk.navigator.openNewEntry(item?.sys?.id, { slideIn: true })
    }
  }

  return (
    <Autocomplete
      isOpen={true}
      closeAfterSelect
      items={filteredItems}
      itemToString={(item) => item.name}
      renderItem={(item) => item.name}
      onInputValueChange={handleInputValueChange}
      onSelectItem={handleSelectItem}
    />
  )
}
