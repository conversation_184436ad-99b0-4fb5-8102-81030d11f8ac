import React from 'react'

const alertStyles = {
  success: 'bg-green-100 text-green-800 border-green-400',
  error: 'bg-red-100 text-red-800 border-red-400',
  warning: 'bg-yellow-100 text-yellow-800 border-yellow-400',
  info: 'bg-primary2-50 text-primary2-600 border-primary2-100',
}
type AlertType = 'success' | 'error' | 'warning' | 'info'

// Define the properties that the Alert component accepts.
interface AlertProps {
  type?: AlertType // Optional type of alert to adjust styles.
  title?: string // Optional title of the alert.
  description?: string // Optional description of the alert.
  hasIcon?: boolean // Determines if an icon should be shown.
  icon?: React.ReactNode // Optional icon to display.
  className?: string // Additional classes for styling.
  onClose?: () => void // Optional function to call on close.
}
const Alert: React.FC<AlertProps> = ({
  type = 'info',
  title,
  description,
  hasIcon = false,
  icon,
  className = '',
  onClose,
}) => {
  const baseStyle = 'p-4 rounded border flex items-start space-x-3'
  const style = `${baseStyle} ${alertStyles[type]} ${className}`

  return (
    <div className={style}>
      {hasIcon && icon && (
        <h5 className='flex-shrink-0 h-full flex justify-center items-center'>
          {icon}
        </h5>
      )}
      <div className='flex-1'>
        {title && <h3 className='font-semibold text-xl'>{title}</h3>}
        {description && <p className='text-sm'>{description}</p>}
      </div>
      {onClose && (
        <button
          onClick={onClose}
          className='text-gray-500 hover:text-gray-800 focus:outline-none'
        >
          &times;
        </button>
      )}
    </div>
  )
}

export default Alert
