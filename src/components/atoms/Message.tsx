// src/utils/CustomMessage.ts
import { message } from 'antd'

// const CustomMessage = {
//   success: (content: string, duration = 3) => {
//     message.success(content, duration);
//   },
//   error: (content: string, duration = 3) => {
//     message.error(content, duration);
//   },
//   info: (content: string, duration = 3) => {
//     message.info(content, duration);
//   },
//   warning: (content: string, duration = 3) => {
//     message.warning(content, duration);
//   },
//   loading: (content: string, duration = 0) => {
//     message.loading(content, duration);
//   },
// };

export default message
