import { HomeAppSDK } from '@contentful/app-sdk'
import { Box, Select } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import logo from '../../assets/page_settings.png'
import { Domains, domainsConfig } from '../../components/Crosspost/utils'
import GenericIcon from '../../components/Icons/SysIcon'
import {
  fetchGlobalConfigData,
  fetchMSAConfigData,
} from '../../redux/slices/dashboard/dvSlices'
import { useAppDispatch } from '../../redux/store'
import CacheControl from '../ConfigurationEngine/TabsPanels/Cache'
import Experimentation from '../ConfigurationEngine/TabsPanels/Experimentation'
import HostedFile from '../ConfigurationEngine/TabsPanels/SiteSettings/HostedFiles'
import { templateName, templates } from '../DataVisualization/utils'
import DashboardIntro from './Components/DashboardIntro'
import UpcomingFeatComp from './Components/UpcomingFeatComp'
import Data from './dashboard.json'
import DataVisualizationDashboard from './DataVisualization'
import './index.scss'
import { Category, DashboardDataType } from './interface'
import MSALanguages from './MSALanguages'
import DomainNotifications from './Notifications/DomainNotifications'
import GlobalNotifications from './Notifications/GlobalNotifications'
import { setIsAddNewNotification } from '../../redux/slices/dashboard/notificationsSlice'

const Dashboard = ({ sdk }: { sdk: HomeAppSDK }) => {
  const DashboardData: DashboardDataType = Data

  const dispatch = useAppDispatch()

  const [dropdownValue, setDropdownValue] = useState<'global' | Domains>(
    'global'
  )

  const [selectedDomain, setSelectedDomain] = useState<Domains>('agl')

  const [selectedCategory, setSelectedCategory] = useState('')

  const [selectedSubCategory, setSelectedSubCategory] = useState('')

  const [selectedType, setSelectedType] = useState<'global' | 'msa'>('global')

  const [selectedDvTemplate, setSelectedDvTemplate] =
    useState<templateName>('Line chart')

  /**
   * Handles the selection of a domain from the dropdown.
   *
   * Updates the selected type based on the input. If the input
   * domain is not 'global', sets the selected domain. Resets the
   * dropdown value, selected category, and subcategory. Dispatches
   * an action to indicate that a new notification is not being added.
   *
   * @param {string} x - The domain or 'global' as selected from the dropdown.
   */

  const handleDomainSelect = (x: string) => {
    setSelectedType(x === 'global' ? 'global' : 'msa')
    if (x !== 'global') setSelectedDomain(x as Domains)
    setDropdownValue(x as 'global' | Domains)
    setSelectedCategory('')
    setSelectedSubCategory('')
    dispatch(setIsAddNewNotification(false))
  }

  /**
   * Handles the selection of a category from the category dropdown.
   *
   * Updates the selected category if the input category is not the same as the
   * current selected category. If the input category is the same as the current
   * selected category, resets the selected category and subcategory. Dispatches
   * an action to indicate that a new notification is not being added.
   *
   * @param {string} x - The category selected from the dropdown.
   */
  const handleCategorySelect = (x: string) => {
    if (selectedCategory !== x) setSelectedCategory(x)
    else {
      setSelectedCategory('')
      setSelectedSubCategory('')
    }
    dispatch(setIsAddNewNotification(false))
  }

  const handleDvtemplate = (x: templateName) => setSelectedDvTemplate(x)

  useEffect(() => {
    if (selectedType === 'global') dispatch(fetchGlobalConfigData())

    if (selectedType === 'msa' && selectedDomain)
      dispatch(fetchMSAConfigData(selectedDomain))
  }, [dispatch, selectedDomain, selectedType])

  // Mapper For Global Tab Component
  const globalCategoryComponent = {
    'Data Visualisation': (
      <DataVisualizationDashboard selectedTemplate={selectedDvTemplate} />
    ),
    Experimentation: (
      <UpcomingFeatComp
        bgColor='#e8f0fc'
        borderColor='#bdd8ff'
        color='#1040a3'
        title='Experimentation'
        aboutTitle='Experimentation'
        about='We are excited to share that our team is developing an Experimentation feature. This will enable team to run experiments and compare different versions of the content to make data-driven decisions. Stay tuned for updates as we refine this powerful tool!'
        desc='With content growth and increasing traffic, A/B testing is crucial for optimizing website performance by comparing different content, design, and feature versions based on user behavior. This data-driven approach ensures changes align with user preferences, enhancing engagement, conversions, and satisfaction while reducing risks and costs through effective, iterative testing.'
      />
    ),
    Documentation: (
      <UpcomingFeatComp
        bgColor='#edf7eb'
        borderColor='#cce3c2'
        color='#2d6a4f'
        title='Documentation'
        desc='Documenting the codebase and creating guides enhances team efficiency and prevents the project from becoming a black box. Clear documentation aids onboarding, promotes consistency and collaboration, and serves as a reference for troubleshooting and maintenance. It supports scalability, code reuse, and knowledge transfer, ultimately improving communication and clarifying requirements.'
      />
    ),
    'Content Discovery': (
      <UpcomingFeatComp
        bgColor='#f2e6ff'
        borderColor='#d1c1ff'
        color='#6b32af'
        title='Content Discovery'
        desc='MSA-powered Crossposting enables publishing the same content across multiple websites, maintaining relevance, user interest, and building a strong network of interconnected web properties, thereby increasing traffic. Real-time notifications and the AI-enhanced MSA Search System improve content discovery, accuracy, and personalized recommendations, automating content organization for better user experience and increased engagement, traffic, and conversions.'
      />
    ),
    Notifications: <GlobalNotifications sdk={sdk} />,
  }

  // Mapper For MSA Tab Component
  const msaCategoryComponent = {
    Notifications: <DomainNotifications domain={selectedDomain} sdk={sdk} />,
    Experimentation: <Experimentation selectedDomain={selectedDomain} />,
    Cache: <CacheControl selectedDomain={selectedDomain} />,
    'Hosted Files': <HostedFile selectedDomain={selectedDomain} />,
    Languages: <MSALanguages selectedDomain={selectedDomain} />,
  }

  return (
    <div className={'dashboardRoot'}>
      <div className='dashboardContainer'>
        <div className='sidebar'>
          <Box
            style={{
              display: 'flex',
              paddingTop: '20px',
              flexDirection: 'row',
              marginBottom: '10px',
              padding: '10px',
              alignItems: 'center',
              justifyContent: 'spaceBetween',
              gap: '5px',
              margin: '10px 0',
            }}
          >
            <img src={logo} height={38} width={38} alt='' className='pt-2' />
            <Box
              style={{
                display: 'flex',
                alignItems: 'center',
                maxWidth: '219px',
                border: '1px solid #CFD9E0',
                boxShadow: 'inset 0px 2px 0px rgba(225, 228, 232, 0.2)',
                borderRadius: '6px',
                paddingInline: '10px',
              }}
            >
              <p
                style={{
                  paddingBottom: '1px',
                }}
              >
                Configure
              </p>
              <Select
                value={dropdownValue}
                onChange={(event: React.ChangeEvent<HTMLSelectElement>) => {
                  handleDomainSelect(event.target.value)
                }}
                size='small'
                testId='original-domain-select'
                className='customSelect'
              >
                <Select.Option value={'global'}>Global Settings</Select.Option>
                {domainsConfig?.map((item) => (
                  <Select.Option key={item.key} value={item.key}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>
            </Box>
          </Box>
          <hr className='' />
          {DashboardData[
            selectedType as keyof DashboardDataType
          ]?.categories.map((item: Category) => {
            if (!item.isEnabled) return null

            return (
              <div className='sidebar'>
                <h6
                  className={`dropdownHeader ${
                    selectedCategory === item.title ? 'active' : ''
                  }`}
                  onClick={() => handleCategorySelect(item.title)}
                >
                  {item.title}
                  {item?.subcategories && item.subcategories.length > 0 && (
                    <span className={'dropdownArrow'}>
                      <GenericIcon
                        icon={'DownChevron'}
                        htmlAttr={{ className: 'icon' }}
                        size={'sm'}
                      />
                    </span>
                  )}
                </h6>
                {item?.subcategories && item.subcategories.length > 0 && (
                  <ul
                    className={`dropdownContent ${
                      selectedCategory === item.title ? 'show' : ''
                    }`}
                  >
                    <Select
                      className='dvTemplate'
                      id='dv-template'
                      name='dv-template'
                      value={selectedDvTemplate}
                      onChange={(e: any) => handleDvtemplate(e.target.value)}
                    >
                      {templates.map((template) => (
                        <Select.Option
                          key={template.name}
                          value={template.name}
                        >
                          {template.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </ul>
                )}
              </div>
            )
          })}
        </div>
        <div className='content'>
          {!selectedCategory && !selectedSubCategory && <DashboardIntro />}
          {selectedType === 'global' &&
            globalCategoryComponent[
              selectedCategory as keyof typeof globalCategoryComponent
            ]}
          {selectedType === 'msa' &&
            selectedDomain &&
            msaCategoryComponent[
              selectedCategory as keyof typeof msaCategoryComponent
            ]}
        </div>
      </div>
    </div>
  )
}

export default Dashboard
