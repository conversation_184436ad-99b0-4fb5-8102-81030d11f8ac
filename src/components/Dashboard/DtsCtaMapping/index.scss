.dtsCtaMappingRoot {
	display: flex;
	height: 100%;
  
	.dtsCtaMappingInnerRoot {
	  width: 100%;
	  display: flex;
  
	  .dtsCtaSidebar {
		width: 550px !important;
		background: rgb(247, 249, 250);
  
		.dtsCtaResizer {
		  border: 3px solid black;
		}
  
		.dtsCtaAsideTopDiv {
		  padding: 16px 16px 0 16px;
  
		  .dtsCtaSearchRoot {
			margin-bottom: 12px;
		  }
  
		  .dtsCtaStylingRoot {
			margin-bottom: 18px;
			display: flex;
			justify-content: center;
		  }
		}
  
		.dtsCtaCategoryRoot,
		.dtsCtaSubCategoryRoot {
		  overflow-y: auto;
  
		  .dtsCtaSubCategoryInner {
			padding: 16px 0;
  
			.dtsCtaHover:hover {
			  background-color: rgba(207, 217, 224, 0.5);
			}
  
			a {
			  text-decoration: none;
			  color: black;
			  font-size: 14px;
			}
		  }
  
		  .dtsCtaCategoryHeading {
			margin: 0;
			padding-left: 16px;
			padding-top: 10px;
		  }
  
		  ul {
			li.active {
			  background-color: rgba(207, 217, 224, 0.5);
			}
  
			li {
			  list-style: none;
			  font-weight: 600;
  
			  &:hover {
				background-color: rgba(207, 217, 224, 0.5);
			  }
  
			  a {
				text-decoration: none;
				color: black;
				font-size: 14px;
			  }
			}
		  }
		}
  
		.dtsCtaTagsRoot {
		  padding: 16px;
		  gap: 10px;
		  display: flex;
		  flex-direction: column;
		}
	  }
  
	  .dtsCtaMainRoot {
		width: 100%;
		height: 100%;
		padding: 16px;
  
		.dtsCtaPageSettingPreviewRoot {
		  height: 100% !important;
		  width: 100%;
		  display: flex;
		  justify-content: flex-start;
		  align-items: start;
		}
	  }
	}
  
	.dtsCtaFooterInner {
	  display: flex;
	  justify-content: center;
	}
  }
  