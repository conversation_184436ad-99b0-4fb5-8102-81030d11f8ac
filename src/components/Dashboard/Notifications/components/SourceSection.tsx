import {
  Box,
  Button,
  Select,
  TextInput,
  Tooltip,
} from '@contentful/f36-components'
import { LinkIcon } from '@contentful/f36-icons'
import React from 'react'
import Info from '../../../../assets/icons/Info'
import FormControlComp from '../../../Shared/FormControlComp'
import {
  DomainNotiState,
  GlobalNotiState,
  UpdateNotiStateFunction,
} from '../utils/notifications.interface'
import CustomEntryCard from './CustomEntryCard'

/**
 * A type guard function that checks if a given state is a DomainNotiState
 * based on the presence of the `stickyNotificationTemplate` property.
 *
 * @param state - the state to check
 * @returns a boolean indicating if the state is a DomainNotiState
 */
function isDomainNotiState(
  state: DomainNotiState | GlobalNotiState
): state is DomainNotiState {
  return 'stickyNotificationTemplate' in state // This property is unique to DomainNotiState
}

function SourceSection({
  state,
  updateState,
  CTASelector,
  pageSelector,
  carouselSelector,
  carouselRemove,
  fromGlobal,
}: {
  state: DomainNotiState | GlobalNotiState
  updateState: UpdateNotiStateFunction<DomainNotiState | GlobalNotiState>
  CTASelector?: any
  pageSelector?: any
  carouselSelector?: any
  carouselRemove?: any
  fromGlobal?: boolean
  fromMsa?: boolean
}) {
  const {
    descriptionSource,
    selectedCTA,
    selectedPage,
    titleSource,
    typeOfNotification,
    url,
  } = state

  return (
    <Box className='w-full flex justify-start items-start flex-col'>
      <Box className='w-full py-4'>
        <h6 className='my-2 flex gap-1'>
          Source{' '}
          <Tooltip
            placement='right'
            content={
              'Add Source Details from either Page or CTA or directly add external URL'
            }
          >
            <Info />
          </Tooltip>
        </h6>
        <hr className='endHorizontalLine w-full' />
      </Box>
      {!selectedCTA && !url && (typeOfNotification === '3' || fromGlobal) && (
        <>
          <FormControlComp
            label='Page Source'
            tooltip='Select existing page to autofill the details'
            isRequired={fromGlobal}
          >
            {selectedPage ? (
              <CustomEntryCard
                data={selectedPage}
                onRemoveEntry={() =>
                  updateState({
                    selectedPage: null,
                  })
                }
                field={'formFloating'}
              />
            ) : (
              <Button
                onClick={() => {
                  pageSelector && pageSelector()
                }}
                variant='secondary'
                startIcon={<LinkIcon />}
                size='small'
              >
                Link existing entry
              </Button>
            )}
          </FormControlComp>
          <Box className='h-5' />
        </>
      )}

      {typeOfNotification === '1' &&
        isDomainNotiState(state) &&
        state?.stickyNotificationTemplate !== 'Carousel' && (
          <>
            <FormControlComp
              label='CTA Source'
              tooltip='Select existing CTA to autofill the details'
              isRequired={typeOfNotification === '1'}
            >
              {selectedCTA ? (
                <CustomEntryCard
                  data={selectedCTA}
                  onRemoveEntry={() =>
                    updateState({
                      selectedCTA: null,
                    })
                  }
                  field={'formFloating'}
                />
              ) : (
                <Button
                  onClick={() => {
                    CTASelector && CTASelector()
                  }}
                  variant='secondary'
                  startIcon={<LinkIcon />}
                  size='small'
                >
                  Link existing entry
                </Button>
              )}
            </FormControlComp>
            <Box className='h-7' />
          </>
        )}

      {!selectedPage && (typeOfNotification === '3' || fromGlobal) && (
        <>
          <FormControlComp
            label='External URL'
            tooltip='Add manual URL or select existing entry'
            isRequired={typeOfNotification === '3' || fromGlobal}
          >
            <TextInput
              value={url}
              placeholder='Enter URL'
              onChange={(e) =>
                updateState({
                  url: e?.target?.value,
                })
              }
            />
          </FormControlComp>
        </>
      )}

      {selectedPage && (
        <>
          <Box className='flex justify-start items-center gap-3 w-full'>
            <FormControlComp
              label='Notification Title From:'
              tooltip='Choose option to copy title from'
            >
              <Select
                onChange={(e) => {
                  const titleSource = e?.target?.value
                  const title =
                    selectedPage?.fields?.[titleSource]?.['en-CA'] || ''
                  updateState({
                    titleSource,
                    title,
                  })
                }}
                className='w-full'
                value={titleSource}
              >
                <Select.Option value='' isDisabled>
                  Select
                </Select.Option>
                <Select.Option value='afsCardTitle'>AFS Title</Select.Option>
                <Select.Option value='seoTitle'>SEO Title</Select.Option>
                <Select.Option value='shortTitle'>Algolia Title</Select.Option>
              </Select>
            </FormControlComp>
          </Box>
          <Box className='h-3' />
          <Box className='flex justify-start items-center gap-3 w-full'>
            <FormControlComp
              label='Notification Description From:'
              tooltip='Choose option to copy Notification Description from'
            >
              <Select
                onChange={(e) => {
                  const descriptionSource = e?.target?.value
                  const description =
                    selectedPage?.fields?.[descriptionSource]?.['en-CA']

                  updateState({
                    descriptionSource,
                    description,
                  })
                }}
                className='w-full'
                value={descriptionSource}
              >
                <Select.Option value='' isDisabled>
                  Select
                </Select.Option>
                <Select.Option value='afsCardTitle'>AFS Title</Select.Option>
                <Select.Option value='seoTitle'>SEO Title</Select.Option>
                <Select.Option value='shortTitle'>Algolia Title</Select.Option>
                <Select.Option value='seoDescription'>
                  SEO Description
                </Select.Option>
              </Select>
            </FormControlComp>
          </Box>
        </>
      )}

      {typeOfNotification === '1' &&
        isDomainNotiState(state) &&
        state?.stickyNotificationTemplate === 'Carousel' && (
          <FormControlComp
            label='Carousel Data'
            tooltip='Select existing carousels to add to notification'
            isRequired={
              typeOfNotification === '1' &&
              state?.stickyNotificationTemplate === 'Carousel'
            }
          >
            <Box className='flex flex-wrap w-full gap-3'>
              {state?.carouselData?.map((carouselPage: any, index: number) => {
                return (
                  <CustomEntryCard
                    key={index}
                    data={carouselPage}
                    onRemoveEntry={() => {
                      carouselRemove && carouselRemove()
                    }}
                    field={'formFloating'}
                  />
                )
              })}
            </Box>
            <Box className='h-1' />
            <Button
              onClick={() => {
                carouselSelector && carouselSelector()
              }}
              variant='secondary'
              startIcon={<LinkIcon />}
              size='small'
            >
              Link existing entry
            </Button>
          </FormControlComp>
        )}
      {/*{(typeOfNotification === '3' || fromGlobal) &&*/}
      {/*  <>*/}
      {/*    <Box className="h-5" />*/}
      {/*    <FormControlComp*/}
      {/*      label="CTA Text"*/}
      {/*      tooltip={`Enter CTA Text for this Notification`}*/}
      {/*    >*/}
      {/*      <TextInput*/}
      {/*        value={ctaText}*/}
      {/*        placeholder="Enter Title"*/}
      {/*        onChange={(e) =>*/}
      {/*          updateState({*/}
      {/*            ctaText: e?.target?.value,*/}
      {/*          })*/}
      {/*        }*/}
      {/*      />*/}
      {/*    </FormControlComp>*/}
      {/*  </>*/}
      {/*}*/}
    </Box>
  )
}

export default SourceSection
