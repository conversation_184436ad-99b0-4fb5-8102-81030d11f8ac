import {
  Box,
  Button,
  ModalControls,
  Text,
  Tooltip,
} from '@contentful/f36-components'
import React from 'react'
import { setIsAddNewNotification } from '../../../../../redux/slices/dashboard/notificationsSlice'
import { useAppDispatch } from '../../../../../redux/store'
import NextButton from '../../../../Buttons/NextButton'
import PrevButton from '../../../../Buttons/PrevButton'
import SaveButton from '../../../../Buttons/SaveButton'
import {
  GlobalNotiState,
  UpdateNotiStateFunction,
} from '../../utils/notifications.interface'

/**
 * GlobalNotificationFooter
 *
 * This component renders the footer for the GlobalNotification wizard. It displays
 * the appropriate buttons and controls based on the current step of the wizard.
 * The footer includes navigation buttons to move between steps and a save button
 * to create the notification.
 *
 * @param {Object} props - The properties passed to the component
 * @param {GlobalNotiState} props.state - The state of the GlobalNotification wizard
 * @param {Function} props.updateState - Function to update the state of the wizard
 * @param {Function} props.checkStepDisable - Function to check if the current step is valid
 * @param {boolean} props.saveDisable - If the save button should be disabled
 * @param {Function} props.clearAllStates - Function to clear all the states of the wizard
 */

function GlobalNotificationFooter({
  state,
  updateState,
  checkStepDisable,
  saveDisable,
  clearAllStates,
}: {
  state: GlobalNotiState
  updateState: UpdateNotiStateFunction<GlobalNotiState>
  saveDisable: boolean
  checkStepDisable: () => boolean
  clearAllStates: () => void
}) {
  const dispatch = useAppDispatch()

  return (
    <Box className='flex w-full flex-col justify-end items-center'>
      <hr className='endHorizontalLine w-full' />
      <ModalControls className='pt-4'>
        {state?.status === 'success' ? (
          <Text fontColor='green500'> Notification created successfully</Text>
        ) : state?.status === 'error' ? (
          <Text fontColor='red500'> Notification creation failed</Text>
        ) : (
          <></>
        )}
        {state?.activeStep === 0 ? (
          <>
            <Tooltip content={'Cancel'} placement={'top'}>
              <Button
                variant='secondary'
                size={'small'}
                onClick={() => {
                  clearAllStates()
                  dispatch(setIsAddNewNotification(false))
                }}
              >
                Cancel
              </Button>
            </Tooltip>
            <NextButton
              helpText={
                !checkStepDisable() ? 'Please fill all the fields' : 'Next'
              }
              onClick={() => updateState({ activeStep: 1 })}
              isDisabled={!checkStepDisable()}
            />
          </>
        ) : state?.activeStep === 1 ? (
          <>
            <PrevButton
              helpText='Amend/Update'
              onClick={() => updateState({ activeStep: 0 })}
            />
            <NextButton
              helpText='Next'
              onClick={() => updateState({ activeStep: 2 })}
            />
            {/*<SaveButton*/}
            {/*  helpText='Create Notification'*/}
            {/*  onClick={() => updateState({ showConfirmBox: true })}*/}
            {/*  isLoading={state?.isLoading}*/}
            {/*/>*/}
          </>
        ) : (
          <>
            <PrevButton
              helpText='Amend/Update'
              onClick={() => updateState({ activeStep: 1 })}
              isDisabled={saveDisable || state?.isLoading}
            />
            <SaveButton
              helpText='Create Notification'
              onClick={() => updateState({ showConfirmBox: true })}
              isLoading={state?.isLoading}
              isDisabled={saveDisable || state?.isLoading}
            />
          </>
        )}
      </ModalControls>
    </Box>
  )
}

export default GlobalNotificationFooter
