import { HomeAppSDK } from '@contentful/app-sdk'
import { Box } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { generateRandomId } from '../../../../globals/firebase/utils'
import {
  fetchNotificationHistory,
  setIsAddNewNotification,
} from '../../../../redux/slices/dashboard/notificationsSlice'
import { useAppDispatch, useAppSelector } from '../../../../redux/store'
import ModalConfirm from '../../../ConfirmModal'
import { domainsConfig } from '../../../Crosspost/utils'
import PushNotificationSettings from '../components/PushNotificationSettings'
import {
  CreateRealTimeNotification,
  createSingleAsset,
  entryPageSelector,
  SaveNotificationAsHistory,
  selectSingleAsset,
} from '../utils'
import LoadingState from '../utils/LoadingState'
import {
  GlobalNotiState,
  RealTimeNotificationPayload,
  UpdateNotiStateFunction,
} from '../utils/notifications.interface'
import GlobalNotificationFooter from './components/GlobalNotificationFooter'
import GlobalNotificationHeader from './components/GlobalNotificationHeader'
import GlobalNotificationStep1 from './components/GlobalNotificationStep1'
import GlobalNotificationStep2 from './components/GlobalNotificationStep2'
import GlobalNotificationHistoryTable from './GlobalNotificationHistoryTable'

function GlobalNotifications({ sdk }: { sdk: HomeAppSDK }) {
  const { id, dataSource, isAddNewNotification } = useAppSelector(
    (state) => state.notificationData
  )

  const dispatch = useAppDispatch()

  // Initial state
  const initialState: GlobalNotiState = {
    status: '',
    showConfirmBox: false,
    titleSource: 'seoTitle',
    descriptionSource: 'seoDescription',
    activeStep: 0,
    extension: null,
    isLoading: false,
    externalLink: '',
    title: '',
    url: '',
    description: '',
    selectedCTA: null,
    thumbnail: {
      url: '',
      status: '',
    },
    badge: {
      url: '',
      status: '',
    },
    selectedPage: null,
    typeOfNotification: '2',
    notificationType: 'both',
    dataFetching: false,
    notificationDuration: '30',
    ctaText: 'Read more',
    ctaTarget: '_self',
    notificationCategory: ['general'],
    isSystemNotification: false,
    attentionRequired: false,
    groupingCategory: '',
    renotify: false,
    isSystemNotificationDismissible: false,
  }

  // fetch notification history
  useEffect(() => {
    dispatch(fetchNotificationHistory('MSA'))
  }, [])

  const [state, setState] = useState<GlobalNotiState>(initialState)

  const [saveDisable, setSaveDisable] = useState(false)

  /**
   * Updates the `state` by merging the `updates` object with the current `state`.
   * This is a controlled way of updating the state, as it ensures that the state
   * is updated in a predictable and controlled manner.
   * @param updates - The updates to be applied to the state. This should be a
   * partial of the `GlobalNotiState` object.
   */
  const updateState: UpdateNotiStateFunction<GlobalNotiState> = (updates) => {
    setState((prevState) => ({
      ...prevState,
      ...updates,
    }))
  }

  const clearAllStates = () => updateState(initialState)

  /**
   * Handles file input changes for the thumbnail and badge assets.
   * This function is called whenever the user selects a new file for either
   * the thumbnail or badge asset. It updates the state with the new asset
   * by calling the `createSingleAsset` function.
   *
   * @param event - The event object containing the newly selected file.
   * @param type - The type of asset being updated, either 'thumbnail' or 'badge'.
   */

  const handleFileChange = async (event: any, type: 'thumbnail' | 'badge') => {
    updateState({ dataFetching: true })
    const res = await createSingleAsset(event, sdk, type)
    updateState(res)
  }

  /**
   * Handles the selection of an asset for either thumbnail or badge.
   * This function is triggered when the user chooses an asset, updating
   * the state with the newly selected asset.
   *
   * @param type - Specifies the type of asset being selected, either 'thumbnail' or 'badge'.
   */

  const selectThumbnail = async (type: 'thumbnail' | 'badge') => {
    updateState({ dataFetching: true })
    const res = await selectSingleAsset(sdk, type)
    updateState(res)
  }

  /**
   * Selects a page for the Global Notification.
   * This function is called whenever the user clicks on the 'Select Page' button
   * for Global Notifications. It opens the Contentful UI's single-entry selector and
   * allows the user to select a page. The selected page is then added to the
   * state.
   */
  const pageSelector = async () => {
    updateState({ dataFetching: true })
    const res = await entryPageSelector(sdk)
    updateState(res)
  }

  const handleClose = () => updateState({ showConfirmBox: false })

  /**
   * Confirms the creation of a real-time notification and calls the
   * `HandleRealtimeNotification` function to create the notification.
   * The confirmation modal is closed before creating the notification.
   */
  const handleOnConfirm = async () => {
    handleClose()
    await HandleRealtimeNotification()
  }

  /**
   * Checks if the current step is valid by checking if all required fields
   * are filled in.
   *
   * @returns {boolean} True if all required fields are filled in, false otherwise.
   */
  const checkStepDisable = () => {
    const {
      title,
      description,
      url,
      thumbnail,
      externalLink,
      selectedPage,
      selectedCTA,
    } = state

    return !!(
      title &&
      description &&
      thumbnail.url &&
      (url || externalLink || selectedPage || selectedCTA)
    )
  }

  /**
   * Handles the creation of a real-time notification by creating a notification
   * for each domain in the `domainsConfig` array. It creates a promise for each
   * domain and resolves all of them using `Promise.all()`. If all promises are
   * resolved successfully, it saves the notification as history and sets the
   * `status` to 'success'. If any promise fails, it sets the `status` to 'error'.
   * Finally, it clears all states and disables the save button for 3 seconds.
   */
  const HandleRealtimeNotification = async () => {
    updateState({ isLoading: true })

    const {
      title,
      selectedPage,
      description,
      thumbnail,
      url,
      externalLink,
      notificationType,
      notificationDuration,
      ctaText,
      ctaTarget,
      notificationCategory,
      isSystemNotification,
      attentionRequired,
      badge,
      groupingCategory,
      renotify,
      isSystemNotificationDismissible,
    } = state

    const payload: RealTimeNotificationPayload = {
      title: title || selectedPage?.fields?.seoTitle?.['en-CA'],
      body: description || selectedPage?.fields?.seoDescription?.['en-CA'],
      icon: thumbnail?.url,
      url: url || externalLink || selectedPage?.fields?.slug?.['en-CA'],
      id: generateRandomId(),
      domain: '',
      type: notificationType,
      duration: notificationDuration,
      timeStamp: new Date(),
      ctaText,
      ctaTarget,
      category: JSON.stringify(notificationCategory),
      isSystemNotification: isSystemNotification.toString(),
      attentionRequired: attentionRequired.toString(),
      badge: badge?.url,
      tag: groupingCategory,
      renotify: renotify.toString(),
      isDismissible: isSystemNotificationDismissible.toString(),
    }

    // remove one11 as there is no deployment for it yet
    const domainsArray = domainsConfig
      .filter((d) => d.domainKey !== 'domainOne11Com')
      .map((d) => d.key)

    // create notification promise for all domains
    const promises = domainsArray.map(async (domain) => {
      let data: any = { ...state }
      return await CreateRealTimeNotification({ state: data, domain })
    })

    const results = await Promise.all(promises)
    const allSuccessful = results.every((res) => res === 'success')
    updateState({
      status: allSuccessful ? 'success' : 'error',
      isLoading: false,
    })

    setSaveDisable(true)

    try {
      if (allSuccessful) {
        // once notification has been created, save it as history
        await SaveNotificationAsHistory({
          data: payload,
          dataSource,
          id: id || '',
          domain: 'global',
          type: 'realTime',
        })
        dispatch(fetchNotificationHistory('MSA'))
      }
    } catch (e) {
      console.log('error', e)
    } finally {
      // setTimeout(() => {
      // }, 3000)
      clearAllStates()
      setSaveDisable(false)
      dispatch(setIsAddNewNotification(false))
    }
  }

  const innerComp = {
    header: <GlobalNotificationHeader state={state} />,
    0: (
      <GlobalNotificationStep1
        state={state}
        updateState={updateState}
        handleFileChange={handleFileChange}
        pageSelector={pageSelector}
        selectSingleAsset={selectThumbnail}
      />
    ),
    1: <PushNotificationSettings state={state} updateState={updateState} />,
    2: <GlobalNotificationStep2 state={state} />,
    footer: (
      <GlobalNotificationFooter
        state={state}
        updateState={updateState}
        checkStepDisable={checkStepDisable}
        saveDisable={saveDisable}
        clearAllStates={clearAllStates}
      />
    ),
  }

  return (
    <Box className='flex w-full flex-col justify-between items-center h-full px-4 relative'>
      {isAddNewNotification && (
        <>
          <Box
            className={`w-full pb-5 ${
              state.activeStep === 0 ? 'h-[70%]' : 'h-auto'
            } ${state.dataFetching && 'opacity-50'}`}
          >
            {innerComp['header']}

            {innerComp?.[state.activeStep as keyof typeof innerComp]}
          </Box>

          {innerComp['footer']}

          {state.dataFetching && <LoadingState />}

          <ModalConfirm
            children={
              <p className='text-base'>
                Once you create a notification, it can't be undone.
              </p>
            }
            handleClose={handleClose}
            onConfirm={handleOnConfirm}
            open={state.showConfirmBox}
          />
        </>
      )}

      {!isAddNewNotification && <GlobalNotificationHistoryTable />}
    </Box>
  )
}

export default GlobalNotifications
