import { HomeAppSDK } from '@contentful/app-sdk'
import React, { useEffect, useState } from 'react'
import { ENV_VARIABLES } from '../../../constant/variables'
import {
  CreateConfigurationEntry,
  UpdateConfigurationEntry,
} from '../../../globals/config-helpers'
import { getConfigurationByScopeAndType } from '../../../globals/utils'
import TemplateC<PERSON>Mapper from '../DtsCtaMapping/components/TemplateCtaMapper'

function DtsCtaMappingV2({ sdk }: { sdk: HomeAppSDK }) {
  const [dtsCtaByCatByDomain, setDtsCtaByCatByDomain] = useState<any>({})

  const [contentId, setContentId] = useState<string>('')

  const envId = ENV_VARIABLES.contentfulEnvironment

  const spaceId = ENV_VARIABLES.contentfulSpaceID

  /**
   * Updates the dtsCtaByCatByDomain state and saves the DTS CTA mapping configuration
   * @param {string} activeCat - The active category selected
   * @param {{ [key: string]: string }} data - The DTS CTA mapping data
   */
  const handleDtsCtaMapping = async (
    activeCat: string,
    data: { [key: string]: string }
  ) => {
    let oldData = { ...dtsCtaByCatByDomain }

    oldData = { ...oldData, [activeCat]: data }

    setDtsCtaByCatByDomain({ ...dtsCtaByCatByDomain, [activeCat]: data })
    await handleSaveDtsCtaMapping(oldData)
  }

/**
 * Saves or updates the DTS CTA mapping configuration.
 * If a configuration exists, it updates the existing configuration entry.
 * Otherwise, it creates a new configuration entry with the provided data 
 * and sets the content ID.
 * 
 * @param {any} dtsCtaData - The DTS CTA mapping data to be saved or updated.
 */

  const handleSaveDtsCtaMapping = async (dtsCtaData: any) => {
    if (contentId) {
      // if entry exists, then update the data else create a new crosspost configuration

      await UpdateConfigurationEntry({
        contentId,
        data: dtsCtaData,
        envId,
        spaceId,
      })
    } else {
      const res = await CreateConfigurationEntry({
        data: dtsCtaData,
        type: 'DTS CTA Mapping',
        scope: 'MSA',
        internalName: `Crosspost DTS CTA Mapping - MSA`,
        envId,
        spaceId,
      })

      setContentId(res)
    }
  }

  /**
   * Fetches the DTS CTA mapping configuration from the Contentful API.
   * If the configuration exists, it sets the contentId state to the configuration's id,
   * and sets the dtsCtaByCatByDomain state to the configuration's data.
   */
  const fetchDtsConfig = async () => {
    const res: any = await getConfigurationByScopeAndType(
      'DTS CTA Mapping',
      'MSA'
    )

    if (res) {
      setContentId(res?.id)
      setDtsCtaByCatByDomain(res?.data)
    }
  }

  useEffect(() => {
    fetchDtsConfig()
  }, [])

  return (
    <div className='flex w-full items-start justify-center flex-col gap-5 p-5'>
      <h4>Insights - DTS Widget</h4>
      <p className='text-base'>
        Please select the relevant CTA that will be used within the Insights DTS
        widget across the following websites.{' '}
      </p>
      <div className='grid grid-cols-2 w-full gap-5'>
        <TemplateCtaMapper
          sdk={sdk}
          onChange={(data) =>
            handleDtsCtaMapping('DynamicWidgetInsights', data)
          }
          ctaByDomainData={dtsCtaByCatByDomain?.['DynamicWidgetInsights'] || {}}
        />
      </div>
    </div>
  )
}

export default DtsCtaMappingV2
