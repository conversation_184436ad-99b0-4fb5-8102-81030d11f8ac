* {
  margin: 0;
  padding: 0;
}

.dashboardRoot {
  height: 100vh;
  width: 100vw;
  overflow-x: hidden;
}

.dashboardContainer {
  display: grid;
  grid-template-columns: 1fr 5fr;
  grid-gap: 20px;
  padding: 20px;
  height: 100vh;
}

.sidebar {
  background-color: white;
  border-radius: 5px;

  h6 {
    padding: 10px 20px;
    margin-bottom: 0;
    font-size: 16px;
    transition: background-color 0.3s ease;
    font-weight: 400;
    user-select: none;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
  }

  h6:hover,
  h6.active {
    background-color: #cfd9e080;
  }

  ul {
    list-style: none;
    padding: 0;
    cursor: pointer;
    user-select: none;
    overflow: hidden;
    transition: max-height 0.5s ease;
    max-height: 0;
  }

  ul li {
    list-style: none;
    padding: 10px 0 10px 36px;
    font-size: 16px;
    transition: background-color 0.3s ease;
  }

  ul li:hover,
  ul li.active {
    background-color: #cfd9e080;
  }

  .show {
    max-height: 1000px;
  }
}

.dropdownHeader {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content {
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.customSelect select {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding-left: 5px !important;
  padding-right: 30px;
}

.upcomingFeatureRoot {
  padding: 0px;
  // height: 60vh;
}

.dvTemplate {
  margin: 10px 20px;

  svg {
    right: 1.5rem;
  }
}