import {
  Box,
  Button,
  Flex,
  Form,
  FormControl,
  Modal,
  ModalControls,
  Spinner,
  Tooltip
} from '@contentful/f36-components'
import { notification } from 'antd'
import { Asset } from 'contentful-management'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import Download from '../../assets/icons/Download'
import { GlobalContext } from '../../contexts/globalContext'
import { publishEntry } from '../../globals/utils'
import {
  setDVAsset,
  setDVTemplate,
  setDVUpdatedData,
} from '../../redux/slices/dashboard/dvSlices'
import { AppDispatch, RootState } from '../../redux/store'
import NextButton from '../Buttons/NextButton'
import PrevButton from '../Buttons/PrevButton'
import RevertButton from '../Buttons/RevertButton'
import SaveButton from '../Buttons/SaveButton'
import ModalConfirm from '../ConfirmModal'
import { EntityProvider } from '../InputFields/Reference'
import ProgressBar from '../ProgressBar'
import FileUpload from './@Core/FileUpload'
import Preferences from './@Core/Preferences'
import Visualizer from './@Core/Visualizer'
import './index.scss'
import {
  ChartModalPropsI,
  getDataSourceUrlByTemplateName,
  getPatternByTemplateName,
  removeFieldFromEntry,
  templateI,
  templateName,
  templates,
  UpdateDVEntryData
} from './utils'

const DataVisualization = (props: ChartModalPropsI) => {
  const { sdk, entryId } = props
  const sdkFields = sdk?.entry?.fields
  const entry = sdk?.entry
  const internalName = sdkFields?.['internalName']?.getValue()
  const templateContainerRef = useRef<HTMLDivElement>(null)
  const { chartFileModal, currentLocale, setCharFileModal } =
    useContext(GlobalContext)
  const [activeStep, setActiveStep] = useState(0)
  const [isReverting, setIsReverting] = useState(false)
  const [dataLoading, setDataLoading] = useState<boolean>(false)
  const [currentTab, setCurrentTab] = useState('dv-general')
  const [confirmApplyModal, setConfirmApplyModal] = useState<boolean>(false)
  const [confirmCloseModal, setConfirmCloseModal] = useState<boolean>(false)
  const [confirmTemplateChangeModal, setConfirmTemplateChangeModal] =
    useState<boolean>(false)
  const [onConfirmTemplate, setOnConfirmTemplate] =
    useState<templateName>(undefined)
  const [isDvPublished, setDVPublished] = useState(false)
  const dispatch = useDispatch<AppDispatch>()

  const dvTemplate: any = useSelector(
    (state: RootState) => state?.dvDashboard?.dvTemplate
  )
  const dvChartId: any = useSelector(
    (state: RootState) => state?.dvDashboard?.dvChartId
  )
  const dvAsset: any = useSelector(
    (state: RootState) => state?.dvDashboard?.dvAsset
  )
  const dvUpdatedData: any = useSelector(
    (state: RootState) => state?.dvDashboard?.dvUpdatedData
  )
  const dvGlobalData: any = useSelector(
    (state: RootState) =>
      state?.dvDashboard?.globalConfigData?.DataVisualization?.[dvTemplate]
  )
  const referencUrl =
    dvGlobalData?.chart?.asset?.fields?.file?.[currentLocale]?.url

  useEffect(() => {
    if (dvTemplate) {
      dvAsset ? setActiveStep(2) : setActiveStep(1)
    }
  }, [])

  const handlePreferenceRevert = async () => {
    setIsReverting(true)
    if (currentTab === 'dv-axes') {
      const payLoad = {
        ...dvUpdatedData,
      }
      delete payLoad.axes
      dispatch(setDVUpdatedData(payLoad))
    } else if (currentTab === 'dv-general') {
      const payload = {
        ...dvUpdatedData,
      }
      delete payload.general
      dispatch(setDVUpdatedData(payload))
    } else if (currentTab === 'dv-styles') {
      const payload = {
        ...dvUpdatedData,
      }
      delete payload.styles
      dispatch(setDVUpdatedData(payload))
    } else if (currentTab === 'dv-grid') {
      const payload = {
        ...dvUpdatedData,
      }
      delete payload.grid
      dispatch(setDVUpdatedData(payload))
    } else {
      const payload = {
        ...dvUpdatedData,
      }
      delete payload.chart
      dispatch(setDVUpdatedData(payload))
    }

    setIsReverting(false)
  }

  const handleTemplateRevert = () => {
    dispatch(setDVTemplate(undefined))
    setActiveStep(0)
  }
  const handleFileRevert = async () => {
    await removeFieldFromEntry(entryId, 'source')
    dispatch(setDVAsset(undefined))
    setActiveStep(1)
  }

  //The sortedTemplates array is created by copying the templates array and sorting it so that the selected template always comes first.
  const sortedTemplates = [...templates].sort((a, b) => {
    if (a.name === dvTemplate) return -1
    if (b.name === dvTemplate) return 1
    return 0
  })

  //Handle Template save
  const handleTemplateSave = async (template: templateI) => {
    setDataLoading(true)
    const p1 = dvTemplate && getPatternByTemplateName(dvTemplate)
    const p2 = getPatternByTemplateName(template.name)

    if (p1 !== p2 && dvTemplate && dvAsset) {
      setOnConfirmTemplate(template.name)
      setConfirmTemplateChangeModal(true)
    } else {
      dvAsset ? setActiveStep(2) : setActiveStep(1)
      dispatch(setDVTemplate(template.name))
      // Scroll to top
      if (templateContainerRef.current) {
        const scrollContainer = document.querySelector('.templeFormControl')
        if (scrollContainer)
          scrollContainer.scrollTop = templateContainerRef.current.offsetTop
      }
    }

    setDataLoading(false)
  }

  //Handle new or existing asset choose
  const handleChatFileAction = async (assetData: Asset | undefined) => {
    setDataLoading(true)
    dispatch(setDVAsset(assetData))
    if (assetData) {
      setActiveStep(2)
    } else {
      await removeFieldFromEntry(entryId, 'source')
      setActiveStep(1)
    }
    setDataLoading(false)
  }

  //Handle chartFile preferences save
  const handleCahrtFileSave = async () => {
    setDataLoading(true)
    setActiveStep(3)
    setDataLoading(false)
  }

  //handle preferences
  const handlePreferenceApply = async (key: string, data: any) => {
    setDataLoading(true)
    const payLoad = {
      ...dvUpdatedData,
      [key]: {
        ...(dvUpdatedData?.[key] ?? {}),
        ...data,
      },
    }
    dispatch(setDVUpdatedData(payLoad))
    setDataLoading(false)
  }

  const handleOptionChange = async (template: templateName) => {
    setDataLoading(true)
    const p1 = dvTemplate && getPatternByTemplateName(dvTemplate)
    const p2 = getPatternByTemplateName(template)
    if (p1 !== p2) {
      setOnConfirmTemplate(template)
      setConfirmTemplateChangeModal(true)
    } else {
      dispatch(setDVTemplate(template))
    }
    setDataLoading(false)
  }

  const handleTemplateChangeModal = async () => {
    setDataLoading(true)
    dispatch(setDVTemplate(onConfirmTemplate))
    dispatch(setDVAsset(undefined))
    dispatch(setDVUpdatedData(undefined))
    const payload = {
      dvTemplate: onConfirmTemplate,
    }
    await UpdateDVEntryData(entryId, payload, currentLocale)
    setDataLoading(false)
    setConfirmTemplateChangeModal(false)
    setActiveStep(1)
  }

  //handle Apply
  const handleApply = async () => {
    setDataLoading(true)
    const payload = {
      dvTemplate,
      dvAsset,
      dvUpdatedData,
    }
    await UpdateDVEntryData(entryId, payload, currentLocale)
    setDataLoading(false)
    setConfirmApplyModal(false)
    setActiveStep(4)
  }
  const handleModalClose = async () => {
    setDataLoading(true)
    const payload = {
      dvTemplate,
      dvAsset,
      dvUpdatedData,
    }
    await UpdateDVEntryData(entryId, payload, currentLocale)
    setDataLoading(false)
    setCharFileModal(false)
    setConfirmCloseModal(false)
  }

  const handlePublish = async () => {
    setDataLoading(true)
    let ispublished = await publishEntry(props?.entryId)
    setDVPublished(ispublished)
    setDataLoading(false)
    openNotification()
  }

  const openNotification = () => {
    notification.success({
      key: 'dv-publish-success-notification',
      message: 'Data visualisation was successfully created.',
      placement: 'bottom',
      duration: 3, // Closes after 3 seconds
      onClose: () => { setCharFileModal(false) }, // Close modal when notification closes
      closeIcon: false,
      style: { position: 'absolute', left: '-150px', bottom: '100px', width: '500px', backgroundColor: '#b0dfd8', color: '#005446' }
    });
  };

  const handleClose = () => {
    notification.destroy('dv-publish-success-notification');
    setCharFileModal(false)
    setConfirmCloseModal(false)
  }
  const filename = dvAsset?.fields?.title?.[currentLocale]?.split(' ')
  const formattedFileName = dvAsset?.fields?.title?.[currentLocale]

  let title = (
    <p className='mdlTitle'>
      <strong>Data Visualisations</strong>
    </p>
  )
  if (activeStep === 0) {
    title = (
      <p className='mdlTitle'>
        <span>
          <strong>Data Visualisations: </strong>Please select a chart type for {internalName}
        </span>
        <span>Step {activeStep + 1} of 5</span>
      </p>
    )
  } else if (activeStep === 1) {
    title = (
      <p className='mdlTitle'>
        <span>
          <strong>Data Visualisations: </strong>Please add a data source for {internalName}.
        </span>
        <span> Step {activeStep + 1} of 5</span>
      </p>
    )
  } else if (activeStep === 2) {
    title = (
      <p className='mdlTitle'>
        <span>
          <strong>Data Visualisations: </strong>Preview generated from {internalName}.
        </span>
        <span> Step {activeStep + 1} of 5</span>
      </p>
    )
  } else if (activeStep === 3) {
    title = (
      <p className='mdlTitle'>
        <span>
          <strong>Data Visualisations: </strong>Customise {dvTemplate ?? ''} for {internalName}.
        </span>
        <span>Step {activeStep + 1} of 5</span>
      </p>
    )
  } else if (activeStep === 4) {
    title = (
      <p className='mdlTitle'>
        <span>
          <strong>Data Visualisations: </strong>Preview of{' '}
          {dvTemplate ?? ''} from {formattedFileName ?? ''}.
        </span>
        <span> Step {activeStep + 1} of 5</span>
      </p>
    )
  } 

  const revertTooltipText =
    activeStep === 3
      ? currentTab === 'dv-axes'
        ? dvUpdatedData?.axes
          ? 'Revert to default settings'
          : 'You have made no changes'
        : currentTab === 'dv-general'
          ? dvUpdatedData?.general
            ? 'Revert to default settings'
            : 'You have made no changes'
          : currentTab === 'dv-styles'
            ? dvUpdatedData?.styles
              ? 'Revert to default settings'
              : 'You have made no changes'
            : currentTab === 'dv-grid'
              ? dvUpdatedData?.grid
                ? 'Revert to default settings'
                : 'You have made no changes'
              : dvUpdatedData?.chart
                ? 'Revert to default settings'
                : 'You have made no changes'
      : activeStep === 1
        ? dvTemplate
          ? 'Undo'
          : 'You have made no changes'
        : activeStep === 2 && dvAsset
          ? 'Undo'
          : 'You have made no changes'

  const revertDisable = revertTooltipText === 'You have made no changes'

  const templateDiv = (
    <FormControl
      key={'step 0'}
      className='templeFormControl'
      style={{ width: `${dvTemplate ? '115px' : 'calc(100% - 15px)'}` }}
    >
      <div className='template-selection' ref={templateContainerRef}>
        {sortedTemplates.map((template) => (
          <div
            key={template.name}
            style={{ width: `${dvTemplate ? '100px' : '170px'}` }}
            className={`template-card ${template.name === dvTemplate && 'selected'
              }`}
            onClick={() => handleTemplateSave(template)}
          >
            <img src={template.imageUrl} alt={template.name} />
            <p>{template.name}</p>
          </div>
        ))}
      </div>
    </FormControl>
  )

  const dataSourceDiv = (
    <FormControl key={'step 1'} className='dataSourceFormControl'>
      <Flex
        gap='20px'
        style={{
          width: `${dvAsset ? '200px' : '100%'}`,
          paddingLeft: `${dvAsset ? '0' : '15px'}`,
          justifyContent: 'center'
        }}
      >
        <FileUpload
          sdk={sdk}
          asset={dvAsset}
          onAction={handleChatFileAction}
          setDataLoading={setDataLoading}
          dataLoading={dataLoading}
        />
        {!dvAsset && (
            <Tooltip
              placement='top'
              id='download-tool-tip'
              content={`Download the reference asset for ${dvTemplate ?? ''} `}
            >
              <Button
                as='a'
              variant='secondary'
                startIcon={<Download />}
                size='small'
                isDisabled={dataLoading}
                href={
                  referencUrl
                    ? `https://${referencUrl}`
                    : dvTemplate && getDataSourceUrlByTemplateName(dvTemplate)
                }
            >Download reference data source</Button>
          </Tooltip>
        )} 
      </Flex>
    </FormControl>
  )

  return (
    <EntityProvider sdk={sdk}>
      <Modal
        className='dvModalRoot'
        onClose={() => {
          setConfirmCloseModal(true)
        }}
        isShown={chartFileModal}
        shouldCloseOnOverlayClick={false}
        shouldCloseOnEscapePress={false}
        position='center'
      >
        {() => (
          <>
            <Modal.Header
              className='header'
              title=''
              onClose={() =>
                activeStep === 4 ? (isDvPublished ? handleClose(): setConfirmCloseModal(true)) : setConfirmCloseModal(true)
              }
            >
              <Box className={'headerBox'}>{title}</Box>
            </Modal.Header>
            <Box style={{ width: '100%' }}>
              <ProgressBar completedSteps={activeStep} totalSteps={4} />
            </Box>
            <Modal.Content className='dvModalContent'>
              <Form className='content-root'>
                {activeStep === 0 && templateDiv}

                {activeStep === 1 &&
                  (dataLoading ? (
                    <Spinner
                      customSize={30}
                      style={{ margin: '35vh 0 34vh 50%' }}
                    />
                  ) : (
                    <Flex>
                      {templateDiv}
                      {dvTemplate && (
                        <>
                          <div
                            className='vertical-line'
                            style={{ left: '130px' }}
                          ></div>
                          {dataSourceDiv}
                        </>
                      )}
                    </Flex>
                  ))}

                {activeStep === 2 &&
                  (dataLoading ? (
                    <Spinner
                      customSize={30}
                      style={{ margin: '35vh 0 34vh 50%' }}
                    />
                  ) : (
                    <Flex>
                      {templateDiv}
                      {dvTemplate && (
                        <>
                          <div
                            className='vertical-line'
                            style={{ left: '130px' }}
                          ></div>
                          {dataSourceDiv}
                          {dvAsset && (
                            <>
                              <div
                                className='vertical-line'
                                style={{ left: '360px' }}
                              ></div>
                              <div
                                className={`preview1 ${isReverting && 'blurred'
                                  }`}
                              >
                                <Visualizer
                                  template={dvTemplate}
                                  changedData={dvUpdatedData}
                                  id={dvChartId}
                                  asset={dvAsset}
                                />
                              </div>
                            </>
                          )}
                        </>
                      )}
                    </Flex>
                  ))}

                {activeStep === 3 && (
                  <Box
                    style={{
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'start',
                      flexDirection: 'column',
                      alignItems: 'start',
                    }}
                  >

                    <Box
                      style={{
                        display: 'flex',
                        justifyContent: 'start',
                        flexDirection: 'column',
                        alignItems: 'center',
                        width: '100%',
                        paddingLeft: '15px',
                        right: '15px',
                        top: '20px',
                        background: '#c6c6c6',
                        height: '380px'
                      }}
                    >
                      <Box className={`preview ${isReverting && 'blurred'}`}>
                        <Visualizer
                          template={dvTemplate}
                          changedData={dvUpdatedData}
                          id={dvChartId}
                          asset={dvAsset}
                          isFixedHeight
                        />
                      </Box>
                    </Box>
                    <Box
                      style={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'start',
                        flexDirection: 'column',
                        alignItems: 'start',
                      }}
                    >
                      <FormControl
                        key={'step 3'}
                        className='preferenceFormControl'
                      >
                        <div className={`${isReverting && 'blurred'}`}>
                          <Preferences
                            onDataChange={handlePreferenceApply}
                            changedData={dvUpdatedData}
                            asset={dvAsset}
                            setDataLoading={setDataLoading}
                            dataLoading={dataLoading}
                            currentTab={currentTab}
                            setCurrentTab={setCurrentTab}
                            template={dvTemplate}
                            isGlobal={false}
                          />
                        </div>
                      </FormControl>
                    </Box>
                  </Box>
                )}
                {activeStep === 4 && (
                  <Flex flexDirection='column' gap={'30px'} className='w-100'>
                    <Visualizer
                    template={dvTemplate}
                    changedData={dvUpdatedData}
                    id={dvChartId}
                    asset={dvAsset}
                      isFixedHeight
                    />
                  </Flex>
                )}

                <ModalConfirm
                  open={confirmCloseModal}
                  btn1Text='Resume'
                  btn2Text='End the world'
                  title='Hold up, wait a minute...'
                  loading={dataLoading}
                  onConfirm={handleModalClose}
                  children={
                    <>
                      <Box className='summery'>
                        <p>
                          All changes made will be saved as a draft and you can choose to resume later. Or the world might end.
                          <br />
                          <br />
                          Are you sure you want to exit?
                        </p>
                      </Box>
                    </>
                  }
                  handleClose={() => setConfirmCloseModal(false)}
                />

                <ModalConfirm
                  open={confirmTemplateChangeModal}
                  btn1Text='No'
                  btn2Text='Yes'
                  title='Hold up...'
                  loading={dataLoading}
                  onConfirm={handleTemplateChangeModal}
                  children={
                    <>
                      <Box className='summery'>
                        <p>
                          If you change the template to {onConfirmTemplate}, all
                          your current settings will be reset to the global
                          defaults, and you will need to start from the
                          beginning.
                          <br />
                          <br />
                          Do you want to proceed with this change?
                        </p>
                      </Box>
                    </>
                  }
                  handleClose={() => setConfirmTemplateChangeModal(false)}
                />
              </Form>

            </Modal.Content>
              <ModalControls className='modelControl'>
                <>
                  {activeStep === 0 ? (
                    <>
                      <NextButton
                        helpText={
                          dvTemplate ? 'Continue' : 'Please select template'
                        }
                        onClick={() => setActiveStep(1)}
                        btnText='Continue'
                        type='primary'
                        isDisabled={true}
                        isLoading={dataLoading}
                        tooltipPlacement='top'
                        isIcon={false}
                      />
                    </>
                  ) : activeStep === 1 ? (
                    <>
                        <PrevButton
                          btnText='Back'
                          type='secondary'
                        onClick={handleTemplateRevert}
                        isLoading={isReverting}
                        isDisabled={dataLoading || revertDisable}
                        helpText={`${revertTooltipText}`}
                        tooltipPlacement='top'
                          isIcon={false}
                      />
                      <NextButton
                          helpText={dvAsset ? 'Continue' : 'Please add Data Source'}
                        onClick={() => setActiveStep(2)}
                          btnText='Continue'
                        type='primary'
                        isDisabled={true}
                        isLoading={dataLoading}
                        tooltipPlacement='top'
                          isIcon={false}
                      />
                    </>
                  ) : activeStep === 2 ? (
                    <>
                          <PrevButton
                            btnText='Back'
                            type='secondary'
                        onClick={handleFileRevert}
                        isLoading={isReverting}
                        isDisabled={dataLoading || revertDisable}
                        helpText={`${revertTooltipText}`}
                        tooltipPlacement='top'
                            isIcon={false}
                      />
                      <NextButton
                            helpText={!isReverting ? 'Continue' : 'Reverting'}
                        onClick={handleCahrtFileSave}
                            btnText='Continue'
                        type='primary'
                        isDisabled={!isReverting ? false : true}
                        isLoading={dataLoading}
                        tooltipPlacement='top'
                            isIcon={false}
                      />{' '}
                    </>
                  ) : activeStep === 3 ? (
                    <>
                          {currentTab !== 'dv-mark' && <RevertButton
                        btnText=''
                        onClick={handlePreferenceRevert}
                        isLoading={isReverting}
                        isDisabled={dataLoading || revertDisable}
                        helpText={`${revertTooltipText}`}
                        tooltipPlacement='top'
                        style={{ position: 'absolute', left: '1rem' }}
                          />}
                      <PrevButton
                              btnText='Back'
                              type='secondary'
                        helpText={isReverting ? 'Reverting' : 'Amend/Update'}
                        onClick={() => setActiveStep(2)}
                        isDisabled={isReverting || dataLoading}
                        tooltipPlacement='top'
                              isIcon={false}
                      />
                      <NextButton
                              helpText={!isReverting ? 'Continue' : 'Reverting'}
                              onClick={handleApply}
                              btnText='Continue'
                        type='primary'
                        isDisabled={!isReverting ? false : true}
                        isLoading={dataLoading}
                        tooltipPlacement='top'
                              isIcon={false}
                      />
                    </>
                        ) : (
                    <>
                              {!isDvPublished && <><PrevButton
                                btnText='Back'
                                type='secondary'
                        helpText={isReverting ? 'Reverting' : 'Amend/Update'}
                                onClick={() => setActiveStep(3)}
                        isDisabled={isReverting}
                        tooltipPlacement='top'
                                isIcon={false}
                              />
                      <SaveButton
                                  btnText={'Publish'}
                                  onClick={handlePublish}
                        isLoading={dataLoading}
                        isDisabled={isReverting}
                        tooltipPlacement='top'
                                  helpText={'Publish'}
                                  isIcon={false}
                                /></>}
                            </>
                  )}
                </>
              </ModalControls>
          </>
        )}
      </Modal>
    </EntityProvider>
  )
}

export default DataVisualization
