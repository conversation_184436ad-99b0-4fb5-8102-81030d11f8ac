import * as echarts from 'echarts'
import React, { useContext, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import Download from '../../../../assets/icons/Download'
import Share from '../../../../assets/icons/Share'
import { GlobalContext } from '../../../../contexts/globalContext'
import { RootState } from '../../../../redux/store'
import { defaults, getData, getParsedData } from '../../utils'
import {
  getAreaChartProps,
  getBarChartProps,
  getBarRaceChartProps,
  getBarStackNormalizationChartProps,
  getBoxplotChartProps,
  getCandlestickChartProps,
  getComboChartProps,
  getDoughnutChartProps,
  getLineChartProps,
  getLineRaceChartProps,
  getPieChartProps,
  getScatterChartProps,
  getTreemapChartProps,
  getWaterfallChartProps
} from './mapper'
const Visualizer = (props: any) => {
  const { currentLocale } = useContext(GlobalContext)
  const [data, setData] = useState<any>({})
  const [isDataLoaded, setIsDataLoaded] = useState(false)
  const dvGlobalData: any = useSelector(
    (state: RootState) => state.dvDashboard.globalConfigData
  )
  const id = props?.id
  const template = props?.template

  useEffect(() => {
    const fetch = async () => {
      const preferences =
        dvGlobalData?.DataVisualization?.[template] ?? defaults?.[template]
      const assetData = props?.asset
      let fileUrl = assetData?.fields?.file?.[currentLocale].url
      const fileData = fileUrl ? await getParsedData(fileUrl) : []
  
      setData((pre) => {
        return {
          ...pre,
          fileData: fileData,
          preferences: {
            axes: { ...preferences?.axes, ...props?.changedData?.axes },
            general: {
              ...preferences?.general,
              ...props?.changedData?.general,
            },
            styles: { ...preferences?.styles, ...props?.changedData?.styles },
            grid: { ...preferences?.grid, ...props?.changedData?.grid },
            chart: {
              ...preferences?.chart,
              ...props?.changedData?.chart,
              asset: assetData,
              colors: {
                ...preferences?.chart?.colors,
                ...(props?.changedData?.chart?.colors ?? {}),
              },
              chartType: {
                ...preferences?.chart?.chartType,
                ...(props?.changedData?.chart?.chartType ?? {}),
              },
              'x-axis': {
                ...preferences?.chart?.['x-axis'],
                ...(props?.changedData?.chart?.['x-axis'] ?? {}),
              },
              'y-axis': {
                ...preferences?.chart?.['y-axis'],
                ...(props?.changedData?.chart?.['y-axis'] ?? {}),
              },
              activeLegends: props?.changedData?.chart?.activeLegends,
              lineStyle: {
                ...preferences?.chart?.lineStyle,
                ...(props?.changedData?.chart?.lineStyle ?? {}),
              },
            },
            mark: { ...preferences?.mark, ...props?.changedData?.mark },
          },
        }
      })

      setIsDataLoaded(true)
    }

    fetch()
  }, [dvGlobalData, props])

  const stylePreference = data?.preferences?.styles
  const generalPreference = data?.preferences?.general

  const height = stylePreference?.chartHeight || '400px'

  useEffect(() => {
    if (!isDataLoaded) return // Wait until data is loaded

    // Create a new ECharts instance
    const myChart = echarts.init(document.getElementById(id)!)

    let options = null
    switch (template) {
      case 'Line chart':
        options = getLineChartProps(data)
        break
      case 'Bar chart':
        options = getBarChartProps(data)
        break
      case 'Area chart':
        options = getAreaChartProps(data)
        break
      case 'Combo chart':
        options = getComboChartProps(data)
        break
      case 'Pie chart':
        options = getPieChartProps(data)
        break
      case 'Doughnut chart':
        options = getDoughnutChartProps(data)
        break
      case 'Waterfall chart':
        options = getWaterfallChartProps(data)
        break
      case 'Candlestick chart':
        options = getCandlestickChartProps(data)
        break
      case 'Treemap chart':
        options = getTreemapChartProps(data)
        break
      case 'Scatter chart':
        options = getScatterChartProps(data)
        break
      case 'Boxplot chart':
        options = getBoxplotChartProps(data)
        break
      case 'LineRace chart':
        options = getLineRaceChartProps(data)
        break
      case 'BarRace chart':
        options = getBarRaceChartProps(data, 0)
        break
      case 'BarStackNormalization chart':
        options = getBarStackNormalizationChartProps(data)
        break
      default:
        options = getLineChartProps(data)
        break
    }
    // Define the chart option based on props or defaults
    const option = options
    if (template === 'BarRace chart') {
      const { categoriesData } = getData(data?.fileData);
      const timeArray = Object.values(categoriesData).find((value) => Array.isArray(value)) || [];
      const duration = Number(data?.preferences?.general?.duration * 1000) || 3000;

      myChart.setOption(option)
      
      let currentIndex = 1;
      const updateChart = () => {
        const updatedOptions = getBarRaceChartProps(data, currentIndex);
        myChart.setOption(updatedOptions,{ notMerge: true });// Update without merging other options // Loop over time frames
        currentIndex = (currentIndex + 1) % timeArray.length;
      };

      setTimeout(() => {
        updateChart()
      }, 0)
      // Set interval for animation
      const interval = setInterval(() => {
        updateChart()
      }, duration)
      // Stop the interval after a specified duration,if isLoop s disabled
      const timeout = setTimeout(() => {
        if (!data?.preferences?.general?.isLoop) {
          clearInterval(interval);
        }
      }, duration); 
      return () => {
        clearInterval(interval);
        clearTimeout(timeout);
      };
    } else {
      myChart.setOption(option)
    }

    // Handle resize
    const handleResize = () => myChart.resize();
    window.addEventListener('resize', handleResize);

    // Clean up ECharts instance and event listener on unmount
    return () => {
      myChart.dispose()
      window.removeEventListener('resize', handleResize);
    }
  }, [isDataLoaded, data])

  const shareContainer = (
    <div className='shareContainer'>
      <p>Sharing options are only available on the front end.</p>
    </div>
  )
  const downloadContainer = (
    <div className='downloadContainer'>
      <p>Download options are only available on the front end.</p>
    </div>
  )

  // Returning the input text and it should be enclosed with SimpleText.
  return (
    <div
      className='flex flex-col gap-3 p-4'
      style={{
        background: stylePreference?.graphBgColor,
        borderWidth: `${stylePreference?.borderWidth}px`,
        borderColor: stylePreference?.borderColor,
        borderStyle: stylePreference?.borderType,
        boxShadow: `${stylePreference?.isBorderShadow ? 'rgba(0, 0, 0, 0.05) 0px 0px 0px 1px' : 'none'}`,
        height: `${props?.isFixedHeight ? '100%' : ''}`
      }}
    >
      <div className='flex justify-center items-start relative'>
        <div className='flex justify-end items-start text-center'>

          <div className=''>
            {generalPreference?.title && <h5>{generalPreference?.title}</h5>}
            {generalPreference?.subtitle && <p className=''>{generalPreference?.subtitle}</p>}
          </div>

        </div>
        <div className='flex absolute right-0' >
          {generalPreference?.isDownloadable && (
            <div className='px-4 cursor-pointer hoverContainer'>
              <Download />
              {downloadContainer}
            </div>
          )}
          <div className='pl-4 flex gap-3 cursor-pointer hoverContainer pb-4'>
            <div>
              <Share />
            </div>{' '}
            <span style={{ fontSize: '18px' }}>Share</span>
            {shareContainer}
          </div>
        </div>
      </div>
      <div style={{ width: '100%', height: `${props?.isFixedHeight ? '260px' : height + 'px'}` }} id={id}></div>
      <div className='text-right'>
        <p className='"text-xs"'>{generalPreference?.sourceText}</p>
      </div>
    </div>
  )
}

export default Visualizer
