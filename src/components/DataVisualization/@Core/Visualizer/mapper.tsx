import { calculateBoxplotStatistics, getData, getGradientColor, getLeafNodes, getPieChartSeriesData, getTooltipBarRaceChart, getTooltipBoxplot, getTooltipCandleStick, getTooltipLineChart, getTooltipPieChart, getTooltipScatter, getTooltipTreemap, getTooltipWaterfall, parseValue, parseValueWithDataUnit, tools } from "../../utils";


const getAxes = (axes: any, grid: any, categories: any, seriesData: any, template?: string) => {

  const x = axes?.['x-axis']?.map((axis: any, index: number) => {
    const symbols = template === 'Candlestick chart' ? seriesData[index]?.[0]?.[0] : template === 'Scatter chart' ? seriesData?.[index]?.[0]?.[0] : seriesData[index]?.[0]

    const isVisible = axes?.['x-axis']?.length > 1
    // Formatting min, max, and interval before passing to axisLabel
    const min = template === 'BarStackNormalization chart' ? 0 : axis?.min ? Number(axis.min) : undefined;
    const max = template === 'BarStackNormalization chart' ? 1 : axis?.max ? Number(axis.max) : undefined;
    const interval = axis?.interval ? Number(axis.interval) : undefined;
    return {
      show: axes?.showAxes && axis?.show,
      name: axis?.title,
      nameLocation: axis?.nameLocation || 'center',
      nameRotate: axis?.nameRotate ? 90 : 0,
      nameGap: axis?.nameGap || 30,
      type: axis?.type || 'category',
      ...(axis?.type === 'category' && {
        data: template === 'BarRace chart' ? categories : categories?.[`x-axis-${index + 1}`],
      }),
      ...(isVisible && {
        ...(axis?.axisPosition && { position: axis?.axisPosition }),
        ...(axis?.alignTicks && { alignTicks: axis?.alignTicks }),
        ...(axis?.offset && { offset: Number(axis?.offset) }),
      }),
      ...(axis?.type === 'value' && {
        min: min,
        max: max,
        interval: interval
      }),
      splitLine: {
        show: grid?.showGrid
          ? grid?.gridFormat !== 'Horizontal'
            ? true
            : false
          : false,
        lineStyle: {
          color: grid?.showGrid && grid?.gridLineColor,
          type: grid?.showGrid && grid?.gridLineStyle,
        },
      },
      axisLine: {
        show: axis?.showAxisLine,
        lineStyle: {
          color: axis?.xAxisLineColor || '#333',
        },
      },
      inverse: axis?.reverseAxis ? true : false,
      axisLabel: {
        rotate: axis?.lableRotate ? 45 : 0,
        formatter: function (value) {
          if (axis?.type === 'value') {
            const params = {
              prefix: symbols?.prefix,
              value: template === 'BarStackNormalization chart' ? (Math.round(value * 1000) / 10) : value,
              suffix: axis?.dataUnit ?? symbols?.suffix,
              decimalPlaces: symbols?.decimalPlaces
            }
            return template === 'BarStackNormalization chart' ? `${params?.prefix}${params?.value}%` : tools.getAxesLabelFormatValue(params)
          }
          return value;  // Default return value for smaller numbers or other axis types
        },

        // width: '100',
        overflow: 'truncate',
        ellipsis: '...',
        fontSize: '12',
        fontFamily: 'Aeonik Regular, sans-serif',
      },
      nameTextStyle: {
        fontFamily: 'Arial, sans-serif',
        /*  color:axis?.xAxisFonts?.fontColor,
      fontFamily:axis?.xAxisFonts?.fontFamily,
      fontSize:axis?.xAxisFonts?.fontSize ,
      lineHeight: axis?.xAxisFonts?.lineHeight , */
      },
      ...(template === 'Scatter chart' && { scale: true }),
      ...((template === 'BarRace chart' && axis?.type === 'category') && {
        animationDuration: 300,
        animationDurationUpdate: 300,
        max: categories.length - 1,
      }),
      ...((template === 'BarRace chart' && axis?.type === 'value') && {
        max: 'dataMax'
      })
    }
  })

  const y = axes?.['y-axis']?.map((axis: any, index: number) => {
    const symbols = template === 'Candlestick chart' ? seriesData[index]?.[0]?.[0] : template === 'Scatter chart' ? seriesData?.[index]?.[0]?.[1] : seriesData[index]?.[0]
    const isVisible = axes?.['y-axis']?.length > 1
    const min = template === 'BarStackNormalization chart' ? 0 : axis?.min ? Number(axis.min) : undefined;
    const max = template === 'BarStackNormalization chart' ? 1 : axis?.max ? Number(axis.max) : undefined;
    const interval = axis?.interval ? Number(axis.interval) : undefined;
    return {
      show: axes?.showAxes && axis?.show,
      name: axis?.title,
      nameLocation: axis?.nameLocation || 'end',
      nameRotate: axis?.nameRotate ? 90 : 0,
      nameGap: axis?.nameGap || 30,
      type: axis?.type || 'value',
      ...(axis?.type === 'category' && {
        data: template === 'BarRace chart' ? categories : categories?.[`y-axis-${index + 1}`],
      }),
      ...(isVisible && {
        ...(axis?.axisPosition && { position: axis?.axisPosition }),
        ...(axis?.alignTicks && { alignTicks: axis?.alignTicks }),
        ...(axis?.offset && { offset: Number(axis?.offset) }),
      }),
      ...(axis?.type === 'value' && {
        min: min,
        max: max,
        interval: interval
      }),
      splitLine: {
        show: grid?.showGrid
          ? grid?.gridFormat !== 'Vertical'
            ? true
            : false
          : false,
        lineStyle: {
          color: grid?.showGrid && grid?.gridLineColor,
          type: grid?.showGrid && grid?.gridLineStyle,
        },
      },
      axisLine: {
        show: axis?.showAxisLine,
        lineStyle: {
          color: axis?.yAxisLineColor || '#333',
        },
      },
      inverse: axis?.reverseAxis ? true : false,
      axisLabel: {
        rotate: axis?.lableRotate ? 45 : 0,
        formatter: function (value) {
          if (axis?.type === 'value') {
            const params = {
              prefix: symbols?.prefix,
              value: template === 'BarStackNormalization chart' ? (Math.round(value * 1000) / 10) : value,
              suffix: axis?.dataUnit ?? symbols?.suffix,
              decimalPlaces: symbols?.decimalPlaces
            }
            return template === 'BarStackNormalization chart' ? `${params?.prefix}${params?.value}%` : tools.getAxesLabelFormatValue(params)
          }
          return value;  // Default return value for smaller numbers or other axis types
        },
        //width: '50',
        overflow: 'truncate',
        ellipsis: '...',
        fontSize: '12',
        fontFamily: 'Aeonik Regular, sans-serif',
      },
      nameTextStyle: {
        fontFamily: 'Aeonik Regular, sans-serif',
        /*  color:axis?.yAxisFonts?.fontColor,
      fontFamily:axis?.yAxisFonts?.fontFamily,
      fontSize:axis?.yAxisFonts?.fontSize ,
      lineHeight: axis?.yAxisFonts?.lineHeight , */
      },
      ...(template === 'Scatter chart' && { scale: true }),
      ...((template === 'BarRace chart' && axis?.type === 'category') && {
        animationDuration: 300,
        animationDurationUpdate: 300,
        max: categories.length - 1,
      }),
      ...((template === 'BarRace chart' && axis?.type === 'value') && {
        max: 'dataMax'
      })
    }
  })

  return { x, y }
}

const generateMarkLine = (markPreference: any, header: string, valueTypeAxis?: { xAxis: boolean, yAxis: boolean }) => {
  // Create a map of mark lines grouped by series name
  const markLineMap = markPreference?.markLineData?.reduce((acc, markLine) => {
    const seriesName = markLine.series
    if (!acc[seriesName]) {
      acc[seriesName] = []
    }
    acc[seriesName].push(markLine)
    return acc
  }, {})

  return {
    label: {
      show: true,
      formatter: '{b}',
      padding: 5, // Padding: [top, right, bottom, left]
      borderRadius: 2, // Optional: Rounding the label box
      borderColor: '#ccc', // Optional: Label border color
      borderWidth: 1, // Optional: Label border width
      fontFamily: 'Aeonik Regular, sans-serif',
    },

    symbol: ['circle', 'circle'],
    animation: true,
    animationDuration: 1000,
    data:
      markLineMap?.[header]?.map((data) => {
        const axisLine = data?.axis === 'x-axis' ? 'xAxis' : 'yAxis'
        const labelDistance = data?.axis === 'x-axis' ? -35 : -80

        const axisPoint = (valueTypeAxis?.[axisLine] && parseValue(data?.axisPont)) ? parseValue(data?.axisPont)?.value : data?.axisPont
        return {
          name: data?.MarkLineName ?? '',
          [`${axisLine}`]: axisPoint ?? '',
          label: {
            distance: data?.axis === 'x-axis' ? -35 : 10,
            ...(data?.axis === 'y-axis' && { position: 'insideEnd' }),
            color: /* data?.markLineFonts?.fontColor || */ '#fff',
            /*  fontFamily:data?.markLineFonts?.fontFamily,
        fontSize:data?.markLineFonts?.fontSize ,
        lineHeight: data?.markLineFonts?.lineHeight , */
            backgroundColor: /* data?.markLineFonts?.bgColor || */ '#e2222b',
          },
          lineStyle: {
            color: data?.markLineFonts?.bgColor || '#e2222b',
            width: 1,
            type: 'dashed',
          },
        }
      }) || [],
  }
}
const getLegends = (legendPreferences: any, dimensions: any) => {
  // Initialize selected with all legends set to true
  let selected = {}
  dimensions.forEach((legendName) => {
    selected[legendName] = true // Default all legends to true
  })

  // Set legends not in activeLegends to false
  dimensions.forEach((legendName) => {
    if (
      legendPreferences?.activeLegends &&
      !legendPreferences?.activeLegends?.includes(legendName)
    ) {
      selected[legendName] = false // Mark those not in activeLegends as false
    }
  })

  const legend = {
    show: legendPreferences?.showLegend,
    data: legendPreferences.activeLegends ?? dimensions,
    //selected: selected,
    type: 'scroll',
    width: '100%',
    icon: 'circle',
    itemWidth: 10,
    orient: legendPreferences?.legendOrientation ?? 'horizontal',
    ...(legendPreferences?.legendOrientation === 'horizontal'
      ? legendPreferences?.legendPosition === 'bottom-center'
        ? {
            bottom: '10',
            left: 'center',
          }
        : {
            top: '10',
            left: 'center',
          }
      : legendPreferences?.legendPosition === 'bottom-right'
      ? {
          bottom: '20',
          left: 'right',
          align: 'left',
        }
      : {
          top: '20',
          left: 'right',
          align: 'left',
        }),

    textStyle: {
      width:
        legendPreferences?.legendOrientation === 'horizontal' ? 'auto' : '150',
      overflow: 'break',
      fontFamily: 'Aeonik Regular, sans-serif',
      fontSize: 12,
      /*  color:legendPreferences?.legendFonts?.fontColor,
      fontFamily:legendPreferences?.legendFonts?.fontFamily,
      fontSize:legendPreferences?.legendFonts?.fontSize ,
      lineHeight: legendPreferences?.legendFonts?.lineHeight , */
    },
  }
  return legend
}
const getTitle = (generalPreference: any) => {
  const title = {
    show: false /*  generalPreference?.showTitle, disabling this , moving title out of Echart */,
    text: generalPreference?.title,
    subtext: generalPreference?.subtitle,
    ...(generalPreference?.titlePosition === 'bottom' && {
      bottom: '0%',
    }),
    ...(generalPreference?.titlePosition === 'top' && {
      top: 'top',
    }),
    left: generalPreference?.titleAlignment || 'left',
    textStyle: {
      color: generalPreference?.titleFonts?.fontColor,
      fontFamily: generalPreference?.titleFonts?.fontFamily,
      fontSize: generalPreference?.titleFonts?.fontSize,
      lineHeight: generalPreference?.titleFonts?.lineHeight,
    },
    subtextStyle: {
      color: generalPreference?.subTitleFonts?.fontColor,
      fontFamily: generalPreference?.subTitleFonts?.fontFamily,
      fontSize: generalPreference?.subTitleFonts?.fontSize,
      lineHeight: generalPreference?.subTitleFonts?.lineHeight,
    },
  }
  return title
}
const getGrid = (gridPreferences: any, generalPreference: any) => {
  const grid = {
    show: gridPreferences?.showGrid,
    backgroundColor: gridPreferences?.gridBgColor,
    borderColor: gridPreferences?.gridLineColor,
    left: gridPreferences?.gridLeft || 30,
    top: gridPreferences?.gridTop || 30,
    containLabel: true,
    right: gridPreferences?.gridRight || 30,
    bottom: generalPreference?.isZoomable
      ? `${parseInt(gridPreferences?.gridBottom || 30) + 40}`
      : gridPreferences?.gridBottom || 30,
  }
  return grid
}
const getDataZoom = (gridPreferences: any, generalPreference: any) => {
  const dataZoom = [
    {
      show: generalPreference?.isZoomable,
      type: 'slider',
      start: 0,
      end: 100,
      bottom: gridPreferences?.gridBottom || 30,
      height: 10,
      fillerColor: '#efefef',
    },
    {
      type: 'inside',
      start: 0,
      end: 100,
      disabled: !generalPreference?.isZoomable,
    },
  ]
  return dataZoom
}

export const getLineChartProps = (props: any) => {
  const generalPreference = props?.preferences?.general
  const gridPreferences = props?.preferences?.grid
  const stylePreference = props?.preferences?.styles
  const axesPreference = props?.preferences?.axes
  const legendPreferences = props?.preferences?.chart
  const colors = legendPreferences?.colors
  const seriesX = legendPreferences?.['x-axis']
  const seriesY = legendPreferences?.['y-axis']
  const lineStyle = legendPreferences?.lineStyle
  const markPreference = props?.preferences?.mark
  const data = props.fileData
  const { categoriesData, dimensions, seriesData } = getData(data)

  const series = dimensions?.map((header, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0
    
    const xValueType = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value'
    const yValueType = axesPreference?.['y-axis']?.[seriesYIndex]?.type === 'value'

    const AxisType = { 'xAxis': axesPreference?.reverseAxisType ? yValueType : xValueType, 'yAxis': axesPreference?.reverseAxisType ? xValueType : yValueType }

    const dataUnit = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value' ? axesPreference?.['x-axis']?.[seriesXIndex]?.dataUnit : axesPreference?.['y-axis']?.[seriesYIndex]?.dataUnit
    // Generate a stack key based on the combination of xAxisIndex and yAxisIndex
    const stackKey = `${seriesXIndex}-${seriesYIndex}`

    return {
      name: header,
      data: seriesData[i]?.map(d => {
        return dataUnit ? parseValueWithDataUnit(d?.value, d?.prefix, `${dataUnit}`) : {
          value: d?.value,
          prefix: d?.prefix,
          suffix: d?.suffix,
          decimalPlaces: d?.decimalPlaces
        }
      }),
      //color: colors?.[`dimension${i + 1}`]
      color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
        ? getGradientColor(colors?.[`dimension${i + 1}`])
        : colors?.[`dimension${i + 1}`],
      type: 'line',
      xAxisIndex: seriesXIndex,
      yAxisIndex: seriesYIndex,
      emphasis: {
        focus: 'series',
      },
      showSymbol: false, // Disable dots on the line
      smooth: legendPreferences?.isSmooth,
      symbol: 'circle',
      symbolSize: 10,
      ...(generalPreference?.format === 'stacked' ? { stack: stackKey } : {}),
      markLine: generateMarkLine(markPreference, header, AxisType),
      lineStyle: {
        type: lineStyle?.[`dimension${i + 1}`] || 'solid',
      },
      endLabel: {
        show: legendPreferences?.isEndLable,
        width: 150,
        color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
          ? getGradientColor(colors?.[`dimension${i + 1}`])
          : colors?.[`dimension${i + 1}`],
        overflow: "break",
        fontSize: '12',
        fontFamily: 'Aeonik Regular, sans-serif',
        formatter: function (params) {
          return params?.seriesName + ': ' + tools.getTooltipFormatValue(params?.data);
        },
      },
      labelLayout: {
        moveOverlap: 'shiftY',
      },
      animation: true,
      animationDuration: 1000
    }
  })

  const { x, y } = getAxes(axesPreference, gridPreferences, categoriesData, seriesData)
  const sTWithBP =
    generalPreference?.subtitle && generalPreference?.titlePosition === 'bottom' //sunTitle and position bottom
  const options = {
    grid: getGrid(gridPreferences, generalPreference),
    title: getTitle(generalPreference),
    legend: getLegends(legendPreferences, dimensions),
    dataZoom: getDataZoom(gridPreferences, generalPreference),
    xAxis: axesPreference?.reverseAxisType ? y : x,
    yAxis: axesPreference?.reverseAxisType ? x : y,
    series: series,
    tooltip: getTooltipLineChart(
      gridPreferences,
      generalPreference,
      legendPreferences
    ),
  }
  return options
}
export const getBarChartProps = (props: any) => {
  const generalPreference = props?.preferences?.general
  const gridPreferences = props?.preferences?.grid
  const stylePreference = props?.preferences?.styles
  const axesPreference = props?.preferences?.axes
  const legendPreferences = props?.preferences?.chart
  const colors = legendPreferences?.colors
  const seriesX = legendPreferences?.['x-axis']
  const seriesY = legendPreferences?.['y-axis']
  const markPreference = props?.preferences?.mark
  const data = props.fileData

  const { categoriesData, dimensions, seriesData } = getData(data)

  const series = dimensions?.map((header, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0

    const xValueType = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value'
    const yValueType = axesPreference?.['y-axis']?.[seriesYIndex]?.type === 'value'

    const AxisType = { 'xAxis': axesPreference?.reverseAxisType ? yValueType : xValueType, 'yAxis': axesPreference?.reverseAxisType ? xValueType : yValueType }

    const dataUnit = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value' ? axesPreference?.['x-axis']?.[seriesXIndex]?.dataUnit : axesPreference?.['y-axis']?.[seriesYIndex]?.dataUnit

    // Generate a stack key based on the combination of xAxisIndex and yAxisIndex
    const stackKey = `${seriesXIndex}-${seriesYIndex}`

    return {
      name: header,
      data: seriesData[i].map(d => {
        return dataUnit ? parseValueWithDataUnit(d?.value, d?.prefix, `${dataUnit}`) : {
          value: d?.value,
          prefix: d?.prefix,
          suffix: d?.suffix,
          decimalPlaces: d?.decimalPlaces
        }
      }),
      //color: colors?.[`dimension${i + 1}`],
      color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
        ? getGradientColor(colors?.[`dimension${i + 1}`])
        : colors?.[`dimension${i + 1}`],
      type: 'bar',
      xAxisIndex: seriesXIndex,
      yAxisIndex: seriesYIndex,
      emphasis: {
        focus: 'series',
      },
      showSymbol: false, // Disable dots on the line
      smooth: true,
      ...(generalPreference?.format === 'stacked' ? { stack: stackKey } : {}),
      markLine: generateMarkLine(markPreference, header, AxisType),
      label: {
        show: generalPreference?.showDataLable,
        position: generalPreference?.dataLablePlacement,
        fontSize: '12',
        fontFamily: 'Aeonik Regular, sans-serif',
        formatter: function (params) {
          return tools.getTooltipFormatValue(params.data);
        },
      }
    }
  })
  const { x, y } = getAxes(axesPreference, gridPreferences, categoriesData, seriesData)
  const sTWithBP =
    generalPreference?.subtitle && generalPreference?.titlePosition === 'bottom' //sunTitle and position bottom
  const options = {
    grid: getGrid(gridPreferences, generalPreference),
    title: getTitle(generalPreference),
    legend: getLegends(legendPreferences, dimensions),
    dataZoom: getDataZoom(gridPreferences, generalPreference),
    xAxis: axesPreference?.reverseAxisType ? y : x,
    yAxis: axesPreference?.reverseAxisType ? x : y,
    series: series,
    tooltip: getTooltipLineChart(
      gridPreferences,
      generalPreference,
      legendPreferences
    ),
  }
  return options
}

export const getAreaChartProps = (props: any) => {
  const generalPreference = props?.preferences?.general
  const gridPreferences = props?.preferences?.grid
  const stylePreference = props?.preferences?.styles
  const axesPreference = props?.preferences?.axes
  const legendPreferences = props?.preferences?.chart
  const colors = legendPreferences?.colors
  const seriesX = legendPreferences?.['x-axis']
  const seriesY = legendPreferences?.['y-axis']
  const markPreference = props?.preferences?.mark
  const data = props.fileData

  const { categoriesData, dimensions, seriesData } = getData(data)
  const series = dimensions?.map((header, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0

    const xValueType = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value'
    const yValueType = axesPreference?.['y-axis']?.[seriesYIndex]?.type === 'value'

    const AxisType = { 'xAxis': axesPreference?.reverseAxisType ? yValueType : xValueType, 'yAxis': axesPreference?.reverseAxisType ? xValueType : yValueType }

    const dataUnit = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value' ? axesPreference?.['x-axis']?.[seriesXIndex]?.dataUnit : axesPreference?.['y-axis']?.[seriesYIndex]?.dataUnit

    // Generate a stack key based on the combination of xAxisIndex and yAxisIndex
    const stackKey = `${seriesXIndex}-${seriesYIndex}`

    return {
      name: header,
      data: seriesData[i].map(d => {
        return dataUnit ? parseValueWithDataUnit(d?.value, d?.prefix, `${dataUnit}`) : {
          value: d?.value,
          prefix: d?.prefix,
          suffix: d?.suffix,
          decimalPlaces: d?.decimalPlaces
        }
      }),
      color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
        ? getGradientColor(colors?.[`dimension${i + 1}`])
        : colors?.[`dimension${i + 1}`], 
      type: 'line',
      xAxisIndex: seriesXIndex,
      yAxisIndex: seriesYIndex,
      areaStyle: {
        color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
          ? getGradientColor(colors?.[`dimension${i + 1}`])
          : colors?.[`dimension${i + 1}`],
        opacity: 1
      },
      emphasis: {
        focus: 'series',
      },
      showSymbol: false, // Disable dots on the line
      smooth: true,
      ...(generalPreference?.format === 'stacked' ? { stack: stackKey } : {}),
      markLine: generateMarkLine(markPreference, header, AxisType),
    }
  })

  const { x, y } = getAxes(axesPreference, gridPreferences, categoriesData, seriesData)
  const sTWithBP =
    generalPreference?.subtitle && generalPreference?.titlePosition === 'bottom' //sunTitle and position bottom
  const options = {
    grid: getGrid(gridPreferences, generalPreference),
    title: getTitle(generalPreference),
    legend: getLegends(legendPreferences, dimensions),
    dataZoom: getDataZoom(gridPreferences, generalPreference),
    xAxis: axesPreference?.reverseAxisType ? y : x,
    yAxis: axesPreference?.reverseAxisType ? x : y,
    series: series,
    tooltip: getTooltipLineChart(
      gridPreferences,
      generalPreference,
      legendPreferences
    ),
  }
  return options
}

export const getComboChartProps = (props: any) => {
  const generalPreference = props?.preferences?.general
  const gridPreferences = props?.preferences?.grid
  const stylePreference = props?.preferences?.styles
  const axesPreference = props?.preferences?.axes
  const legendPreferences = props?.preferences?.chart
  const colors = legendPreferences?.colors
  const seriesX = legendPreferences?.['x-axis']
  const seriesY = legendPreferences?.['y-axis']
  const type = legendPreferences?.chartType
  const markPreference = props?.preferences?.mark
  const data = props.fileData

  const { categoriesData, dimensions, seriesData } = getData(data)
  const series = dimensions?.map((header, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0
    const xValueType = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value'
    const yValueType = axesPreference?.['y-axis']?.[seriesYIndex]?.type === 'value'

    const AxisType = { 'xAxis': axesPreference?.reverseAxisType ? yValueType : xValueType, 'yAxis': axesPreference?.reverseAxisType ? xValueType : yValueType }
    const dataUnit = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value' ? axesPreference?.['x-axis']?.[seriesXIndex]?.dataUnit : axesPreference?.['y-axis']?.[seriesYIndex]?.dataUnit

    // Generate a stack key based on the combination of xAxisIndex and yAxisIndex
    const stackKey = `${seriesXIndex}-${seriesYIndex}`

    return {
      name: header,
      data: seriesData[i].map(d => {
        return dataUnit ? parseValueWithDataUnit(d?.value, d?.prefix, `${dataUnit}`) : {
          value: d?.value,
          prefix: d?.prefix,
          suffix: d?.suffix,
          decimalPlaces: d?.decimalPlaces
        }
      }),
      color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
        ? getGradientColor(colors?.[`dimension${i + 1}`])
        : colors?.[`dimension${i + 1}`],
      type: type?.[`dimension${i + 1}`] ?? 'bar',
      xAxisIndex: seriesXIndex,
      yAxisIndex: seriesYIndex,
      emphasis: {
        focus: 'series',
      },
      showSymbol: false, // Disable dots on the line
      smooth: legendPreferences?.isSmooth,
      endLabel: {
        show: legendPreferences?.isEndLable,
        width: 150,
        color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
          ? getGradientColor(colors?.[`dimension${i + 1}`])
          : colors?.[`dimension${i + 1}`],
        overflow: "break",
        fontSize: '12',
        fontFamily: 'Aeonik Regular, sans-serif',
        formatter: function (params) {
          return params.seriesName + ': ' + tools.getTooltipFormatValue(params.data);
        },
      },
      labelLayout: {
        moveOverlap: 'shiftY'
      },
      label: {
        show: generalPreference?.showDataLable,
        position: generalPreference?.dataLablePlacement,
        fontSize: '12',
        fontFamily: 'Aeonik Regular, sans-serif',
        formatter: function (params) {
          return tools.getTooltipFormatValue(params.data);
        },
      },
      ////stack:stackKey,
      markLine: generateMarkLine(markPreference, header, AxisType),
    }
  })

  const { x, y } = getAxes(axesPreference, gridPreferences, categoriesData, seriesData)
  const options = {
    grid: getGrid(gridPreferences, generalPreference),
    title: getTitle(generalPreference),
    legend: getLegends(legendPreferences, dimensions),
    dataZoom: getDataZoom(gridPreferences, generalPreference),
    xAxis: axesPreference?.reverseAxisType ? y : x,
    yAxis: axesPreference?.reverseAxisType ? x : y,
    series: series,
    tooltip: getTooltipLineChart(
      gridPreferences,
      generalPreference,
      legendPreferences
    ),
  }
  return options
}

export const getPieChartProps = (props: any) => {
  const generalPreference = props?.preferences?.general
  const legendPreferences = props?.preferences?.chart
  const data = props.fileData
  const { seriesData, dimensions } = getPieChartSeriesData(data, props?.preferences, 'Pie-Chart')
  let options = {
    title: getTitle(generalPreference),
    legend: getLegends(legendPreferences, dimensions),
    series: seriesData,
    tooltip: getTooltipPieChart(generalPreference),
  }

  return options
}

export const getDoughnutChartProps = (props: any) => {
  const legendPreferences = props?.preferences?.chart
  const generalPreference = props?.preferences?.general
  const data = props.fileData
  const { seriesData, dimensions } = getPieChartSeriesData(data, props?.preferences, 'Doughnut chart')
  let options = {
    title: getTitle(generalPreference),
    legend: getLegends(legendPreferences, dimensions),
    series: seriesData,
    tooltip: getTooltipPieChart(generalPreference),
  }

  return options
}

export const getWaterfallChartProps = (props: any) => {
  const generalPreference = props?.preferences?.general
  const gridPreferences = props?.preferences?.grid
  const axesPreference = props?.preferences?.axes
  const legendPreferences = props?.preferences?.chart
  const colors = legendPreferences?.colors
  const seriesX = legendPreferences?.['x-axis']
  const seriesY = legendPreferences?.['y-axis']
  const markPreference = props?.preferences?.mark
  const data = props?.fileData

  const { categoriesData, dimensions, seriesData } = getData(data)
  const newdimenssions = dimensions.filter((series) => series !== 'Placeholder')

  const series = dimensions?.map((header, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0

    const xValueType = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value'
    const yValueType = axesPreference?.['y-axis']?.[seriesYIndex]?.type === 'value'

    const AxisType = { 'xAxis': axesPreference?.reverseAxisType ? yValueType : xValueType, 'yAxis': axesPreference?.reverseAxisType ? xValueType : yValueType }

    const dataUnit = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value' ? axesPreference?.['x-axis']?.[seriesXIndex]?.dataUnit : axesPreference?.['y-axis']?.[seriesYIndex]?.dataUnit

    // Generate a stack key based on the combination of xAxisIndex and yAxisIndex
    const stackKey = `${seriesXIndex}-${seriesYIndex}`

    if (i === 0) {
      return {
        name: 'Placeholder',
        type: 'bar',
        itemStyle: {
          borderColor: 'transparent',
          color: 'transparent',
        },
        emphasis: {
          itemStyle: {
            borderColor: 'transparent',
            color: 'transparent',
          },
        },
        data: seriesData[i].map(d => {
          return dataUnit ? parseValueWithDataUnit(d?.value, d?.prefix, `${dataUnit}`) : {
            value: d?.value,
            prefix: d?.prefix,
            suffix: d?.suffix,
            decimalPlaces: d?.decimalPlaces
          }
        }),
        label: {
          show: false,
        },
        xAxisIndex: seriesXIndex,
        yAxisIndex: seriesYIndex,
        stack: 'stackKey',
      }
    } else {
      return {
        name: header,
        type: 'bar',
        data: seriesData[i].map(d => {
          return dataUnit ? parseValueWithDataUnit(d?.value, d?.prefix, `${dataUnit}`) : {
            value: d?.value,
            prefix: d?.prefix,
            suffix: d?.suffix,
            decimalPlaces: d?.decimalPlaces
          }
        }),
        label: {
          show: generalPreference?.showDataLable,
          position: generalPreference?.dataLablePlacement,
          fontSize: '12',
          fontFamily: 'Aeonik Regular, sans-serif',
          formatter: function (params) {
            return tools.getTooltipFormatValue(params.data);
          },
        },
        itemStyle: {
          color: colors?.[`dimension${i}`]?.startsWith('linear-gradient')
            ? getGradientColor(colors?.[`dimension${i}`])
            : colors?.[`dimension${i}`],
        },
        xAxisIndex: seriesXIndex,
        yAxisIndex: seriesYIndex,
        emphasis: {
          focus: 'series',
        },
        stack: 'stackKey',
        markLine: generateMarkLine(markPreference, header, AxisType),
      }
    }
  })

  const { x, y } = getAxes(axesPreference, gridPreferences, categoriesData, seriesData)

  const options = {
    grid: getGrid(gridPreferences, generalPreference),
    title: getTitle(generalPreference),
    legend: getLegends(legendPreferences, newdimenssions),
    dataZoom: getDataZoom(gridPreferences, generalPreference),
    xAxis: axesPreference?.reverseAxisType ? y : x,
    yAxis: axesPreference?.reverseAxisType ? x : y,
    series: series,
    tooltip: getTooltipWaterfall(
      gridPreferences,
      generalPreference
    ),
  }
  return options
}

export const getCandlestickChartProps = (props: any) => {
  const generalPreference = props?.preferences?.general
  const gridPreferences = props?.preferences?.grid
  const stylePreference = props?.preferences?.styles
  const axesPreference = props?.preferences?.axes
  const legendPreferences = props?.preferences?.chart
  const colors = legendPreferences?.colors
  const seriesX = legendPreferences?.['x-axis']
  const seriesY = legendPreferences?.['y-axis']
  const markPreference = props?.preferences?.mark
  const data = props?.fileData
  const { categoriesData, dimensions, seriesData } = getData(data, 'Candlestick chart')

  const convertedData = seriesData?.map((series, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0

    const dataUnit = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value' ? axesPreference?.['x-axis']?.[seriesXIndex]?.dataUnit : axesPreference?.['y-axis']?.[seriesYIndex]?.dataUnit

    return series?.map((data) => {
      return data?.trim()?.split('/')?.map((d) => {
          const data = parseValue(d)
          return dataUnit ? parseValueWithDataUnit(data?.value, data?.prefix, `${dataUnit}`) : data
        }
        )
    })
  }) 
  const convertedSeriesData = convertedData?.map((series) => {
    return series?.map((data) => {
      return data?.map((item) => item?.value) // Extract only the `value` property
    }
    )
  }
  );
  const series = dimensions?.map((header, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0
    const xValueType = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value'
    const yValueType = axesPreference?.['y-axis']?.[seriesYIndex]?.type === 'value'

    const AxisType = { 'xAxis': axesPreference?.reverseAxisType ? yValueType : xValueType, 'yAxis': axesPreference?.reverseAxisType ? xValueType : yValueType }
    // Generate a stack key based on the combination of xAxisIndex and yAxisIndex
    const stackKey = `${seriesXIndex}-${seriesYIndex}`
    return {
      name: header,
      data: convertedSeriesData[i],
      type: 'candlestick',
      xAxisIndex: seriesXIndex,
      yAxisIndex: seriesYIndex,
      emphasis: {
        focus: 'series',
      },
      itemStyle: {
        /*  color: colors?.[`dimension${2 * i + 1}`], // Color of rising candles
        color0: colors?.[`dimension${2 * i + 2}`], // Color of falling candles
        borderColor: colors?.[`dimension${2 * i + 1}`],
        borderColor0: colors?.[`dimension${2 * i + 2}`], */
        color: colors?.[`dimension${2 * i + 1}`]?.startsWith('linear-gradient')
          ? getGradientColor(colors?.[`dimension${2 * i + 1}`])
          : colors?.[`dimension${2 * i + 1}`],
        color0: colors?.[`dimension${2 * i + 2}`]?.startsWith('linear-gradient')
          ? getGradientColor(colors?.[`dimension${2 * i + 2}`])
          : colors?.[`dimension${2 * i + 2}`],
        borderColor: colors?.[`dimension${2 * i + 1}`]?.startsWith(
          'linear-gradient'
        )
          ? getGradientColor(colors?.[`dimension${2 * i + 1}`])
          : colors?.[`dimension${2 * i + 1}`],
        borderColor0: colors?.[`dimension${2 * i + 2}`]?.startsWith(
          'linear-gradient'
        )
          ? getGradientColor(colors?.[`dimension${2 * i + 2}`])
          : colors?.[`dimension${2 * i + 2}`],
      },
      showSymbol: false, // Disable dots on the line
      smooth: true,
      //stack:stackKey,
      markLine: generateMarkLine(markPreference, header, AxisType),
    }
  })

  const { x, y } = getAxes(axesPreference, gridPreferences, categoriesData, convertedData, 'Candlestick chart')

  const options = {
    grid: getGrid(gridPreferences, generalPreference),
    title: getTitle(generalPreference),
    legend: getLegends(legendPreferences, dimensions),
    dataZoom: getDataZoom(gridPreferences, generalPreference),
    xAxis: axesPreference?.reverseAxisType ? y : x,
    yAxis: axesPreference?.reverseAxisType ? x : y,
    series: series,
    tooltip: getTooltipCandleStick(
      gridPreferences,
      generalPreference,
      convertedData
    ),
  }
  return options
}

export const getTreemapChartProps = (props: any) => {
  const generalPreference = props?.preferences?.general
  const gridPreferences = props?.preferences?.grid
  const stylePreference = props?.preferences?.styles
  const legendPreferences = props?.preferences?.chart
  const colors = legendPreferences?.colors
  const fileData = props?.fileData

  const leafNodes = getLeafNodes(fileData)
  // Step 1: Get all the field names (headers) from the first row
  const headers = [...fileData?.[0]];

  // Step 2: Map through the remaining rows and create a dynamic object for each row
  const data = fileData?.slice(1)?.map((row) => {
    const rowData = {};

    // Dynamically assign each field from the headers to the corresponding row value
    headers.forEach((header, index) => {
      if (header) {
        rowData[header?.toLowerCase()] = row?.[index];
      }  
    });

    return rowData;  // Return the constructed row object
  });


  //Function to dynamically build the tree structure
  function buildHierarchy(data) {
    const map = {};

    // Initialize each item and store references by name
    data.forEach((item, i) => {
      if (item) {
        const parsedItem = parseValue(item?.value);
        const ParseDataByUnit = generalPreference?.dataUnit
          ? parseValueWithDataUnit(parsedItem?.value, parsedItem?.prefix, generalPreference?.dataUnit)
          : parsedItem;

        // Update the item directly in the `item` array
        item.value = ParseDataByUnit?.value;
        item.suffix = ParseDataByUnit?.suffix;
        item.prefix = ParseDataByUnit?.prefix;
        item.decimalPlaces = ParseDataByUnit?.decimalPlaces

        map[item?.name] = {
          ...item,
          name: item?.name,
          children: [],
          itemStyle: leafNodes?.includes(item?.name) ? {
            color: colors[`dimension${item?.name}`] || colors[`dimension${leafNodes.indexOf(item?.name) + 1}`]
          } : {},
        };
      }
    });

    const tree = [];

    // Construct the hierarchy
    data?.forEach((item, j) => {
      if (item?.parent === '-') {
        tree?.push(map?.[item?.name]);
      } else {
        map[item?.parent]?.children?.push(map[item?.name]);
      }
    });

    return tree;
  }


  const hierarchicalData = buildHierarchy(data);

  const legendData = hierarchicalData?.map((node) => node?.name)
  const legend = {
    ...getLegends(legendPreferences, legendData),
    selectedMode: 'single',
  }
  const seriesData = hierarchicalData?.map((node, i) => {
    return { 
      name: node?.name,
      type: 'treemap',
      left: gridPreferences?.gridLeft || 30,
      top: gridPreferences?.gridTop || 30,
      right: gridPreferences?.gridRight || 30,
      bottom: gridPreferences?.gridBottom || 30,
      roam: generalPreference?.isZoomable,
      data: [hierarchicalData[i]],
      breadcrumb: {
        show: true,
      },
      label: {
        show: true,
        formatter: '{b}', // Show node name
      },
      leafDepth: 9
    }
  }
  )

  const options = {
    title: getTitle(generalPreference),
    legend: legend,
    series: seriesData,
    tooltip: getTooltipTreemap(generalPreference),
  }
  return options
}
export const getScatterChartProps = (props: any) => {
  const generalPreference = props?.preferences?.general
  const gridPreferences = props?.preferences?.grid
  const stylePreference = props?.preferences?.styles
  const axesPreference = props?.preferences?.axes
  const legendPreferences = props?.preferences?.chart
  const colors = legendPreferences?.colors
  const seriesX = legendPreferences?.['x-axis']
  const seriesY = legendPreferences?.['y-axis']
  const markPreference = props?.preferences?.mark
  const data = props?.fileData
  const { categoriesData, dimensions, seriesData } = getData(data, 'Candlestick chart')

  const convertedData = seriesData?.map((series, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0
    const dataUnit = [axesPreference?.['x-axis']?.[seriesXIndex]?.dataUnit, axesPreference?.['y-axis']?.[seriesYIndex]?.dataUnit]

    return series?.map((data) => {
      return data?.trim()?.split('/')?.map((d, i) => {
          const data = parseValue(d)
          return dataUnit?.[i] ? parseValueWithDataUnit(data?.value, data?.prefix, `${dataUnit?.[i]}`) : data
        })
    })
  })
  const convertedSeriesData = convertedData?.map((series) => {
    return series?.map((data) => {
      return data?.map((item) => item?.value) // Extract only the `value` property
    }
    )
  }
  );
  const series = dimensions?.map((header, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0

    const xValueType = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value'
    const yValueType = axesPreference?.['y-axis']?.[seriesYIndex]?.type === 'value'

    const AxisType = { 'xAxis': axesPreference?.reverseAxisType ? yValueType : xValueType, 'yAxis': axesPreference?.reverseAxisType ? xValueType : yValueType }

    // Generate a stack key based on the combination of xAxisIndex and yAxisIndex
    const stackKey = `${seriesXIndex}-${seriesYIndex}`
    return {
      name: header,
      data: convertedSeriesData[i],
      type: 'scatter',
      xAxisIndex: seriesXIndex,
      yAxisIndex: seriesYIndex,
      emphasis: {
        focus: 'series',
      },
      symbolSize: function (val) {
        return val?.[2] >=0 ? (val?.[2]) * 2 : 10;
      },
      color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
        ? getGradientColor(colors?.[`dimension${i + 1}`])
        : colors?.[`dimension${i + 1}`],
      showSymbol: false, // Disable dots on the line
      smooth: true,
      //stack:stackKey,
      markLine: generateMarkLine(markPreference, header, AxisType),
    }
  })

  const { x, y } = getAxes(axesPreference, gridPreferences, categoriesData, convertedData, 'Scatter chart')

  const options = {
    grid: getGrid(gridPreferences, generalPreference),
    title: getTitle(generalPreference),
    legend: getLegends(legendPreferences, dimensions),
    dataZoom: getDataZoom(gridPreferences, generalPreference),
    xAxis: axesPreference?.reverseAxisType ? y : x,
    yAxis: axesPreference?.reverseAxisType ? x : y,
    series: series,
    tooltip: getTooltipScatter(generalPreference, convertedData),
  }
  return options
}

export const getBoxplotChartProps = (props: any) => {
  const generalPreference = props?.preferences?.general
  const gridPreferences = props?.preferences?.grid
  const stylePreference = props?.preferences?.styles
  const axesPreference = props?.preferences?.axes
  const legendPreferences = props?.preferences?.chart
  const colors = legendPreferences?.colors
  const seriesX = legendPreferences?.['x-axis']
  const seriesY = legendPreferences?.['y-axis']
  const markPreference = props?.preferences?.mark
  const data = props?.fileData
  const { categoriesData, dimensions, seriesData } = getData(data, 'Candlestick chart')

  const convertedData = seriesData?.map((series, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0

    const dataUnit = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value' ? axesPreference?.['x-axis']?.[seriesXIndex]?.dataUnit : axesPreference?.['y-axis']?.[seriesYIndex]?.dataUnit

    return series?.map((data) => {
      return data?.trim()?.split(',')?.map((d) => {
          const data = parseValue(d)
          return dataUnit ? parseValueWithDataUnit(data?.value, data?.prefix, `${dataUnit}`) : data
        })
    })
  })

  const convertedSeriesData = convertedData?.map((series) => {
    return series?.map((data) => {
      return data?.map((item) => item?.value) // Extract only the `value` property
    }
    )
  }
  );

  const series = dimensions?.map((header, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0

    const xValueType = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value'
    const yValueType = axesPreference?.['y-axis']?.[seriesYIndex]?.type === 'value'

    const AxisType = { 'xAxis': axesPreference?.reverseAxisType ? yValueType : xValueType, 'yAxis': axesPreference?.reverseAxisType ? xValueType : yValueType }
    // Generate a stack key based on the combination of xAxisIndex and yAxisIndex
    const stackKey = `${seriesXIndex}-${seriesYIndex}`
    return {
      name: header,
      type: 'boxplot',
      data: calculateBoxplotStatistics(convertedSeriesData[i]),
      xAxisIndex: seriesXIndex,
      yAxisIndex: seriesYIndex,
      emphasis: {
        focus: 'series',
      },
      color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
        ? getGradientColor(colors?.[`dimension${i + 1}`])
        : colors?.[`dimension${i + 1}`],
      itemStyle: {
        color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
          ? getGradientColor(colors?.[`dimension${i + 1}`])
          : colors?.[`dimension${i + 1}`]
      },
      //stack:stackKey,
      markLine: generateMarkLine(markPreference, header, AxisType),
    }
  })

  const { x, y } = getAxes(axesPreference, gridPreferences, categoriesData, convertedData, 'Candlestick chart')
  const options = {
    grid: getGrid(gridPreferences, generalPreference),
    title: getTitle(generalPreference),
    legend: getLegends(legendPreferences, dimensions),
    dataZoom: getDataZoom(gridPreferences, generalPreference),
    xAxis: axesPreference?.reverseAxisType ? y : x,
    yAxis: axesPreference?.reverseAxisType ? x : y,
    series: series,
    tooltip: getTooltipBoxplot(generalPreference, convertedData),
  }
  return options
}

export const getLineRaceChartProps = (props: any) => {
  const generalPreference = props?.preferences?.general
  const gridPreferences = props?.preferences?.grid
  const stylePreference = props?.preferences?.styles
  const axesPreference = props?.preferences?.axes
  const legendPreferences = props?.preferences?.chart
  const colors = legendPreferences?.colors
  const seriesX = legendPreferences?.['x-axis']
  const seriesY = legendPreferences?.['y-axis']
  const markPreference = props?.preferences?.mark
  const data = props.fileData
  const { categoriesData, dimensions, seriesData } = getData(data)
  const series = dimensions?.map((header, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0
    const xValueType = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value'
    const yValueType = axesPreference?.['y-axis']?.[seriesYIndex]?.type === 'value'

    const AxisType = { 'xAxis': axesPreference?.reverseAxisType ? yValueType : xValueType, 'yAxis': axesPreference?.reverseAxisType ? xValueType : yValueType }
    const dataUnit = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value' ? axesPreference?.['x-axis']?.[seriesXIndex]?.dataUnit : axesPreference?.['y-axis']?.[seriesYIndex]?.dataUnit
    // Generate a stack key based on the combination of xAxisIndex and yAxisIndex
    const stackKey = `${seriesXIndex}-${seriesYIndex}`

    return {
      name: header,
      data: seriesData[i]?.map(d => {
        return dataUnit ? parseValueWithDataUnit(d?.value, d?.prefix, `${dataUnit}`) : {
          value: d?.value,
          prefix: d?.prefix,
          suffix: d?.suffix,
          decimalPlaces: d?.decimalPlaces

        }
      }),
      color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
        ? getGradientColor(colors?.[`dimension${i + 1}`])
        : colors?.[`dimension${i + 1}`],
      type: 'line',
      xAxisIndex: seriesXIndex,
      yAxisIndex: seriesYIndex,
      emphasis: {
        focus: 'series',
      },
      showSymbol: false, // Disable dots on the line
      smooth: legendPreferences?.isSmooth,
      symbol: 'circle',
      symbolSize: 10,
      ...(generalPreference?.format === 'stacked' ? { stack: stackKey } : {}),
      markLine: generateMarkLine(markPreference, header, AxisType),
      lineStyle: {
        type: legendPreferences?.lineStyle?.[`dimension${i + 1}`] || 'solid',
      },
      endLabel: {
        show: legendPreferences?.isEndLable,
        width: 150,
        color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
          ? getGradientColor(colors?.[`dimension${i + 1}`])
          : colors?.[`dimension${i + 1}`],
        overflow: "break",
        fontSize: '12',
        fontFamily: 'Aeonik Regular, sans-serif',
        formatter: function (params) {
          return params.seriesName + ': ' + tools.getTooltipFormatValue(params.data);
        },
      },
      labelLayout: {
        moveOverlap: 'shiftY'
      },
    }
  })

  const { x, y } = getAxes(axesPreference, gridPreferences, categoriesData, seriesData)
  const sTWithBP =
    generalPreference?.subtitle && generalPreference?.titlePosition === 'bottom' //sunTitle and position bottom
  const options = {
    grid: getGrid(gridPreferences, generalPreference),
    title: getTitle(generalPreference),
    legend: getLegends(legendPreferences, dimensions),
    dataZoom: getDataZoom(gridPreferences, generalPreference),
    xAxis: axesPreference?.reverseAxisType ? y : x,
    yAxis: axesPreference?.reverseAxisType ? x : y,
    series: series,
    tooltip: getTooltipLineChart(
      gridPreferences,
      generalPreference,
      legendPreferences
    ),
    animationDuration: Number(generalPreference?.duration * 1000)
  }
  return options
}
export const getBarRaceChartProps = (props: any, currentIndex) => {
  const generalPreference = props?.preferences?.general
  const gridPreferences = props?.preferences?.grid
  const stylePreference = props?.preferences?.styles
  const axesPreference = props?.preferences?.axes
  const legendPreferences = props?.preferences?.chart
  const colors = legendPreferences?.colors
  const seriesX = legendPreferences?.['x-axis']
  const seriesY = legendPreferences?.['y-axis']
  const markPreference = props?.preferences?.mark
  const data = props.fileData

  const { categoriesData, dimensions, seriesData } = getData(data)
  const currentValues = seriesData.map((currentData) => currentData?.[currentIndex]);

  const seriesXIndex =
    seriesX?.[`dimension${currentIndex + 1}`] <= axesPreference?.['x-axisCount']
      ? Number(seriesX[`dimension${currentIndex + 1}`]) - 1
      : 0
  const seriesYIndex =
    seriesY?.[`dimension${currentIndex + 1}`] <= axesPreference?.['y-axisCount']
      ? Number(seriesY[`dimension${currentIndex + 1}`]) - 1
      : 0

  const dataUnit = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value' ? axesPreference?.['x-axis']?.[seriesXIndex]?.dataUnit : axesPreference?.['y-axis']?.[seriesYIndex]?.dataUnit

  const { x, y } = getAxes(axesPreference, gridPreferences, dimensions, seriesData, 'BarRace chart')
  const timeArray = Object.values(categoriesData)?.find(value => Array.isArray(value));

  const options = {
    grid: getGrid(gridPreferences, generalPreference),
    title: getTitle(generalPreference),
    //legend: getLegends(legendPreferences,  categoriesData['x-axis-1']),
    dataZoom: getDataZoom(gridPreferences, generalPreference),
    xAxis: axesPreference?.reverseAxisType ? y : x,
    yAxis: axesPreference?.reverseAxisType ? x : y,
    series: [
      {
        realtimeSort: true,
        type: 'bar',
        data: currentValues?.map(d => {
          return dataUnit ? parseValueWithDataUnit(d?.value, d?.prefix, `${dataUnit}`) : {
            value: d?.value,
            prefix: d?.prefix,
            suffix: d?.suffix,
            decimalPlaces: d?.decimalPlaces
          }
        }),
        itemStyle: {
          color: (params) => {
            return colors?.[`dimension${params.dataIndex + 1}`]?.startsWith('linear-gradient')
              ? getGradientColor(colors?.[`dimension${params.dataIndex + 1}`])
              : colors?.[`dimension${params.dataIndex + 1}`];
          },
        },
        xAxisIndex: 0,
        yAxisIndex: 0,
        label: {
          show: generalPreference?.showDataLable,
          position: generalPreference?.dataLablePlacement,
          fontSize: '12',
          fontFamily: 'Aeonik Regular, sans-serif',
          valueAnimation: true,
          formatter: function (params) {
            return tools.getTooltipFormatValue(params?.data);
          },
        },

      },
    ],
    tooltip: getTooltipBarRaceChart(
      generalPreference,
      timeArray?.[currentIndex]
    ),
    animationDuration: 0,
    animationDurationUpdate: Number(generalPreference?.duration * 1000),
    animationEasing: "linear",
    animationEasingUpdate: "linear",
    graphic: {
      elements: [
        {
          type: 'text',
          right: `${parseInt(gridPreferences?.gridRight || 30) + 20}`,
          bottom: generalPreference?.isZoomable
            ? `${parseInt(gridPreferences?.gridBottom || 30) + 60}`
            : `${parseInt(gridPreferences?.gridBottom || 30) + 20}`,
          style: {
            text: timeArray?.[currentIndex],
            font: 'bolder 40px Aeonik Regular, sans-serif',
            fill: 'rgba(100, 100, 100, 0.25)'
          },
          z: 100
        }
      ]
    }
  }
  return options

}
export const getBarStackNormalizationChartProps = (props: any) => {
  const generalPreference = props?.preferences?.general
  const gridPreferences = props?.preferences?.grid
  const stylePreference = props?.preferences?.styles
  const axesPreference = props?.preferences?.axes
  const legendPreferences = props?.preferences?.chart
  const colors = legendPreferences?.colors
  const seriesX = legendPreferences?.['x-axis']
  const seriesY = legendPreferences?.['y-axis']
  const markPreference = props?.preferences?.mark
  const data = props.fileData

  const { categoriesData, dimensions, seriesData } = getData(data)

  const totalData = seriesData?.[0]?.map((_, i) =>
    seriesData?.reduce((sum, series) => sum + series?.[i]?.value, 0)
  );

  const series = dimensions?.map((header, i) => {
    const seriesXIndex =
      seriesX?.[`dimension${i + 1}`] <= axesPreference?.['x-axisCount']
        ? Number(seriesX[`dimension${i + 1}`]) - 1
        : 0
    const seriesYIndex =
      seriesY?.[`dimension${i + 1}`] <= axesPreference?.['y-axisCount']
        ? Number(seriesY[`dimension${i + 1}`]) - 1
        : 0
    const xValueType = axesPreference?.['x-axis']?.[seriesXIndex]?.type === 'value'
    const yValueType = axesPreference?.['y-axis']?.[seriesYIndex]?.type === 'value'

    const AxisType = { 'xAxis': axesPreference?.reverseAxisType ? yValueType : xValueType, 'yAxis': axesPreference?.reverseAxisType ? xValueType : yValueType }

    // Generate a stack key based on the combination of xAxisIndex and yAxisIndex
    const stackKey = `${seriesXIndex}-${seriesYIndex}`

    return {
      name: header,
      data: seriesData?.[i].map((d, did) => {
        return {
          value: totalData?.[did] <= 0 ? 0 : (d?.value / totalData?.[did]),
          prefix: d?.prefix,
          suffix: d?.suffix
        }
      }),
      color: colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
        ? getGradientColor(colors?.[`dimension${i + 1}`])
        : colors?.[`dimension${i + 1}`],
      type: 'bar',
      xAxisIndex: seriesXIndex,
      yAxisIndex: seriesYIndex,
      emphasis: {
        focus: 'series',
      },
      stack: 'stack',
      markLine: generateMarkLine(markPreference, header, AxisType),
      label: {
        show: generalPreference?.showDataLable,
        position: generalPreference?.dataLablePlacement,
        fontSize: '12',
        fontFamily: 'Aeonik Regular, sans-serif',
        formatter: function (params) {
          const data = {
            ...params.data,
            value: Math.round(params?.data?.value * 1000) / 10,
            suffix: '%'
          }
          return `${data?.prefix}${data?.value}${data?.suffix}`;
        },
      }
    }
  })
  const { x, y } = getAxes(axesPreference, gridPreferences, categoriesData, seriesData, 'BarStackNormalization chart')

  const options = {
    grid: getGrid(gridPreferences, generalPreference),
    title: getTitle(generalPreference),
    legend: { ...getLegends(legendPreferences, dimensions), selectedMode: false },
    dataZoom: getDataZoom(gridPreferences, generalPreference),
    xAxis: axesPreference?.reverseAxisType ? y : x,
    yAxis: axesPreference?.reverseAxisType ? x : y,
    series: series,
    tooltip: getTooltipLineChart(
      gridPreferences,
      generalPreference,
      legendPreferences,
      'BarStackNormalization chart'
    ),
  }
  return options
}