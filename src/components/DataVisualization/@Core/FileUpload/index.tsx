import { EditorAppSDK } from '@contentful/app-sdk'
import { Tooltip } from '@contentful/f36-components'
import { PlusIcon } from '@contentful/f36-icons'
import { Asset } from 'contentful-management'
import React, { useContext, useEffect, useState } from 'react'
import { GlobalContext } from '../../../../contexts/globalContext'
import CustomButton from '../../../Buttons/CustomButton'
import { createOrUpdateAsset } from '../../utils'
import CustomAsset from './CustomAsset'
import './index.scss'

interface FileUploadPropsI {
  sdk: EditorAppSDK
  asset: Asset | undefined
  onAction: (asset: Asset | undefined) => void
  setDataLoading: React.Dispatch<React.SetStateAction<boolean>>
  dataLoading?: boolean
  usedFor?: string
}

const FileUpload = (props: FileUploadPropsI) => {
  const { sdk, asset, onAction, setDataLoading, dataLoading, usedFor = 'DV' } = props
  const { currentLocale } = useContext(GlobalContext)

  const [selectedAsset, setSelectedAsset] = useState(asset)
  useEffect(() => {
    setSelectedAsset(asset)
  }, [asset])

  const handleFileChange = async (event) => {
    setDataLoading(true)
    const file = event.target.files[0]
    if (!file) {
      setDataLoading(false)
      return
    }

    const assetData = await createOrUpdateAsset(file, currentLocale, usedFor)
    setSelectedAsset(assetData)
    onAction(assetData)
    setDataLoading(false)
  }

  const openAssetSelector = async () => {
    setDataLoading(true)
    if (sdk) {
      const assetData = await sdk.dialogs.selectSingleAsset()
      if (assetData) {
        setSelectedAsset(assetData as Asset)
        onAction(assetData as Asset)
      }
      setDataLoading(false)
    }
  }

  const handleFileEditOrDel = async (assetData: Asset | undefined) => {
    setDataLoading(true)
    setSelectedAsset(assetData)
    onAction(assetData)
    setDataLoading(false)
  }

  return (
    <>
      {selectedAsset ? (
        <CustomAsset
          asset={selectedAsset}
          currentLocale={currentLocale}
          sdk={sdk}
          onFileUpdate={handleFileEditOrDel}
        />
      ) : (
          <div className={'dv-inputDiv'}>
            <Tooltip content='Add new Data Source' placement='top' isDisabled={dataLoading}>
              <div
                className={`dv-newFile  ${dataLoading ? 'css-dscz04' : 'css-cjqc4f'}`}
              >
                <input
                  type='file'
                  id='dv-fileInput'
                  className='dv-input'
                  onChange={handleFileChange}
                />
                <label
                  htmlFor='dv-fileInput'
                  id='dv-fileInputLabel'
                  className={`css-40g50j dv-lable ${dataLoading ? 'disabled' : ''}`}
                >
                  <PlusIcon className='css-3u52eb' /> Create new data source
                </label>
              </div>
            </Tooltip>
            <CustomButton
              variant='secondary'
              startIcon={<PlusIcon />}
              isDisabled={dataLoading}
              size='small'
              onClick={openAssetSelector}
              tooltipText='Add existing Data Source'
              tooltipPlacement='top'
            >Link to existing data source</CustomButton>
        </div>
      )}
    </>
  )
}

export default FileUpload
