import {
  Box,
  Checkbox,
  FormControl,
  Select,
  Tabs,
  TextInput,
  Tooltip
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import Info from '../../../assets/icons/Info'
import { RootState } from '../../../redux/store'
import '../index.scss'
import { dataUnits, defaults } from '../utils'

function DvGeneral(props: any) {
  const { onChange, changedData, template, isGlobal } = props

  const [generalData, setGeneralData] = useState<any>({})
  /*  const[isSettingMoadalOpen,setIsSettingMoadalOpen]=useState(false)
  const [fontStyles,setFontStyles]=useState('') */
  const dvGlobalData: any = useSelector(
    (state: RootState) => state.dvDashboard.globalConfigData
  )
  useEffect(() => {
    const preferenceData =
      dvGlobalData?.DataVisualization?.[template]?.['general'] ?? defaults?.[template]?.['general']
    setGeneralData((pre) => {
      return {
        ...pre,
        ...preferenceData,
        ...changedData,
      }
    })
  }, [dvGlobalData, changedData])

  const handleGeneralDataChange = (
    key: string,
    value: string | {} | boolean
  ) => {
    const dataToUpdate = {
      ...generalData,
      [key]: value,
    }
    const changedDataToUpdate = {
      ...changedData,
      [key]: value,
    }
    setGeneralData(dataToUpdate)
    onChange({ ...changedDataToUpdate })
  }

  return (
    <Tabs.Panel id='dv-general' className='tabPanelDiv'>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          gap: '30px',
        }}
      >
        <>
          <Box className='genral-tab-col'>
            {!isGlobal && <>
                <FormControl
                  id='dv-pri-title'
                  className='w-100 fieldsFormControl'
            >  <FormControl.Label>Title</FormControl.Label>
                  <TextInput
                    value={generalData?.title}
                    onChange={(e) =>
                      handleGeneralDataChange('title', e.target.value)
                    }
                    placeholder='Enter chart title...'
                  />
                </FormControl>

                <FormControl
                  id='dv-pri-subtitle'
                  className='w-100 fieldsFormControl'
            >  <FormControl.Label>Subtitle</FormControl.Label>
                  <TextInput
                    value={generalData?.subtitle}
                    onChange={(e) =>
                      handleGeneralDataChange('subtitle', e.target.value)
                    }
                    placeholder='Enter chart subtitle...'
                  />
              </FormControl></>}

            <FormControl id='dv-pri-title' className='w-100 fieldsFormControl'>
              <FormControl.Label>Source</FormControl.Label>
              <TextInput
                value={generalData?.sourceText}
                onChange={(e) =>
                  handleGeneralDataChange('sourceText', e.target.value)
                }
                placeholder='Source text'
              />
            </FormControl>
          </Box>
          <Box className='genral-tab-col'>
            {!isGlobal && (
              <FormControl
                id='dv-pri-chart-id'
                className='w-100 fieldsFormControl'
              >
                <div className='SwithWithTooltip'>
                  <FormControl.Label>Chart ID</FormControl.Label>
                  <Tooltip
                    placement='top'
                    id='dv-pri-chart-id-tooltip'
                    content='Add anchor text able to share this chart with external users.'
                  >
                    <Info />
                  </Tooltip>
                </div>
                <TextInput
                  value={generalData?.chartId}
                  onChange={(e) =>
                    handleGeneralDataChange('chartId', e.target.value)
                  }
                  placeholder='insert-chart-id-in-this-format'
                />
              </FormControl>
            )}
            {(template === 'Bar chart' || template === 'Line chart' || template === 'LineRace chart' || template === 'Area chart') && (
              <FormControl
              id='dv-pri-Chart format'
              className='w-100 fieldsFormControl'
              >
                <FormControl.Label>Select chart format</FormControl.Label>
              <Select
                id='dv-pri-title-orientation-controlled'
                name='dv-pri-title-orientation-controlled'
                  value={generalData.format || ''}
                onChange={(e) =>
                  handleGeneralDataChange(
                    'format',
                    e.target.value
                  )
                }
              >
                <Select.Option value='' isDisabled>
                    Chart format
                </Select.Option>
                {['stacked', 'grouped'].map((option) => (
                  <Select.Option value={option}>{option}</Select.Option>
                ))}
              </Select>
              </FormControl>  
            )}
            {(template === 'LineRace chart' || template === 'BarRace chart') && <FormControl id='dv-pri-title' className='w-100 fieldsFormControl'>
              <div className='SwithWithTooltip'>
                <FormControl.Label>Duration</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='dv-pri-Source Text-tooltip'
                  content={`Specify the duration of the ${template} in seconds`}
                >
                  <Info />
                </Tooltip>
              </div>
              <TextInput
                type='number'
                value={generalData?.duration}
                onChange={(e) =>
                  handleGeneralDataChange('duration', e.target.value)
                    }
                placeholder='Duration in seconds'
                min={0}
              />
            </FormControl>
            }
            {template === 'Candlestick chart' && <FormControl
              id='dv-pri-Candlestick tooltip lables'
              className='w-100 fieldsFormControl'
            >
              <FormControl.Label>Tooltip labels</FormControl.Label>
              <TextInput
                value={generalData?.candlstickTooltipLables}
                onChange={(e) =>
                  handleGeneralDataChange('candlstickTooltipLables', e.target.value)
                }
                placeholder='Open, Close, High, Low'
              />
            </FormControl>}

            {(template === 'Pie chart' || template === 'Doughnut chart') &&
              <FormControl
                id='dv-pri-series-name'
                className='w-100 fieldsFormControl'
                  >
                <FormControl.Label>Series name</FormControl.Label>
                <TextInput
                  value={generalData?.seriesName}
                  onChange={(e) =>
                    handleGeneralDataChange('seriesName', e.target.value)
                  }
                  placeholder='Series name'
                />
              </FormControl>
            }
            {template === 'Pie chart' && (
              <FormControl
                id='dv-pri-radius'
                className='w-100 fieldsFormControl'
              ><FormControl.Label>Radius</FormControl.Label>
                <TextInput
                  value={generalData?.radius}
                  onChange={(e) =>
                    handleGeneralDataChange('radius', e.target.value)
                  }
                  placeholder={'Radius in %'}
                />
              </FormControl>
            )}
            {template === 'Doughnut chart' && (
              <>
                <FormControl
                  id='dv-pri-inner-radius'
                  className='w-100 fieldsFormControl'
                >
                  <FormControl.Label>Inner radius</FormControl.Label>
                  <TextInput
                    value={generalData?.innerRadius}
                    onChange={(e) =>
                      handleGeneralDataChange('innerRadius', e.target.value)
                    }
                    placeholder={'Inner radius in %...'}
                  />
                </FormControl>
                <FormControl
                  id='dv-pri-outer-radius'
                  className='w-100 fieldsFormControl'
                > <FormControl.Label>Outer radius</FormControl.Label>
                  <TextInput
                    value={generalData?.outerRadius}
                    onChange={(e) =>
                      handleGeneralDataChange('outerRadius', e.target.value)
                    }
                    placeholder={'Outer radius radius in %...'}
                  />
                </FormControl>
              </>
            )}
            {(template === 'Pie chart' || template === 'Doughnut chart' || template === 'Treemap chart') && (
              <>
                <FormControl
                  id='dv-pri-data-unit'
                  className='w-100 fieldsFormControl'
                > <FormControl.Label>Select data format</FormControl.Label>
                  <Select
                    id='dv-pri-data-unit-controlled'
                    name='dv-pri-data-unit-controlled'
                    value={generalData.dataUnit || ''}
                    onChange={(e) => handleGeneralDataChange('dataUnit', e.target.value)}
                  >
                    <Select.Option value="" isDisabled>
                    Format data
                    </Select.Option>
                    {dataUnits.map(({ value, label }) => (
                      <Select.Option key={label} value={value}>
                        {label}
                      </Select.Option>
                    ))}
                  </Select>
                </FormControl>
                {/*  <FormControl
                id={`Affix`}
                className='w-100 fieldsFormControl'
              >
                <div className='SwithWithTooltip'>
                  <FormControl.Label>Affix</FormControl.Label>
                  <Tooltip
                    placement='top'
                    id={`dv-Affix`}
                    content={'Provide an Affix'}
                  >
                    <Info />
                  </Tooltip>
                </div>

                <TextInput
                  value={generalData?.['Affix']}
                  onChange={(e) =>
                    handleGeneralDataChange('Affix', e.target.value)
                  }
                  placeholder='Enter Affix (e.g., ml, %)...'
                />
              </FormControl>
              <FormControl
                id={`Affix`}
                className='w-100 fieldsFormControl'
              >
                <div className='SwithWithTooltip'>
                  <FormControl.Label>Affix Placement</FormControl.Label>
                  <Tooltip
                    placement='top'
                    id={`dv-Affix`}
                    content={'Provide an Affix Placement'
                    }
                  >
                    <Info />
                  </Tooltip>
                </div>
                <Select
                  value={generalData?.['AffixPlacement'] || 'suffix'}
                  onChange={(e) =>
                    handleGeneralDataChange('AffixPlacement', e.target.value)
                  }
                >
                  <Select.Option value='prefix'>Prefix</Select.Option>
                  <Select.Option value='suffix'>Suffix</Select.Option>
                </Select>
              </FormControl> */}
              </>
            )}

          </Box>
          <Box className='genral-tab-col' >
            <FormControl
              id='pri-dv-isDownloadable'
              className='w-100 fieldsFormControl'
            >
              <div className='SwithWithTooltip'>
                <Checkbox
                  name='dv isDownloadable'
                  id='show-dv-pri-isDownloadable'
                  onChange={() =>
                    handleGeneralDataChange(
                      'isDownloadable',
                      !generalData.isDownloadable
                    )
                  }
                  className='switchRoot'
                  isChecked={generalData.isDownloadable}
                >
                  Enable download
                </Checkbox>
              </div>
            </FormControl>
            <FormControl id='pri-dv-tooltip' className='w-100 fieldsFormControl'>
              <div className='SwithWithTooltip'>
                <Checkbox
                  name='show dv tooltip'
                  id='show-dv-pri-tooltip'
                  onChange={() =>
                    handleGeneralDataChange(
                      'isTooltip',
                      !generalData.isTooltip
                    )
                  }
                  className='switchRoot'
                  isChecked={generalData.isTooltip}
                >
                  Enable Tooltip
                </Checkbox>
              </div>
            </FormControl>
            {(template !== 'Pie chart' && template !== 'Doughnut chart' && !isGlobal) && (
              <FormControl id='pri-dv-zoom' className='w-100 fieldsFormControl'>
                <div className='SwithWithTooltip'>
                  <Checkbox
                    name='show dv zoom'
                    id='show-dv-pri-zoom'
                    onChange={() =>
                      handleGeneralDataChange(
                        'isZoomable',
                        !generalData.isZoomable
                      )
                    }
                    className='switchRoot'
                    isChecked={generalData.isZoomable}
                  >
                    Enable Zoom
                  </Checkbox>
                </div>
              </FormControl>
            )}
          {/*   {(template === 'LineRace chart' || template === 'Line chart') &&
              <FormControl id='pri-dv-tooltip' className='w-100 fieldsFormControl'>
                <div className='SwithWithTooltip'>
                  <Checkbox
                    name='show dv tooltip'
                    id='show-dv-pri-tooltip'
                    onChange={() =>
                      handleGeneralDataChange(
                        'isEndLable',
                        !generalData.isEndLable
                      )
                    }
                    className='switchRoot'
                    isChecked={generalData.isEndLable}
                  >
                    Enable End Lable
                  </Checkbox>
                </div>
              </FormControl>} */}

            {template === 'BarRace chart' && <FormControl id='pri-dv-isLoop' className='w-100 fieldsFormControl'>
              <div className='SwithWithTooltip'>
                <Checkbox
                  name='show dv isLoop'
                  id='show-dv-pri-isLoop'
                  onChange={() =>
                    handleGeneralDataChange('isLoop', !generalData.isLoop)
                  }
                  className='switchRoot'
                  isChecked={generalData.isLoop}
                >
                  Enable Loop
                </Checkbox>
                <Tooltip
                  placement='top'
                  id='dv-pri-loop-tooltip'
                  content='Enable this option to loop continuously.'
                >
                  <Info />
                </Tooltip>
              </div>
            </FormControl>}
            {/*   {(template === 'Line chart' || template === 'LineRace chart' || template === 'Combo chart') &&
              <FormControl id='smooth vs sharp line' className='w-100 fieldsFormControl'>
                <div className='SwithWithTooltip'>
                  <Checkbox
                    name='smooth vs sharp line'
                    id='smooth vs sharp line'
                    onChange={() =>
                      handleGeneralDataChange(
                        'isSmooth',
                        !generalData.isSmooth
                      )
                    }
                    className='switchRoot'
                    isChecked={generalData.isSmooth}
                  >
                    Enable Smooth
                  </Checkbox>
                </div>
              </FormControl>} */}
          </Box>
          <Box className='genral-tab-col'>
            {(template === 'Bar chart' || template === 'BarRace chart' || template === 'Waterfall chart' || template === 'Combo chart' || template === 'BarStackNormalization chart') &&
          <><FormControl id='pri-dv-show-Data-Lable' className='w-100 fieldsFormControl'>
            <div className='SwithWithTooltip'>
              <Checkbox
                name='show dv data Lable'
                id='show-dv-pri-data-lable'
                onChange={() =>
                  handleGeneralDataChange('showDataLable', !generalData.showDataLable)
                }
                className='switchRoot'
                isChecked={generalData.showDataLable}
              >
              Enable Data Label
                </Checkbox>
            </div>
        </FormControl>
            {generalData.showDataLable && (
              <FormControl
                id='dv-pri-Data-lable-placement'
                className='w-100 fieldsFormControl'
              >
                  <div className='SwithWithTooltip'>
                </div>
                <Select
                  id='dv-pri-Data-lable-placement-controlled'
                  name='dv-pri-Data-lable-placement-controlled'
                  value={generalData.dataLablePlacement || ''}
                  onChange={(e) => handleGeneralDataChange('dataLablePlacement', e.target.value)}
                >
                  <Select.Option value="" isDisabled>
                      Select placement
                  </Select.Option>
                    {['inside', 'insideTop', 'insideBottom', 'insideLeft', 'insideRight', 'top', 'right'].map(option => <Select.Option value={option}>{option}</Select.Option>)}
                </Select>
              </FormControl>)}</>}
      </Box>
        </>
      </Box>


    </Tabs.Panel>
  )
}

export default DvGeneral
