h6 {
  margin: 0 0 20px 0;
}
strong{
  font-weight: bold;
}
.header {
  padding: 1rem;
}

.templeFormControl {
  margin: 0 !important;
  scroll-behavior: smooth;
}

.dataSourceFormControl {
  margin: 0 15px !important;
}

.preferenceFormControl {
  margin: 0 !important;
  width: calc(100%);
}

.fieldsFormControl {
  margin: 10px 0 !important;
}

.dvModalRoot {
  width: 100vw !important;
  min-height: 90vh !important;
  height: fit-content !important;
  max-height: 90vh !important;
  position: relative;
}

.dvModalContent {
  overflow: auto !important;
  width: 100% !important;
  height: calc(90vh - 107px) !important;
  padding: 0 !important;
}

.mdlTitle {
  margin: 0px;
  padding: 0px;
  font-family:'Aeonik Regular',Serif;
  color: rgb(17, 27, 43);
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: 0rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  display: flex;
  justify-content: space-between;
}

.headerBox {
  width: 100%;
}

.SwithWithTooltip {
  display: flex;
  justify-content: flex-start;
  align-items: start;
  gap: 2px;
  cursor: pointer;
}

.formLableWIcon {
  display: flex;
  justify-content: flex-start;
  align-items: start;
  gap: 2px;
  cursor: pointer;
  margin-left: 10px;
  margin-bottom: 10px;
}

.endHorizontalLine {
  margin-top: 0px !important;
}

.content-root {
  margin: 20px 20px 70px 1rem;
}

.edit {
  //display: flex;
  justify-content: flex-start;
  align-items: center;
  width: fit-content;
  gap: 5px;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #111b2b;
  font-weight: 500;
  padding: 0.5rem 1rem;
  cursor: pointer;
  position: relative;

  &:hover {
    background-color: rgba(17, 27, 43, 0.05);
  }
}

.lable {
  font-size: 16px;
  display: flex;
  justify-content: center;
}

.template-selection {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: start;
  cursor: pointer;

  .template-card {
    overflow: hidden;
    transition: transform 0.2s;
    //box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;
    border-radius: 5px;
    position: relative;

    img {
      width: 100%;
      height: auto;
    }

    p {
      text-align: center;
      margin: 5px;
    }

    &:hover::before {
      border: 5px solid #0070f3;
      transition: border-color 0.2s;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 1px solid #ddd;
      border-radius: 5px;
      pointer-events: none;
      transition: border-color 0.2s;

      &:hover {
        transform: translateY(0px);
        border: 5px solid #0070f3;
      }
    }

    &.selected::before {
      border: 5px solid #0070f3;
    }
  }
}

.chartFile {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;
  margin: 20px;
  gap: 30px;
}

.cols {
  width: 50%;
}

.row {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: start;
  justify-content: start;
}

.color-picker {
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  width: 50%;
}

.bg {
  background-color: rgba(17, 27, 43, 0.05);
}

.preview1 {
  width: 70%;
  margin: 0 15px;
}

.preview {
  width: 60%;
  height: 100% ;
}

.summery {
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 20px;

  p {
    font-size: 15px;
  }

  h6 {
    margin: 0;
  }
}

.css-9p6xbs {
  font-size: 16px;
}

.useTemplate {
}

.preferenceDiv {
  min-width: 200px;
  padding: 10px 20px 20px;
}

.color {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #c6c6c6;
}

.popoverDiv {
  display: flex;
  gap: 10px;
  padding: 10px 20px 20px;
}

.w-50 {
  width: 50%;
}
.genral-tab-col {
  display: flex;
  justify-content: start;
  flex-direction: column;
  align-items: start,
}
.genral-tab-col:empty {
  width: auto;
}

.genral-tab-col:not(:empty) {
  width: 30%;
}
.w-30 {
  width: 50%;
}

.w-100-imp {
  width: 100% !important;
}
.w-100 {
  width: 100%;
}
.w-70 {
  width: 70%;
}
.buttonsRoot {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vertical-line {
  position: absolute;
  border-left: 1px solid rgb(207, 217, 224);
  height: 100%;
  top: 60px;
}

.tabPanelDiv {
  height: 100%;
  min-height: auto;
  width: calc(100% - 30px);
}

.switchRoot {
  padding: 0px !important;
}

.colorPickerModal {
  width: 100%;
}

.modelControl {
  padding: 0.5rem 1rem !important;
  border-top: 1px solid rgb(207, 217, 224);
  bottom: 0;
  position: fixed;
  background: #fff;
  left: 0;
}

.download-link {
  border: none;
}

.blurred {
  filter: blur(5px);
  pointer-events: none;
}

.closeMsg {
  margin: 35vh 0 34vh 0;
  text-align: center;
  font-size: 24px;
  color: #006d23;
}

.box {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 30px;
  background: #efefef;
  padding: 15px;
  border-radius: 5px;
}
.axisDiv {
  width: calc(50% - 30px);
  margin-bottom: 20px;
}
.axismodal{
  box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;
  background-color: #c6c6c6;
  max-height: 80%;
  overflow-y: auto;
}
.markLineDiv {
  padding: 10px;
  width: calc(25% - 30px);
  
  //box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;
}

.style-picker {
  padding: 20px;
  .m-header {
    padding: 0 0 20px 0;
    h2 {
      display: flex;
    }
  }
  .m-content {
    margin: 10px 0;
  }
}
.tabsRoot {
  position: relative;
  .tab-list {
    overflow-x: auto;
    &::-webkit-scrollbar {
      display: none;
    }
    &::before {
      content: '';
      position: absolute;
      top: 0px;
      bottom: 0px;
      width: 16px;
      height: 46px;
      z-index: 1;
      pointer-events: none;
      left: 0px;
      background: linear-gradient(
        to right,
        rgb(255, 255, 255),
        rgba(255, 255, 255, 0)
      );
    }
    &::after {
      content: '';
      position: absolute;
      top: 0px;
      bottom: 0px;
      width: 16px;
      height: 46px;
      z-index: 1;
      pointer-events: none;
      right: 0px;
      background: linear-gradient(
        to left,
        rgb(255, 255, 255),
        rgba(255, 255, 255, 0)
      );
    }
  }
}
.hoverContainer {
  position: relative;
  &:hover {
    .shareContainer {
      display: flex;
    }
    .downloadContainer {
      display: flex;
    }
  }
}
.shareContainer {
  background-color: #fff;
  padding: 10px;
  box-shadow: 0px 10px 100px 1px rgba(10, 44, 55, 0.1);
  border-radius: 4px;
  gap: 10px;
  position: absolute;
  display: none;
  justify-content: space-between;
  flex-wrap: wrap;
  top: 30px;
  right: 0px;
  width: 100px;
  z-index: 2;
}
.downloadContainer {
  background-color: #fff;
  padding: 10px;
  box-shadow: 0px 10px 100px 1px rgba(10, 44, 55, 0.1);
  border-radius: 4px;
  gap: 10px;
  position: absolute;
  display: none;
  justify-content: space-between;
  flex-wrap: wrap;
  top: 30px;
  left: 0px;
  width: 100px;
  z-index: 2;
}

.tooltip-container {
  font-family: 'Aeonik', sans-serif;
  color: #333333;
  padding: 8px;


  .tooltip-heading {
    font-size: 18px;
    font-family: 'Aeonik bold', sans-serif;
    margin-bottom: 8px;
  }

  .tooltip-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    font-family: 'Aeonik', sans-serif;
    font-size: 16px;
    margin-bottom: 4px;
  }

  .tooltip-candle-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    font-family: 'Aeonik', sans-serif;
    font-size: 12px;
    margin-bottom: 4px;
  }

  .tooltip-candle-series {
    display: flex;
    align-items: start;
    flex-direction: column;
    margin-bottom: 10px;
  }

  .tooltip-series {
    display: flex;
    align-items: center;
    font-family: 'Aeonik', sans-serif;
    font-size: 16px;
  }

  .tooltip-candle-marker {
    display: inline-block;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    margin-right: 6px;
  }

  .tooltip-marker {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
  }


  .tooltip-value {
    font-family: 'Aeonik bold', sans-serif;
  }

}