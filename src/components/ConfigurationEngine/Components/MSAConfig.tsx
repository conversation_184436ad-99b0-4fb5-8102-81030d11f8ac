import { EditorAppSDK, HomeAppSDK } from '@contentful/app-sdk'
import { Box, Form, Tabs } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { getConfigurationsCollectionQuery } from '../../../globals/queries'
import {
  fetchGraphQL,
  findValueByKey,
  getEntryDataById,
  updateEntryData,
  updateValuesInObject,
} from '../../../globals/utils'
import CacheControl from '../TabsPanels/Cache'
import Experimentation from '../TabsPanels/Experimentation'
import NotificationStepperForm from '../TabsPanels/Notifications/NotificationStepperForm'
import FooterScripts from '../TabsPanels/Scripts/FooterScripts'
import HeaderScripts from '../TabsPanels/Scripts/HeaderScripts'
import Description from '../TabsPanels/SEO/Description'
import GTM from '../TabsPanels/SEO/GTM'
import RobotsTxt from '../TabsPanels/SEO/RobotsTxt'
import Title from '../TabsPanels/SEO/Title'
import HostedFiles from '../TabsPanels/SiteSettings/HostedFiles'
import SupportedLanguages from '../TabsPanels/SiteSettings/Languages'
import Redirects from '../TabsPanels/SiteSettings/Redirects'
import Rewrites from '../TabsPanels/SiteSettings/Rewrites'
import ConfiguratorDefaultScreen from './GlobalConfigDefault'

function MSAConfig({
  sdk,
  selectedDomain,
}: {
  sdk: EditorAppSDK | HomeAppSDK
  selectedDomain: string
}) {
  const [selectedCategory, setSelectedCategory] = useState('default') // default

  const [currentTab, setCurrentTab] = useState('')

  const [msaConfigData, setMsaConfigData] = useState<any>({})

  const [contentId, setContentId] = useState('')

  const handleMsaConfigDataChange = (key: string, value: any) => {
    const d = updateValuesInObject(msaConfigData, { [key]: value })
    setMsaConfigData(d)
  }

  const fetchData = async () => {
    const res = await fetchGraphQL(getConfigurationsCollectionQuery()).then(
      (res: any) => res?.data?.configurationsCollection?.items
    )

    const idToFetch =
      res?.find((item: any) => {
        return (
          item?.internalName
            ?.split(' ')
            ?.includes(selectedDomain?.toUpperCase()) && item?.type === 'MSA'
        )
      })?.sys?.id || ''
    if (idToFetch) {
      setContentId(idToFetch)
      const response = await getEntryDataById(idToFetch).then((res: any) => {
        return res?.fields?.data?.['en-CA']?.content?.[0]?.content?.[0]?.value
      })
      console.log(idToFetch, res, response)
      if (!response) return
      setMsaConfigData(JSON.parse(response))
    }
  }

  const handleSave = async () => {
    if (contentId) {
      const response = await getEntryDataById(contentId).then((res: any) => {
        return res.fields
      })

      let updatedPayload = {
        ...response,
        data: {
          'en-CA': {
            content: [
              {
                content: [
                  {
                    data: {},
                    marks: [],
                    nodeType: 'text',
                    value: JSON.stringify(msaConfigData),
                  },
                ],
                data: {},
                nodeType: 'paragraph',
              },
            ],
            data: {},
            nodeType: 'document',
          },
        },
      }

      await updateEntryData(contentId, updatedPayload)
    }
  }

  const selectedCatObj = () =>
    CATEGORIES.find((item) => item.title === selectedCategory) || ({} as any)

  useEffect(() => {
    if (selectedCategory) {
      const tabToSelect = selectedCatObj()?.catFields?.[0]?.value
      tabToSelect && setCurrentTab(tabToSelect)
    }
  }, [selectedCategory])

  useEffect(() => {
    if (selectedDomain) {
      fetchData()
    }
  }, [selectedDomain])

  return (
    <Box
      style={{
        height: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'start',
        flexDirection: 'column',
      }}
    >
      <Form
        className=''
        style={{
          height: '100%',
          width: '100%',
        }}
      >
        <div className={'customiserRoot'}>
          <div className={'customiserInnerRoot'}>
            <aside className='customiserSidebar'>
              <div className={'listRoot'}>
                <div className={'categoryRoot'}>
                  <ul>
                    {CATEGORIES?.map((item, index) => {
                      if (!item.isEnabled) return null

                      return (
                        <li
                          key={index}
                          onClick={() => setSelectedCategory(item.title)}
                          className={`${selectedCategory === item.title ? 'active' : ''
                            } pointer catList`}
                          style={{
                            fontWeight: 500,
                            paddingLeft: '16px !important',
                            padding: '10px !important',
                          }}
                        >
                          {item.title}
                        </li>
                      )
                    })}
                  </ul>
                </div>
              </div>
            </aside>

            <main
              className={'mainRoot formRoot'}
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                alignItems: 'start',
              }}
            >
              <div
                className={'previewRoot'}
                style={{
                  minHeight: '350px',
                  height: '100%',
                }}
              >
                {selectedCatObj()?.catFields?.length > 0 && (
                  <Tabs
                    currentTab={currentTab}
                    onTabChange={setCurrentTab}
                    style={{
                      width: '100%',
                    }}
                  >
                    <Tabs.List variant='horizontal-divider'>
                      {selectedCatObj()?.catFields?.map(
                        (item: any, index: number) => {
                          return (
                            <Tabs.Tab panelId={item.value} key={index}>
                              {item.title}
                            </Tabs.Tab>
                          )
                        }
                      )}
                    </Tabs.List>
                    <GTM
                      handleChange={handleMsaConfigDataChange}
                      value={findValueByKey(msaConfigData, 'gtmId')}
                      sdk={sdk}
                    />
                    <HeaderScripts
                      handleChange={handleMsaConfigDataChange}
                      value={msaConfigData['headerScripts']}
                      sdk={sdk}
                    />
                    <FooterScripts
                      handleChange={handleMsaConfigDataChange}
                      value={msaConfigData['footerScripts']}
                      sdk={sdk}
                    />
                    <RobotsTxt
                      sdk={sdk}
                      handleChange={handleMsaConfigDataChange}
                      value={msaConfigData['robotsTxt']}
                    />
                    <Title
                      handleChange={handleMsaConfigDataChange}
                      value={msaConfigData['title']}
                      sdk={sdk}
                    />
                    <Description
                      handleChange={handleMsaConfigDataChange}
                      value={msaConfigData['description']}
                      sdk={sdk}
                    />

                    <Redirects
                      sdk={sdk}
                      handleChange={handleMsaConfigDataChange}
                      value={msaConfigData['redirects']}
                    />
                    <Rewrites
                      sdk={sdk}
                      handleChange={handleMsaConfigDataChange}
                      value={msaConfigData['rewrites']}
                    />
                    <SupportedLanguages
                      handleChange={handleMsaConfigDataChange}
                      value={msaConfigData['supportedLanguages']}
                      sdk={sdk}
                    />
                  </Tabs>
                )}
                {selectedCategory === 'Cache' && (
                  <CacheControl
                    selectedDomain={selectedDomain}
                    handleChange={handleMsaConfigDataChange}
                    value={findValueByKey(msaConfigData, 'cacheControl')}
                  />
                )}
                {selectedCategory === 'Hosted Files' && (
                  <HostedFiles
                    sdk={sdk}
                    handleChange={handleMsaConfigDataChange}
                    value={msaConfigData['hostedFiles']}
                  />
                )}
                {selectedCategory === 'Notifications' && (
                  <NotificationStepperForm domain={selectedDomain} />
                )}
                {selectedCategory === 'Experimentation' && (
                  <Experimentation
                    domain={selectedDomain}
                    contentId={contentId}
                    handleChange={handleMsaConfigDataChange}
                    value={msaConfigData}
                    refetchData={fetchData}
                  />
                )}
                {selectedCategory === 'default' && (
                  <ConfiguratorDefaultScreen />
                )}
              </div>
            </main>
          </div>
        </div>
      </Form>
    </Box>
  )
}

export default MSAConfig

const CATEGORIES = [
  {
    title: 'Scripts',
    value: 1,
    catFields: [
      { title: 'Header Scripts', value: 'headerScripts' },
      { title: 'Footer Scripts', value: 'footerScripts' },
    ],
    isEnabled: false,
  },
  {
    title: 'SEO',
    value: 2,
    catFields: [
      { title: 'GTM', value: 'gtm' },
      { title: 'Schema', value: 'schema' },
      { title: 'Robots', value: 'robots' },
    ],
    isEnabled: false,
  },

  {
    title: 'Cache',
    value: 3,
    isEnabled: true,
  },
  {
    title: 'Hosted Files',
    value: 4,
    isEnabled: true,
  },
  {
    title: 'Routing',
    value: 5,
    catFields: [
      { title: 'Redirects', value: 'redirects' },
      { title: 'Rewrites', value: 'rewrites' },
    ],
    isEnabled: false,
  },
  {
    title: 'Languages',
    value: 6,
    catFields: [{ title: 'Languages', value: 'languages' }],
    isEnabled: false,
  },
  {
    title: 'Notifications',
    value: 7,
    isEnabled: true,
  },
  {
    title: 'Experimentation',
    value: 8,
    isEnabled: true,
  },
]
