import { Box } from '@contentful/f36-components'
import React from 'react'
import GlobalConfigEngineLogo from '../../../assets/logo-CE.png'

function ConfiguratorDefaultScreen() {
  return (
    <Box
      style={{
        display: 'flex',
        justifyContent: 'start',
        alignItems: 'start',
        width: '100%',
        padding: '16px',
        paddingTop: 0,
        height: '100%',
        flexDirection: 'column',
        gap: '10px',
      }}
      className='tabPanelRoot'
    >
      <div
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: '-20px',
          width: '100%',
        }}
      >
        <div
          style={{
            display: 'flex',
            width: '100%',
            justifyContent: 'center',
          }}
        >
          <img src={GlobalConfigEngineLogo} height={200} width={200} alt='' />
        </div>
        {/* <h2>
        <b>Configuration Engine</b>
        </h2> */}
        <p
          style={{
            textAlign: 'center',
            fontSize: '14px',
            paddingInline: '60px',
          }}
        >
          Configurator powers experience preferences and productivity settings
          across the MSA platform. <br />
          Use this panel to quickly and easily set common editorial preferences
          and behavioural settings globally.
        </p>
      </div>
    </Box>
  )
}

export default ConfiguratorDefaultScreen
