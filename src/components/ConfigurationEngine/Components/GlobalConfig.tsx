import {
  Box,
  Button,
  Flex,
  Form,
  ModalControls,
  Spinner,
  Tabs,
  Text,
  Tooltip,
} from '@contentful/f36-components'
import React, { useContext, useEffect, useState } from 'react'
import Done from '../../../assets/icons/Done'
import Revert from '../../../assets/icons/Revert'
import { GlobalContext } from '../../../contexts/globalContext'
import {
  createContentEntry,
  fetchGlobalConfigurationData,
  getEntryDataById,
  updateEntryData,
} from '../../../globals/utils'
import DvAxes from '../TabsPanels/DataVisualization/Axes'
import ChartAndDimensions from '../TabsPanels/DataVisualization/Chart-Dimensions'
import DvGeneral from '../TabsPanels/DataVisualization/General'
import DvStyles from '../TabsPanels/DataVisualization/Styles'
import PushNotification from '../TabsPanels/Notifications/PushNotification'
import PSDiscovery from '../TabsPanels/PageSettings/Discovery'
import PSGeneral from '../TabsPanels/PageSettings/General'
import PSNavigation from '../TabsPanels/PageSettings/Navigation'
import PSSEO from '../TabsPanels/PageSettings/SEO'
import ConfiguratorDefaultScreen from './GlobalConfigDefault'
import UpcomingFeatComp from './UpcomingFeatComp'

function GlobalConfig() {
  const [currentTab, setCurrentTab] = useState('dv-title')

  const [categoryTab, setCategoryTab] = useState('system')

  const [selectedCategory, setSelectedCategory] = useState(0)

  const [contentId, setContentId] = useState('')

  const [dataLoading, setDataLoading] = useState(false)

  const [saveLoading, setSaveLoading] = useState(false)

  const [globalConfigJsonData, setGLobalConfigJsonData] = useState<any>({})

  const [defaultDataToRevert, setDefaultDataToRevert] = useState<any>({})

  const { isConfigSettingsModalOpen } = useContext(GlobalContext)

  const handleDvConfigJsonDataChange = (data: any) => {
    setGLobalConfigJsonData((prev: any) => {
      return {
        ...prev,
        DataVisualization: {
          ...prev.DataVisualization,
          ...data,
        },
      }
    })
  }

  const handleSave = async () => {
    if (selectedCategory === 2) {
      setSaveLoading(true)
      if (contentId) {
        const response = await getEntryDataById(contentId).then((res: any) => {
          return res.fields
        })

        let updatedPayload = {
          ...response,
          data: {
            'en-CA': {
              content: [
                {
                  content: [
                    {
                      data: {},
                      marks: [],
                      nodeType: 'text',
                      value: JSON.stringify(globalConfigJsonData),
                    },
                  ],
                  data: {},
                  nodeType: 'paragraph',
                },
              ],
              data: {},
              nodeType: 'document',
            },
          },
        }

        await updateEntryData(contentId, updatedPayload)
      } else {
        const fields = {
          internalName: {
            'en-CA': 'Config Global',
          },
          type: {
            'en-CA': 'Global',
          },
          data: {
            'en-CA': {
              content: [
                {
                  content: [
                    {
                      data: {},
                      marks: [],
                      nodeType: 'text',
                      value: JSON.stringify(globalConfigJsonData),
                    },
                  ],
                  data: {},
                  nodeType: 'paragraph',
                },
              ],
              data: {},
              nodeType: 'document',
            },
          },
        }

        const res: any = await createContentEntry('configurations', fields)
        const newContentId = res?.sys?.id || ''
        setContentId(newContentId)
      }
      setSaveLoading(false)
    } else {
    }
  }
  const fetchData = async () => {
    setDataLoading(true)

    const response: any = await fetchGlobalConfigurationData()

    response && response.data && setGLobalConfigJsonData(response.data)

    response && response.data && setDefaultDataToRevert(response.data)

    response && response.contentId && setContentId(response.contentId)

    setDataLoading(false)
  }

  const handleRevertDataForSpecificCategory = () => {
    if (contentId) {
      const revertedDataForCat =
        defaultDataToRevert?.DataVisualization?.[currentTab.split('-')[1]]

      const newData = {
        ...globalConfigJsonData.DataVisualization,
        [currentTab.split('-')[1]]: revertedDataForCat,
      }

      setGLobalConfigJsonData((prev: any) => {
        return {
          ...prev,
          DataVisualization: {
            ...newData,
          },
        }
      })
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    if (selectedCategory) {
      const d = GLobalCategories[selectedCategory - 1]

      setCurrentTab(d?.catFields?.[0]?.value || '')
    }
  }, [selectedCategory])

  useEffect(() => {
    if (!isConfigSettingsModalOpen) {
      setGLobalConfigJsonData({})
      setContentId('')
      setCurrentTab('')
    }
  }, [isConfigSettingsModalOpen])

  return (
    <>
      <Form
        className=''
        style={{
          height: '92%',
        }}
      >
        <div className={'customiserRoot'}>
          <div className={'customiserInnerRoot'}>
            <aside className='customiserSidebar'>
              <div className={'listRoot'}>
                <div className={'categoryRoot'}>
                  <Tabs
                    currentTab={categoryTab}
                    onTabChange={setCategoryTab}
                    style={{
                      width: '100%',
                    }}
                  >
                    <Tabs.List variant='horizontal-divider'>
                      <Tabs.Tab panelId={'system'}>Systems</Tabs.Tab>
                      <Tabs.Tab panelId={'components'} isDisabled>
                        Components
                      </Tabs.Tab>
                    </Tabs.List>
                    <Tabs.Panel id='system' className=''>
                      <ul>
                        {GLobalCategories?.map((item, index) => {
                          if (!item.isEnabled) return null
                          return (
                            <li
                              key={index}
                              onClick={() => setSelectedCategory(item.value)}
                              className={`${
                                selectedCategory === item.value ? 'active' : ''
                              } pointer catList`}
                            >
                              {item.title}
                            </li>
                          )
                        })}
                      </ul>
                    </Tabs.Panel>
                  </Tabs>
                  {/* <h6 className={'categoryHeading'}>Categories</h6> */}
                </div>
              </div>
            </aside>

            <main className={'mainRoot formRoot'}>
              <div
                className={'tabsRoot '}
                style={{
                  height: '100%',
                }}
              >
                {selectedCategory === 0 && <ConfiguratorDefaultScreen />}
                <Tabs
                  currentTab={currentTab}
                  onTabChange={setCurrentTab}
                  style={{
                    width: '100%',
                  }}
                >
                  {categoryTab === 'system' && (
                    <>
                      <Tabs.List variant='horizontal-divider'>
                        {GLobalCategories[selectedCategory - 1]?.catFields?.map(
                          (item, index) => {
                            return (
                              <Tabs.Tab panelId={item.value} key={index}>
                                {item.title}
                              </Tabs.Tab>
                            )
                          }
                        )}
                      </Tabs.List>

                      <ChartAndDimensions
                        onChange={handleDvConfigJsonDataChange}
                        value={
                          globalConfigJsonData?.DataVisualization?.['chart']
                        }
                        loading={dataLoading}
                      />

                      <DvStyles
                        onChange={handleDvConfigJsonDataChange}
                        value={
                          globalConfigJsonData?.DataVisualization?.['styles']
                        }
                        loading={dataLoading}
                      />

                      <DvGeneral
                        onChange={handleDvConfigJsonDataChange}
                        value={
                          globalConfigJsonData?.DataVisualization?.['general']
                        }
                        loading={dataLoading}
                      />
                      <DvAxes
                        onChange={handleDvConfigJsonDataChange}
                        value={
                          globalConfigJsonData?.DataVisualization?.['axes']
                        }
                        loading={dataLoading}
                      />
                      <PSGeneral />
                      <PSDiscovery />
                      <PSNavigation />
                      <PSSEO />
                    </>
                  )}
                </Tabs>
                {selectedCategory === 4 && (
                  <UpcomingFeatComp
                    bgColor='#e8f0fc'
                    borderColor='#bdd8ff'
                    color='#1040a3'
                    title='Experimentation'
                    aboutTitle='Experimentation'
                    about='We are excited to share that our team is developing an Experimentation feature. This will enable team to run experiments and compare different versions of the content to make data-driven decisions. Stay tuned for updates as we refine this powerful tool!'
                    desc='With content growth and increasing traffic, A/B testing is crucial for optimizing website performance by comparing different content, design, and feature versions based on user behavior. This data-driven approach ensures changes align with user preferences, enhancing engagement, conversions, and satisfaction while reducing risks and costs through effective, iterative testing.'
                  />
                )}
                {selectedCategory === 1 && (
                  <UpcomingFeatComp
                    bgColor='#f2e6ff'
                    borderColor='#d1c1ff'
                    color='#6b32af'
                    title='Content Discovery'
                    desc='MSA-powered Crossposting enables publishing the same content across multiple websites, maintaining relevance, user interest, and building a strong network of interconnected web properties, thereby increasing traffic. Real-time notifications and the AI-enhanced MSA Search System improve content discovery, accuracy, and personalized recommendations, automating content organization for better user experience and increased engagement, traffic, and conversions.'
                  />
                )}
                {selectedCategory === 3 && (
                  <UpcomingFeatComp
                    bgColor='#edf7eb'
                    borderColor='#cce3c2'
                    color='#2d6a4f'
                    title='Documentation'
                    desc='Documenting the codebase and creating guides enhances team efficiency and prevents the project from becoming a black box. Clear documentation aids onboarding, promotes consistency and collaboration, and serves as a reference for troubleshooting and maintenance. It supports scalability, code reuse, and knowledge transfer, ultimately improving communication and clarifying requirements.'
                  />
                )}
                {selectedCategory === 6 && <PushNotification />}
              </div>
            </main>
          </div>
        </div>
      </Form>
      {selectedCategory === 2 && (
        <>
          <hr className='endHorizontalLine' />
          <Box
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
            }}
          >
            <ModalControls
              style={{
                paddingLeft: '1rem',
                paddingBottom: '1rem',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '100%',
              }}
            >
              <Tooltip
                content='Revert'
                style={{
                  position: 'absolute',
                  left: '10px',
                }}
              >
                <Button
                  type='submit'
                  variant='negative'
                  size='small'
                  testId='confirm-multi-site-slug'
                  onClick={handleRevertDataForSpecificCategory}
                  isLoading={saveLoading}
                  isDisabled={dataLoading || saveLoading}
                  startIcon={<Revert />}
                ></Button>
              </Tooltip>
            </ModalControls>
            <ModalControls
              style={{
                paddingRight: '1rem',
                paddingBottom: '1rem',
                display: 'flex',
                justifyContent: 'end',
                alignItems: 'center',
                width: '100%',
              }}
            >
              {dataLoading ? (
                <Flex>
                  <Text marginRight='spacingXs'>Loading</Text>
                  <Spinner />
                </Flex>
              ) : (
                <></>
              )}
              <Tooltip id='0-tooltip-1' content='Save' placement='left'>
                <Button
                  type='submit'
                  variant='positive'
                  size='small'
                  testId='confirm-multi-site-slug'
                  onClick={handleSave}
                  isLoading={saveLoading}
                  isDisabled={dataLoading || saveLoading}
                  startIcon={<Done />}
                ></Button>
              </Tooltip>
            </ModalControls>
          </Box>
        </>
      )}
    </>
  )
}

export default GlobalConfig

const GLobalCategories = [
  {
    title: 'Content Discovery',
    value: 1,
    isEnabled: false,
  },
  {
    title: 'Data Visualisation',
    value: 2,
    catFields: [
      { title: 'General', value: 'dv-general' },
      { title: 'Chart & Dimensions', value: 'dv-chart' },
      { title: 'Styles', value: 'dv-styles' },
      { title: 'Axes', value: 'dv-axes' },
    ],
    isEnabled: true,
  },
  {
    title: 'Documentation',
    value: 3,
    isEnabled: false,
  },
  {
    title: 'Experimentation',
    value: 4,
    isEnabled: false,
  },
  {
    title: 'MSA',
    value: 5,
    isEnabled: false,
  },
  {
    title: 'Notifications',
    value: 6,
    isEnabled: true,
  },
  {
    title: 'Page Settings',
    value: 7,
    catFields: [
      { title: 'General', value: 'ps-general' },
      { title: 'Discovery', value: 'ps-discovery' },
      { title: 'Navigation', value: 'ps-navigation' },
      { title: 'SEO', value: 'ps-seo' },
    ],
    isEnabled: false,
  },
]
