{"about": {"type": "Website", "code": "AGL"}, "content": {"isEnabled": true, "data": {"translation": {"langs": [], "urlFormat": ""}, "seo": {"title": "Altus Group", "description": "Description of Altus Group website.", "structuredData": {}, "robots": {}}, "multimedia": {"imgQuality": 100, "imgSrc": ""}}}, "state": {"isEnabled": true, "data": {"tracking": {"gtmId": "asdasdasd", "isLoggingEnabled": true}, "updates": {"isConnectionInfoVisible": false, "isRealtimeUpdatesEnabled": false, "realtimeUpdatesInterval": false}, "pages": {"homePage": "", "contactPage": "", "aboutPage": "", "legalPage": "", "errorPage": ""}, "theming": {"colours": [{"name": "Primary Colours", "data": [{"name": "Primary Colour 1", "scssVarName": "cp1", "value": "#000b3d"}, {"name": "Primary Colour 2", "scssVarName": "cp2", "value": "#ff5f05"}, {"name": "Primary Colour 3", "scssVarName": "cp3", "value": "#001c99"}, {"name": "Primary Colour 4", "scssVarName": "cp4", "value": "#8a8fa6"}]}, {"name": "Secondary Colours", "data": [{"name": "Secondary Colour 1", "scssVarName": "cs1", "value": "#0674e8"}, {"name": "Secondary Colour 2", "scssVarName": "cs2", "value": "#2cde93"}, {"name": "Secondary Colour 3", "scssVarName": "cs3", "value": "#0082ab"}, {"name": "Secondary Colour 4", "scssVarName": "cs4", "value": "#e6f9a9"}, {"name": "Secondary Colour 5", "scssVarName": "cs5", "value": "#54661d"}, {"name": "Secondary Colour 6", "scssVarName": "cs6", "value": "#8ecbd2"}, {"name": "Secondary Colour 7", "scssVarName": "cs7", "value": "#000622"}]}, {"name": "Accent Colours", "data": [{"name": "Accent Colour 1", "scssVarName": "ca1", "value": "#2cde93"}, {"name": "Accent Colour 2", "scssVarName": "ca2", "value": "#00ad23"}, {"name": "Accent Colour 3", "scssVarName": "ca3", "value": "#009980"}, {"name": "Accent Colour 4", "scssVarName": "ca4", "value": "#0674e8"}, {"name": "Accent Colour 5", "scssVarName": "ca5", "value": "#00268b"}, {"name": "Accent Colour 6", "scssVarName": "ca6", "value": "#854dff"}, {"name": "Accent Colour 7", "scssVarName": "ca7", "value": "#d04dff"}, {"name": "Accent Colour 8", "scssVarName": "ca8", "value": "#ff4dd5"}, {"name": "Accent Colour 9", "scssVarName": "ca9", "value": "#ff4d7c"}, {"name": "Accent Colour 10", "scssVarName": "ca10", "value": "#020101"}, {"name": "Accent Colour 11", "scssVarName": "ca11", "value": "#ff5f05"}, {"name": "Accent Colour 12", "scssVarName": "ca12", "value": "#e2222b"}]}, {"name": "Neutral Colours", "data": [{"name": "Black", "scssVarName": "cn1", "value": "#000"}, {"name": "Text - Primary", "scssVarName": "cn2", "value": "#333"}, {"name": "Text - Secondary", "scssVarName": "cn3", "value": "#757575"}, {"name": "Gray - 4", "scssVarName": "cn4", "value": "#c6c6c6"}, {"name": "Gray - 3", "scssVarName": "cn5", "value": "#efefef"}, {"name": "Gray - 2", "scssVarName": "cn6", "value": "#f2f2f2"}, {"name": "Gray - 1", "scssVarName": "cn7", "value": "#f9f9f9"}, {"name": "White", "scssVarName": "cn8", "value": "#fff"}]}, {"name": "Other Colours", "data": [{"name": "Other Colour 1", "scssVarName": "co1", "value": "#0a2c37"}]}], "fonts": [{"name": "Primary Fonts", "data": [{"name": "Primary Font 1", "scssVarName": "pf1", "value": "<PERSON>l, sans-serif"}, {"name": "Primary Font 2", "scssVarName": "pf2", "value": "Helvetica, sans-serif"}]}, {"name": "Secondary Fonts", "data": [{"name": "Secondary Font 1", "scssVarName": "sf1", "value": "Verdana, sans-serif"}, {"name": "Secondary Font 2", "scssVarName": "sf2", "value": "Times New Roman, serif"}]}, {"name": "Accent Fonts", "data": [{"name": "Accent Font 1", "scssVarName": "af1", "value": "Georgia, serif"}, {"name": "Accent Font 2", "scssVarName": "af2", "value": "Courier New, monospace"}]}], "logos": {"favicon": "", "sm": "/", "md": "/", "lg": "/"}, "themes": ["3pS5aewcLV7vgByGxFruke"], "theme": "3pS5aewcLV7vgByGxFruke"}, "scripts": {"header": "", "footer": ""}}}, "routing": {"isEnabled": true, "data": [{"name": "Sample redirect rule 1", "type": "redirect", "source": "/old/url", "destination": "/new/url", "permanent": true, "isEnabled": true}, {"name": "Sam<PERSON> rewrite rule 1", "type": "rewrite", "source": "/old/url", "destination": "/new/url", "isEnabled": true}]}, "domains": {"isEnabled": true, "data": [{"name": "dev", "url": ""}, {"name": "qa", "url": ""}, {"name": "staging", "url": ""}, {"name": "prod", "url": ""}]}, "cacheControl": {"revalidationInterval": 10, "timeUnit": "minutes"}, "experimentation": {"data": []}}