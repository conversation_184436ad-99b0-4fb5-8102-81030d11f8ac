export const EXPERIMENT_TYPE = {
  STATSIG: 'Statsig',
  EVEN_ODD: 'EvenOdd',
} as const

// Status Code
// These constants represent the status codes used in the experimentation process.
export const StatusCode = {
  FAILURE: 0,
  SUCCESS: 1,
}

// Experimentation Option
// These options represent the different types of experimentation that can be performed.
// They are not used in the current implementation but can be used for future enhancements.
export const EXPERIMENTATION_OPTION = [
  {
    value: EXPERIMENT_TYPE.STATSIG,
    title: 'Statsig',
  },
  {
    value: EXPERIMENT_TYPE.EVEN_ODD,
    title: 'Even Odd',
  },
]
// Metrics
// These constants represent the primary and secondary metrics used in the experimentation process.
// They are used to track user engagement and performance over time in Statsig.
// which is not used in the current implementation but can be used for future enhancements.
export const METRICS = {
  primaryMetrics: [
    {
      name: 'wau',
      type: 'user',
    },
    {
      name: 'dau',
      type: 'user',
    },
    {
      name: 'l7',
      type: 'user',
    },
    {
      name: 'weekly_stickiness',
      type: 'user',
    },
    {
      name: 'monthly_stickiness',
      type: 'user',
    },
    {
      name: 'mau_28d',
      type: 'user',
    },
    {
      name: 'new_dau',
      type: 'user',
    },
    {
      name: 'daily_stickiness',
      type: 'user',
    },
    {
      name: 'l28',
      type: 'user',
    },
    {
      name: 'new_wau',
      type: 'user',
    },
  ],
  secondaryMetrics: [
    {
      name: 'wau',
      type: 'user',
    },
    {
      name: 'dau',
      type: 'user',
    },
    {
      name: 'l7',
      type: 'user',
    },
    {
      name: 'weekly_stickiness',
      type: 'user',
    },
    {
      name: 'monthly_stickiness',
      type: 'user',
    },
    {
      name: 'mau_28d',
      type: 'user',
    },
    {
      name: 'new_dau',
      type: 'user',
    },
    {
      name: 'daily_stickiness',
      type: 'user',
    },
    {
      name: 'new_wau',
      type: 'user',
    },
  ],
}

// Experimentation Status
// These constants represent the different statuses of an experiment.
export const pageExperimentationStatus = {
  0: 'Winner',
  1: 'Loser',
  2: 'Inactive',
  3: 'Active',
}
// Publishing Content Flags
// These flags indicate the actions that can be performed on the content related to experiments.
// They are used to control the publishing process of experiments.
export const publishingContentflags = {
  craeteExp: true,
  deleteExp: true,
  completeExp: true,
}

// Experimentation Steps
// These steps represent the different stages in the experimentation process.
export const experimentationSteps = {
  // dahsboard : "Dashboard",
  // detailform : "DetailForm",
  // selectPage : "selectPages",
  // confirmExperiment : "confirmExperiment",
  // finalExperiment : "finalExperiment",
  dahsboard: 0,
  detailform: 1,
  entrySelector: 2,
  confirmExperiment: 3,
  finalExperiment: 4,
}

// Exporting the Contentful Entry Link
// This link is used to access the entries in Contentful for the specified space and environment.
export const EntryLink = `https://app.contentful.com/spaces/${process.env.REACT_APP_CONTENTFUL_SPACE_ID}/environments/${process.env.REACT_APP_CONTENTFUL_ENVIRONMENT}/entries`
