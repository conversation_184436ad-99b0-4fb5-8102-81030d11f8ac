import { ENV_VARIABLES } from "../../../../constant/variables"

export const EXPERIMENT_TYPE = {
  STATSIG: 'Statsig',
  EVEN_ODD: 'EvenOdd',
} as const

export const StatusCode = {
  FAILURE: 0,
  SUCCESS: 1,
}
export const EXPERIMENTATION_OPTION = [
  {
    value: EXPERIMENT_TYPE.STATSIG,
    title: 'Statsig',
  },
  {
    value: EXPERIMENT_TYPE.EVEN_ODD,
    title: 'Even Odd',
  },
]
export const METRICS = {
  primaryMetrics: [
    {
      name: 'wau',
      type: 'user',
    },
    {
      name: 'dau',
      type: 'user',
    },
    {
      name: 'l7',
      type: 'user',
    },
    {
      name: 'weekly_stickiness',
      type: 'user',
    },
    {
      name: 'monthly_stickiness',
      type: 'user',
    },
    {
      name: 'mau_28d',
      type: 'user',
    },
    {
      name: 'new_dau',
      type: 'user',
    },
    {
      name: 'daily_stickiness',
      type: 'user',
    },
    {
      name: 'l28',
      type: 'user',
    },
    {
      name: 'new_wau',
      type: 'user',
    },
  ],
  secondaryMetrics: [
    {
      name: 'wau',
      type: 'user',
    },
    {
      name: 'dau',
      type: 'user',
    },
    {
      name: 'l7',
      type: 'user',
    },
    {
      name: 'weekly_stickiness',
      type: 'user',
    },
    {
      name: 'monthly_stickiness',
      type: 'user',
    },
    {
      name: 'mau_28d',
      type: 'user',
    },
    {
      name: 'new_dau',
      type: 'user',
    },
    {
      name: 'daily_stickiness',
      type: 'user',
    },
    {
      name: 'new_wau',
      type: 'user',
    },
  ],
}

export const pageExperimentationStatus = {
  0: 'Winner',
  1: 'Loser',
  2: 'Inactive',
  3: 'Active',
}

export const publishingContentflags = {
  craeteExp: true,
  deleteExp: true,
  completeExp: true,
}

export const experimentationSteps = {
  // dahsboard : "Dashboard",
  // detailform : "DetailForm",
  // selectPage : "selectPages",
  // confirmExperiment : "confirmExperiment",
  // finalExperiment : "finalExperiment",
  dahsboard: 0,
  detailform: 1,
  entrySelector: 2,
  confirmExperiment: 3,
  finalExperiment: 4,
}

export const EntryLink = `https://app.contentful.com/spaces/${ENV_VARIABLES.contentfulSpaceID}/environments/${ENV_VARIABLES.contentfulEnvironment}/entries`
