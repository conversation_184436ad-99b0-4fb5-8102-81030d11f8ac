import { Notification } from '@contentful/f36-components'
import { uniqueId } from 'lodash'
import moment from 'moment'
import { ENV_VARIABLES } from '../../../../constant/variables'
import { METRICS, StatusCode } from './experiment-const'

/**
 * Experimentation CRUD operations for managing experiments.
 * Provides methods to add, edit, get, delete, and revise experiments.
 * @module ExperimentationCRUD
 * @typedef {Object} ExperimentationData - Represents the data structure for an experiment.
 * @property {string} experimentationId - Unique identifier for the experiment.
 * @property {string} experimentTitle - Title of the experiment.
 * @property {string} experimentDescription - Description of the experiment.
 * @property {boolean} isPublished - Indicates if the experiment is published.
 */
export const ExperimentationCRUD = {
  add: (
    data: ExperimentRootData = {},
    payload: ExperimentationData
  ): ResponseExperimentCRUD<ExperimentRootData> => {
    const { experimentationId } = payload
    const experimentationData = data?.data || []

    const isExperimentExists = experimentationData.find(
      (exp) => exp.experimentationId === experimentationId
    )

    if (isExperimentExists) {
      return { data, status: 0 }
    }
    return {
      data: {
        ...data,
        data: [
          {
            ...payload,
            date: moment().utc().format('YYYY-MM-DD HH:mm:ss [UTC]'),
          },
          ...experimentationData,
        ],
      },
      status: 1, // Delete successful
    }
  },

  edit: (
    data: ExperimentRootData = {},
    experimentationId: string,
    payload: Partial<ExperimentationData>
  ): ResponseExperimentCRUD<ExperimentRootData> => {
    const experimentationData = data?.data || []

    const isExperimentExists = experimentationData.find(
      (exp) => exp.experimentationId === experimentationId
    )

    if (!isExperimentExists) {
      return {
        data,
        status: 0, // Experiment not found
      }
    }

    const updatedData = experimentationData.map((exp) =>
      exp.experimentationId === experimentationId
        ? {
          ...exp,
          ...payload,
          updatedAt: moment().utc().format('YYYY-MM-DD HH:mm:ss [UTC]'),
        }
        : exp
    )
    return {
      data: {
        ...data,
        data: updatedData,
      },
      status: 1, // Delete successful
    }
  },

  getAll: (
    data: ExperimentRootData = {},
    condition?: FilterCondition
  ): ResponseExperimentCRUD<ExperimentationData[]> => {
    const filteredData =
      data?.data?.filter((experiment) => {
        if (!condition) return true // No condition, return all

        return Object.entries(condition).every(([key, value]) => {
          // Handle boolean cases: if value is boolean, ensure default is false
          if (typeof value === 'boolean') {
            return Boolean(experiment[key]) === value
          }

          // Check if the key exists in the experiment and matches the condition
          if (key in experiment) {
            return (
              `${experiment[key]}`?.toLowerCase() === `${value}`?.toLowerCase()
            )
          }

          // Handle nested checks for pages or other structures
          // if (experiment?.experimentationPages) {
          //   return experiment?.experimentationPages.some((page) => page?.[key] === value);
          // }

          return false // Default to no match
        })
      }) || []

    return {
      data: filteredData,
      status: 1,
    }
  },
  getOne: (
    data: ExperimentRootData = {},
    experimentationId: string,
    type: string = 'experimentationId'
  ): ResponseExperimentCRUD<ExperimentationData> => {
    const experiment = data?.data?.find(
      (exp) => exp?.[type]?.toLowerCase() === experimentationId?.toLowerCase()
    )

    return {
      data: experiment || ({} as ExperimentationData),
      status: experiment ? 1 : 0, // 1 if found, 0 if not found
    }
  },

  delete: (
    data: ExperimentRootData = {},
    experimentationId: string
  ): ResponseExperimentCRUD<ExperimentRootData> => {
    const experimentationData = data?.data || []
    const filteredData = experimentationData.map((exp) =>
      exp.experimentationId !== experimentationId
        ? { ...exp }
        : {
          ...exp,
          isDeleted: true,
          isPublished: false,
          deletedAt: moment().utc().format('YYYY-MM-DD HH:mm:ss [UTC]'),
        }
    )
    // const filteredData = experimentationData.filter(
    //   (exp) => exp.experimentationId !== experimentationId
    // )

    return {
      data: {
        ...data,
        data: filteredData,
      },
      status: 1, // Delete successful
    }
  },
  revise: (
    data: ExperimentRootData = {},
    experimentationId: string,
    newVariants: ExperimentationPage[],
    selectedVariantId: string
  ): ResponseExperimentCRUD<ExperimentRootData> => {
    const experimentationData = data?.data || []

    const experiment = experimentationData.find(
      (exp) => exp.experimentationId === experimentationId
    )

    if (!experiment) {
      return {
        data,
        status: StatusCode.FAILURE,
        // message: "Experiment not found",
      }
    }

    // Preserve previous state in history
    const historyEntry: RevisionHistory = {
      revisionId: uniqueId(),
      date: moment().utc().format('YYYY-MM-DD HH:mm:ss [UTC]'),
      previousVariants: [...(experiment?.experimentationPages ?? [])],
      updatedVariant: newVariants?.find(
        (v) => v?.id === selectedVariantId
      ) as ExperimentationPage,
    }

    // Update variants
    const updatedVariants = experiment?.experimentationPages?.map(
      (variant) => ({
        ...variant,
        isActive: variant?.id === selectedVariantId, // Activate only the selected variant
      })
    )
    updatedVariants?.push(
      ...newVariants?.map((variant) => ({
        ...variant,
        isActive: true, // Activate only the selected variant
      }))
    )
    // Update experiment
    const updatedExperiment = {
      ...experiment,
      experimentationPages: updatedVariants,
      revisionHistory: experiment?.revisionHistory
        ? [...experiment.revisionHistory, historyEntry]
        : [historyEntry],
      updatedAt: moment().utc().format('YYYY-MM-DD HH:mm:ss [UTC]'),
    }

    // Replace in the dataset
    const updatedData = experimentationData.map((exp) =>
      exp.experimentationId === experimentationId ? updatedExperiment : exp
    )

    return {
      data: {
        ...data,
        data: updatedData,
      },
      status: StatusCode.SUCCESS,
      // message: "Experiment revised successfully",
    }
  },
}

// Statsig Setup and Functions
export const getStatsigConsoleToken = (): string | null => {
  const token = ENV_VARIABLES.statsigConsoleToken
  // return "console-lrwavKRmcuzuy7kl7hFcSspmgW5HVHTk5N3xFVwVq4R"
  if (!token) {
    console.error(
      'Statsig console token is not defined in environment variables.'
    )
    return null
  }

  return token
}

// Function to create a Statsig experiment
export const createStatsigExperiment = async (
  experimentData: StatsigExperimentData
): Promise<any> => {
  const token = getStatsigConsoleToken()

  try {
    if (!token) {
      throw new Error('Statsig console token is missing.')
    }
    const response = await fetch(
      'https://statsigapi.net/console/v1/experiments',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          'STATSIG-API-KEY': `${token}`,
        },
        // mode: 'no-cors',
        body: JSON.stringify(experimentData),
      }
    )

    if (!response.ok) {
      throw new Error(`Failed to create experiment: ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    Notification.error(`${error}`, {
      duration: 2000,
      title: 'Error while creating experiment in statsig.',
    })
    console.error('Error creating Statsig experiment:', error)
    // throw error;
  }
}

// Function to convert experimentation data to Statsig experiment format
export const convertToStatsigExperimentData = (
  inputData: Partial<ExperimentationData>
): StatsigExperimentData => {
  const groups: StatsigExperimentGroup[] =
    inputData?.experimentationPages?.map((page, index) => ({
      name: `Experiment Group ${index + 1}`,
      size: 100 / (inputData?.experimentationPages?.length ?? 1),
      parameterValues: {
        page: page.slug,
      },
    })) ?? []

  const experimentData: StatsigExperimentData = {
    name: `${inputData?.experimentTitle?.replace(
      /[^a-zA-Z0-9_-]/g,
      ''
    )} ${new Date()?.toISOString()?.replace(/[^a-zA-Z0-9_-]/g, '')}`,
    hypothesis: inputData.experimentDescription,
    description: inputData.experimentDescription,
    type: 'a_b_test',
    groups: groups,
    ...METRICS,
  }

  return experimentData
}

// Function to check for duplicate slugs in a list of page entries
export function hasDuplicateSlugs(entries: PageEntry[]): boolean {
  const slugSet = new Set<string>()

  for (const entry of entries) {
    if (slugSet?.has(entry?.slug?.trim()?.toLowerCase())) {
      return true // Duplicate found
    }

    slugSet?.add(entry?.slug?.trim()?.toLowerCase())
  }

  return false // No duplicates
}
