import { Box, Button } from '@contentful/f36-components'
import React from 'react'
import CustomCard from '../../Notifications/CustomCard'

/**
 * ExperimentationEntrySelector Component
 * This component is used to select and display entries in a carousel format.
 * It allows users to link existing entries or add new ones to the carousel.
 */
const ExperimentationEntrySelector = ({
  carouselSelector,
  carouselData,
  carouselAddMore,
  setCarouselData,
}: {
  carouselSelector: Function
  carouselAddMore: Function
  carouselData: (any | null)[]
  setCarouselData: Function
}) => {
  return (
    <Box
      className='overflow-y-auto w-full '
      style={{
        height: '96%',
      }}
    >
      <Box className='w-full flex justify-center items-center flex-col px-2  gap-5'>
        <Box
          style={{
            width: '100%',
            display: 'flex',
            gap: '10px',
            height: 'auto',
            // flexWrap: 'wrap',
            alignContent: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            // padding: '15px 20px 15px 20px',
            // alignItems:"center"
          }}
        >
          {carouselData?.findIndex((el) => el == null) !== -1 ? null : (
            <Box
              // key={i}
              style={{
                width: 'auto',
                height: 'auto',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Button
                onClick={() => carouselAddMore()}
                variant='primary'
                size='small'
              >
                Add More Page
              </Button>
            </Box>
          )}
          {carouselData?.map((el, i) => {
            return (
              <Box
                key={i}
                style={{
                  height: 'auto',
                  width: 'auto',
                  display: 'flex',
                  alignItems: 'start',
                  justifyContent: 'start',
                }}
              >
                {el === null ? (
                  <Button
                    key={i}
                    onClick={() => {
                      carouselSelector(i)
                    }}
                    variant='secondary'
                    size='small'
                  >
                    Link existing entry
                  </Button>
                ) : (
                  <CustomCard
                    data={el?.selected}
                    onRemoveEntry={() => {
                      setCarouselData((prev: any[]) => {
                        const prevData = [...prev]
                        prevData[i] = null
                        const newData = [...prevData]?.filter((el) => el) ?? []
                        newData?.unshift(null)
                        return [...newData]
                      })
                    }}
                    field={'formFloating'}
                  />
                )}
              </Box>
            )
          })}
        </Box>
      </Box>
    </Box>
  )
}

export default ExperimentationEntrySelector
