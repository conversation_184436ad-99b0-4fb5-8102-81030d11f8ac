import { Box, ModalControls } from '@contentful/f36-components'
import React from 'react'
import { BsExclamationTriangle } from 'react-icons/bs'
import LeftArrow from '../../../../../assets/icons/LeftArrow'
import RightArrow from '../../../../../assets/icons/RightArrow'
import { generateSlugWithTimestamp } from '../../../../../globals/experimentation-util'
import { updateEntryData } from '../../../../../globals/utils'
import { Button, notification, Popconfirm } from '../../../../atoms'
import { domainsConfig } from '../../../../Crosspost/utils'
import {
  experimentationSteps,
  publishingContentflags,
} from '../experiment-const'
import {
  convertToStatsigExperimentData,
  createStatsigExperiment,
  ExperimentationCRUD,
  hasDuplicateSlugs,
} from '../experimentation-helper'
import { handleUrlClick } from './ExperimentationCreateViewConfirm'

const ExperimentationModalControlsSection = ({
  activeStep,
  setActiveStep,
  isLoading,
  setIsLoading,
  data,
  setLoadingText,
  carouselData,
  selectedMetaDataIndex,
  setCarouselData,
  SaveConfigurationandFetchData,
  setFinalData,
  setSelectedMetaDataIndex,
  setPayload,
  currentDomain,
  payload,
  refetchData,
}: {
  activeStep: number
  setActiveStep: Function
  isLoading?: boolean
  setIsLoading: Function
  data: ExperimentRootData
  setLoadingText: Function
  selectedMetaDataIndex?: string | null
  carouselData: (any | null)[]
  setCarouselData: Function
  SaveConfigurationandFetchData: Function
  setFinalData: Function
  setPayload: Function
  setSelectedMetaDataIndex: Function
  currentDomain: string
  payload: any
  refetchData: Function
}) => {
  const handelCreateExperimentation = async () => {
    const timestamp = `${Date.now()}` // current timestamp
    if (selectedMetaDataIndex === null) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            Select the variant for refrence of title , description , keywords
            and SEO related things.
          </p>
        ),
      })
      return
    }
    setIsLoading(true)
    setActiveStep(experimentationSteps.finalExperiment)
    const experimentationPages = carouselData
      ?.filter((el) => el)
      ?.map((el) => ({
        title: el?.selected?.fields?.title?.['en-CA'],
        slug: generateSlugWithTimestamp(
          el?.selected?.fields?.slug?.['en-CA'] ?? 'page-slug',
          payload?.experimentTitle ?? '',
          timestamp
        ),

        internalName: el?.selected?.fields?.internalName?.['en-CA'],
        link: handleUrlClick(el?.selected),
        id: el?.selected?.sys?.id,
        status: el?.pageThumbnail?.status,
        url: el?.pageThumbnail?.url,
      }))
    const newPayload: Partial<ExperimentationData> = {
      ...payload,
      // date: new Date().toUTCString(),
      experimentationPages,
    }
    const groupPayload = convertToStatsigExperimentData(newPayload)
    setLoadingText('Started Experiment Creation on Statsig')
    const statsigExpCreate = await createStatsigExperiment(groupPayload)
    setLoadingText('Completed Experiment Creation on Statsig')
    if (!statsigExpCreate?.data?.id) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            Something went wrong
          </p>
        ),
      })
      return
    }
    newPayload.experimentationId = statsigExpCreate.data.id

    //history count preservation
    const historyCount = () => {
      const dataOfExp = ExperimentationCRUD.getAll(data, {
        masterPage: newPayload?.masterPage?.toLowerCase(),
      })
      if (
        !dataOfExp?.data ||
        !Array.isArray(dataOfExp.data) ||
        !dataOfExp.data.length
      ) {
        return 0
      }
      return (
        dataOfExp?.data
          ?.map(({ experimentationPages = [] }) => experimentationPages)
          ?.flat(2)?.length ?? 0
      )
    }
    newPayload.historyCount = historyCount()
    const response = ExperimentationCRUD.add(
      data,
      newPayload as ExperimentationData
    )

    if (response?.status && selectedMetaDataIndex !== null) {
      const availableLocales = domainsConfig?.find(
        (el) => el?.domainKey === currentDomain
      )?.locales || ['en-CA'] // Default to 'en-CA' if domain not found

      const pageToMetaData = carouselData?.find(
        (en) => en?.selected?.sys?.id === selectedMetaDataIndex
      )?.selected?.fields

      // Helper function to map fields for multiple locales
      const mapLocales = (fieldKey: string, defaultValue: any) => {
        return availableLocales.reduce((acc, locale) => {
          acc[locale] = pageToMetaData?.[fieldKey]?.[locale] || defaultValue // Ensure it doesn't break if undefined
          return acc
        }, {} as Record<string, string>)
      }
      const payloadForVariant = {
        isExperimentation: { 'en-CA': true },
        noFollow: { 'en-CA': true },
        noIndex: { 'en-CA': true },
        hideFromAlgolia: { 'en-CA': true },
        experimentationId: { 'en-CA': statsigExpCreate?.data?.id },
        title: mapLocales('title', ''),
        shortTitle: mapLocales('shortTitle', ''),
        afsDescription: mapLocales('afsDescription', ''),
        seoTitle: mapLocales('seoTitle', ''),
        seoDescription: mapLocales('seoDescription', ''),
        seoKeywords: mapLocales('seoKeywords', []),
        pageThumbnail: mapLocales('pageThumbnail', null),
      }
      const updatePagesPromises = experimentationPages.map((pageToUpdate) => {
        const updatedSlugPerLocale = availableLocales.reduce((acc, locale) => {
          const existingSlug =
            pageToUpdate?.slug ||
            generateSlugWithTimestamp(
              'page-slug',
              payload?.experimentTitle ?? '',
              timestamp
            )
          acc[locale] = existingSlug
          return acc
        }, {} as Record<string, string>)
        return updateEntryData(
          pageToUpdate.id,
          {
            ...payloadForVariant,
            slug: updatedSlugPerLocale,
          },
          publishingContentflags?.craeteExp
        )
      })
      setLoadingText('Started Setting up pages for Experiment')
      await Promise.all(updatePagesPromises)
      setLoadingText('Started Experiment Configration in Contentfull')

      await SaveConfigurationandFetchData(response?.data)
      setLoadingText('')
      refetchData()
      setFinalData({
        ...newPayload,
        statsigData: statsigExpCreate?.data,
      })
      setPayload({
        masterPage: '',
        experimentDescription: '',
        experimentTitle: '',
        // experimentationId: '',
        // end_time: undefined,
        // start_time: undefined,
      })
      setSelectedMetaDataIndex(null)
      setCarouselData([null, null])

      setIsLoading(false)
    }
  }
  const checkFirstStepDisable = () => {
    if (
      // !payload?.experimentationId ||
      !payload?.experimentDescription ||
      !payload?.experimentTitle ||
      !payload?.masterPage
      // !payload?.start_time ||
      // !payload?.end_time
    ) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            Please fill in all required fields.
          </p>
        ),
      })

      return false
    }

    const isExperiment = ExperimentationCRUD.getAll(data, {
      isDeleted: false,
      masterPage: payload?.masterPage?.toLowerCase(),
    })

    if (isExperiment?.status && isExperiment?.data?.length) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            An experiment is already running on this routing URL.
          </p>
        ),
      })

      return false
    }

    return true
  }
  const checkEntrySelectionStepDisable = () => {
    const hasDuplicatedSlugs = hasDuplicateSlugs(
      carouselData
        ?.map((el) => ({
          slug: el?.selected?.fields?.slug?.['en-CA']?.toLowerCase(),
        }))
        ?.filter((el) => el?.slug)
    )
    if (hasDuplicatedSlugs) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            Pages have the same slug. Please change the slugs and re-fetch the
            entries.
          </p>
        ),
      })

      return false
    }

    if (
      // !payload?.experimentationId ||
      !payload?.experimentDescription ||
      !payload?.experimentTitle ||
      !payload?.masterPage
      // !payload?.start_time ||
      // !payload?.end_time
    ) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            Please fill in all required fields in Detail Tab.
          </p>
        ),
      })

      return false
    }
    const hasMasterPageSlugsame = hasDuplicateSlugs(
      [
        ...carouselData?.map((el) => ({
          slug: el?.selected?.fields?.slug?.['en-CA']?.toLowerCase(),
        })),
        { slug: payload?.masterPage?.toLowerCase() },
      ]?.filter((el) => el?.slug)
    )
    // if (hasMasterPageSlugsame) {
    //   notification.error({
    //     message: (
    //       <p
    //         className={'fSansBld'}
    //         style={{ lineHeight: 1, marginBottom: '15px' }}
    //       >
    //         The routing URL and page variants cannot have same slug
    //       </p>
    //     ),
    //     description: 'update either the rounting URL or page variant slug',
    //   })
    //   return false
    // }

    if (carouselData?.filter((el) => el)?.length < 2) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            Please select at least two pages for experimentation.
          </p>
        ),
      })

      return false
    }
    return true
  }
  return (
    <Box
      style={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'end',
        alignItems: 'center',
      }}
    >
      <hr className='endHorizontalLine mb-3' style={{ width: '100%' }} />
      <ModalControls style={{ paddingRight: '1rem', paddingBottom: '1rem' }}>
        {activeStep === experimentationSteps.detailform ? (
          <>
            <Button
              type='primary'
              className={'bg-transparent text-neutral-950'}
              onClick={() => {
                if (checkFirstStepDisable()) {
                  setActiveStep(experimentationSteps?.entrySelector)
                } else {
                  return
                }
              }}
            >
              Next <RightArrow />
            </Button>
          </>
        ) : activeStep === experimentationSteps.entrySelector ? (
          <>
            <Button
              type='primary'
              className={'bg-transparent text-neutral-950'}
              onClick={() => setActiveStep(experimentationSteps.detailform)}
              disabled={isLoading}
            >
              <LeftArrow /> Back
            </Button>
            <Button
              type='primary'
              className={'bg-transparent text-neutral-950'}
              onClick={() => {
                if (checkEntrySelectionStepDisable()) {
                  setActiveStep(experimentationSteps?.confirmExperiment)
                } else {
                  return
                }
              }}
            >
              Next <RightArrow />
            </Button>
          </>
        ) : activeStep === experimentationSteps.confirmExperiment ? (
          <>
            <Button
              type='primary'
              className={'bg-transparent text-neutral-950'}
              onClick={() => setActiveStep(experimentationSteps.entrySelector)}
              disabled={isLoading}
            >
              <LeftArrow /> Back
            </Button>

            <Popconfirm
              title={
                <b style={{ fontSize: '13px' }}>
                  Once you create an experimentation, it can't be undone.
                </b>
              }
              okText='Yes'
              okButtonProps={{
                type: 'primary',
                className: `bg-neutral-950`,
              }}
              styles={{
                body: {
                  padding: '15px',
                },
              }}
              align={{}}
              placement='topLeft'
              icon={<BsExclamationTriangle size={20} className='mr-2  bg-ac' />}
              cancelText='No'
              cancelButtonProps={{
                type: 'default',
                //className: 'bg-neutral-950',
              }}
              onConfirm={handelCreateExperimentation}
            >
              <Button
                type='primary'
                className={'bg-neutral-950 text-white'}
                loading={isLoading}
                disabled={isLoading}
              >
                Save <RightArrow colour='#fff' />
              </Button>
              {/* <SaveButton
                helpText='Save'
                isDisabled={isLoading}
                isLoading={isLoading}
                btnText={'Save'}g
                onClick={() => {}}
                // onClick={() => setShowConfirmBox(true)}
              /> */}
            </Popconfirm>
          </>
        ) : (
          <>
            <Button
              type='primary'
              className={'bg-transparent text-neutral-950'}
              onClick={() => setActiveStep(experimentationSteps.dahsboard)}
              disabled={isLoading}
            >
              View all experiments
            </Button>
          </>
        )}
      </ModalControls>
    </Box>
  )
}

export default ExperimentationModalControlsSection
