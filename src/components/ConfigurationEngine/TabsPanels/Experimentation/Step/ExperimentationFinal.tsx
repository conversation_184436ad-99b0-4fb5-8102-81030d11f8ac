import { Box } from '@contentful/f36-components'
import React from 'react'
import { AiFillInfoCircle } from 'react-icons/ai'
import { ENV_VARIABLES } from '../../../../../constant/variables'
import { Alert, Table } from '../../../../atoms'
import { EntryLink } from '../experiment-const'

/**
 * ExperimentationFinal Component
 * This component displays the final step of the experimentation process.
 * It shows the experiment title, description, routing URL, and a table of experimentation pages.
 * If the data is still loading, it displays a loading message.
 * @param payload - The payload containing the experiment data.
 * @param isLoading - A boolean indicating if the data is currently loading.
 * @param loadingText - The text to display while loading.
 * @param ActiveDomain - The active domain for the experiment.
 * @returns
 */
const ExperimentationFinal = ({
  payload,
  isLoading,
  // setIsLoading,
  loadingText,
  ActiveDomain,
}: // ...props
{
  payload: Partial<ExperimentationData>
  isLoading: boolean
  // setIsLoading : Function
  loadingText: string
  ActiveDomain: string
}) => {
  return isLoading ? (
    <Box
      className='summaryRoot formRoot flex justify-center items-center flex-col'
      style={{ width: '100%' }}
    >
      {' '}
      <p className=' font-thin text-gray-500 '>
        Wait while We create your Experiment{' '}
      </p>
      <br></br>
      <p className='animate-pulse font-semibold '>{`${loadingText}`}</p>
    </Box>
  ) : (
    <Box
      className='summaryRoot formRoot'
      style={{ width: '100%', height: '96%' }}
    >
      <Alert
        icon={
          <AiFillInfoCircle className='!bg-accent9-100 !text-accent9-800' />
        }
        showIcon
        className='!bg-accent9-100 !text-accent9-800'
        message='Do not close this browser while activating your experiment in Statsig'
        // type='warning'
      />
      <div className='pr-5 mb-5 w-full'>
        <h3 className='text-xl font-semibold flex items-center gap-4 justify-between'>
          {payload?.experimentTitle}
        </h3>
        <p className='text-gray-500 mt-2'>{payload?.experimentDescription}</p>
        <div className='text-sm text-gray-400 mt-4'>
          <p className='font-medium text-gray-700'>
            <b>Routing URL : </b>{' '}
            <a
              target='_blank'
              rel='noopener noreferrer'
              href={`${ActiveDomain}${payload?.masterPage}`}
              className='font-medium text-gray-700'
            >
              {payload?.masterPage}{' '}
            </a>
          </p>
        </div>
      </div>
      <hr />
      <div className='my-5'>
        <p>You have successfully created the experiment within Contentful. </p>
        <ol className='list-decimal list-inside space-y-4'>
          <li className='text-gray-800'>
            Set the traffic split to support your experiment (e.g., 50/50 split)
          </li>
          <li className='text-gray-800'>
            <span>Please visit the </span>
            <a
              href={`https://console.statsig.com/${ENV_VARIABLES?.statsigConsoleId}/experiments/${payload?.experimentationId}/setup`}
              target='_blank'
              rel='noopener noreferrer'
              className='text-blue-600 hover:text-blue-400 underline'
            >
              {`https://console.statsig.com/${ENV_VARIABLES?.statsigConsoleId}/experiments/${payload?.experimentationId}/setup`}
            </a>
            <span> and activate your experiment.</span>
          </li>

          <li className='text-gray-800'>
            Once you have activated your experiment, here are the details:
          </li>
        </ol>
      </div>
      <Table
        dataSource={payload?.experimentationPages}
        pagination={false}
        scroll={{ x: '800' }}
        locale={{ emptyText: 'No data For Page To View' }}
        columns={[
          {
            title: 'Title',
            dataIndex: 'internalName',
            key: 'internalName',
            // sorter: (a: ExperimentationPage, b: ExperimentationPage) =>
            //   sortValues(a, b, 'internalName'),
            width: 150,
            render: (link: string, row) =>
              row?.id ? (
                <a
                  href={`${EntryLink}/${row?.id}`}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='hover:text-blue-400 flex justify-between items-center'
                >
                  {`${row?.internalName ?? 'N/A'}`}
                </a>
              ) : (
                'N/A'
              ),
          },
          // {
          //   title: 'Slug',
          //   dataIndex: 'slug',
          //   key: 'slug',
          //   sorter: (a: ExperimentationPage, b: ExperimentationPage) =>
          //     sortValues(a, b, 'slug'),
          //   width: 150,
          // },
          // {
          //   title: 'Internal Name',
          //   dataIndex: 'internalName',
          //   key: 'internalName',
          //   sorter: (a: ExperimentationPage, b: ExperimentationPage) =>
          //     sortValues(a, b, 'internalName'),
          //   width: 250,
          // },
          // {
          //   title: 'Internal URL',
          //   dataIndex: 'link',
          //   key: 'link',
          //   render: (link: string, row) => (
          //     <a
          //       // href={row?.slug}
          //       href={`${ActiveDomain}${row?.slug}`}
          //       target='_blank'
          //       rel='noopener noreferrer'
          //       className='hover:text-blue-400 flex justify-between items-center'
          //     >
          //       {`${row?.slug}`}
          //     </a>
          //   ),
          //   width: 200,
          // },
          {
            title: 'External URL',
            dataIndex: 'slug',
            key: 'slug',
            width: 250,
            render: (_, __, index) => {
              return (
                <a
                  href={`${ActiveDomain}${payload?.masterPage}?variant=${
                    (payload?.historyCount ?? 0) + index
                  }`}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='hover:text-blue-400 flex justify-between items-center'
                >
                  {`${payload?.masterPage}?variant=${
                    (payload?.historyCount ?? 0) + index
                  }`}
                </a>
              )
            },
          },
          // {
          //   title: 'Contentful Entry',
          //   dataIndex: 'link',
          //   key: 'link',
          //   render: (link: string) => (
          //     <a
          //       href={link}
          //       target='_blank'
          //       rel='noopener noreferrer'
          //       className='hover:text-blue-400'
          //     >
          //       View Entry
          //
          //     </a>
          //   ),
          //   width: 200,
          // },
        ]}
      />
    </Box>
  )
}

export default ExperimentationFinal
