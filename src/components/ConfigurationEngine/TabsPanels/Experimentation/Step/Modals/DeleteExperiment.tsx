import { Checkbox } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { GrClose } from 'react-icons/gr'
import Info from '../../../../../../assets/icons/Info'
import { cleanSlug } from '../../../../../../globals/experimentation-util'
import {
  unpublishEntry,
  updateEntryData,
} from '../../../../../../globals/utils'
import {
  Button,
  Modal,
  notification,
  Table,
  Tooltip,
} from '../../../../../atoms'
import { EntryLink, publishingContentflags } from '../../experiment-const'
import { ExperimentationCRUD } from '../../experimentation-helper'
/**
 *
 * @param data - The root data for the experiment.
 * @param experimentData - The data for the specific experiment.
 * @param ActiveDomain - The active domain for the experiment.
 * @param isLoading - A boolean indicating if the data is currently loading.
 * @param setIsLoading - A function to set the loading state.
 * @param SaveConfigurationandFetchData - A function to save the configuration and fetch updated data.
 */
const DeleteExperiment = ({
  data,
  experimentData,
  ActiveDomain,
  isLoading,
  setIsLoading,
  SaveConfigurationandFetchData,
}: {
  data: ExperimentRootData
  experimentData: ExperimentationData
  ActiveDomain?: string
  isLoading?: boolean
  setIsLoading: Function
  SaveConfigurationandFetchData: Function
}) => {
  // const [needToDelete, setNeedtoDelete] = useState<boolean>(false)
  const [openDeleteExperiment, setExperimentDelete] = useState<boolean>(false)
  const [unpublishPage, setUnpublishPage] = useState<string[]>([])

  useEffect(() => {
    if (experimentData?.experimentationPages && !isLoading) {
      const defaultSelected = experimentData.experimentationPages.map(
        (el) => el?.id
      )
      setUnpublishPage(defaultSelected)
    }
  }, [experimentData, openDeleteExperiment, isLoading])
  const handelDeleteExperiment = async () => {
    if (experimentData) {
      setIsLoading(true)
      const res = ExperimentationCRUD.delete(
        data,
        experimentData?.experimentationId
      )
      if (res?.status) {
        try {
          const pagesToDelete = experimentData?.experimentationPages?.filter(
            (page) => unpublishPage?.some((pageId) => pageId === page?.id) // Only include pages that are in the unpublishPage array
          )
          const updatePagesPromises =
            experimentData?.experimentationPages?.map((pagetoupdate) =>
              updateEntryData(
                pagetoupdate?.id,
                {
                  isExperimentation: {
                    'en-CA': false,
                  },
                  experimentationId: {
                    'en-CA': '',
                  },
                  slug: {
                    'en-CA': cleanSlug(pagetoupdate?.slug),
                    'fr-CA': cleanSlug(pagetoupdate?.slug),
                  },
                },
                publishingContentflags?.deleteExp
              )
            ) ?? []
          await Promise.all(updatePagesPromises)

          const archievOrDeletePagesPromises =
            pagesToDelete?.map((pagetoupdate) =>
              unpublishEntry(pagetoupdate?.id)
            ) ?? []
          await Promise.all(archievOrDeletePagesPromises)
          await SaveConfigurationandFetchData(res?.data)
          notification.success({
            message: (
              <p
                className={'fSansBld'}
                style={{ lineHeight: 1, marginBottom: '15px' }}
              >
                {`${experimentData?.experimentTitle} experiment has now been deleted`}
              </p>
            ),
            description: 'Do not forget to push your changes to live.',
          })

          setIsLoading(false)
          handleClose()
        } catch (error) {
          setIsLoading(false)
          notification.error({
            message: (
              <p
                className={'fSansBld'}
                style={{ lineHeight: 1, marginBottom: '15px' }}
              >
                Something went wrong
              </p>
            ),
            description: `${JSON.stringify(error)}`,
          })
        }
      }
    }
  }
  const handleClose = () => {
    setExperimentDelete(false)
    // setNeedtoDelete(false)
  }
  const handleCheckboxChange = (id: string, isChecked: boolean) => {
    setUnpublishPage((prev) =>
      isChecked ? [...prev, id] : prev.filter((pageId) => pageId !== id)
    )
  }
  return (
    <>
      <Modal
        open={openDeleteExperiment}
        onCancel={handleClose}
        onClose={handleClose}
        destroyOnClose
        closable={!isLoading}
        maskClosable={!isLoading}
        cancelButtonProps={{
          disabled: isLoading,
        }}
        okButtonProps={{
          disabled: isLoading,
          loading: isLoading,
          className: 'bg-neutral-950 text-white',
        }}
        onOk={handelDeleteExperiment}
        okText='Confirm'
        centered
        title={
          <div className='pr-5'>
            <h3 className='text-xl font-semibold flex items-center gap-4 justify-between'>
              Delete experiment "{experimentData?.experimentTitle}"
            </h3>
            <p className='text-gray-500 mt-2'>
              {experimentData?.experimentDescription}
            </p>
            <div className='text-sm text-gray-400 mt-4'>
              Routing URL:{' '}
              <a
                target='_blank'
                rel='noopener noreferrer'
                href={`${ActiveDomain}${experimentData?.masterPage}`}
                className='font-medium text-gray-700'
              >
                {experimentData?.masterPage}{' '}
              </a>
            </div>
          </div>
        }
        className='w-full '
      >
        <Table
          dataSource={experimentData?.experimentationPages}
          pagination={false}
          scroll={{ x: 'auto' }}
          locale={{ emptyText: 'No data For Page To View' }}
          columns={[
            {
              title: 'Title',
              dataIndex: 'internalName',
              key: 'internalName',
              width: 150,
              render: (link: string, row) =>
                row?.id ? (
                  <a
                    href={`${EntryLink}/${row?.id}`}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='hover:text-blue-400 flex justify-between items-center'
                  >
                    {`${row?.internalName ?? 'N/A'}`}
                  </a>
                ) : (
                  'N/A'
                ),
            },
            // {
            //   title: 'Internal URL',
            //   dataIndex: 'link',
            //   key: 'link',
            //   width: 200,
            //   render: (_, row) => {
            //     const url = `${ActiveDomain}${row?.slug}`
            //     return (
            //       <a
            //         href={url}
            //         target='_blank'
            //         rel='noopener noreferrer'
            //         className='hover:text-blue-400 flex justify-between items-center'
            //       >
            //         {row?.slug}
            //       </a>
            //     )
            //   },
            // },
            {
              title: 'External URL',
              dataIndex: 'slug',
              key: 'slug',
              width: 200,
              render: (_, __, index) => {
                const variant = (experimentData?.historyCount ?? 0) + index
                const url = `${ActiveDomain}${experimentData?.masterPage}?variant=${variant}`

                return (
                  <a
                    href={url}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='hover:text-blue-400 flex justify-between items-center'
                  >
                    {`${experimentData?.masterPage}?variant=${variant}`}
                  </a>
                )
              },
            },
            {
              title: (
                <Tooltip title={'Select the variants that will be Unpublish'}>
                  <p className='flex gap-3 justify-center items-center'>
                    Unpublish
                    <Info />
                  </p>
                </Tooltip>
              ),
              dataIndex: 'id',
              key: 'id',

              render: (id: string) => (
                <div className='flex items-center justify-center'>
                  <Checkbox
                    isChecked={unpublishPage?.some((pageId) => pageId === id)}
                    onChange={(e) => handleCheckboxChange(id, e.target.checked)}
                  />
                </div>
              ),
              width: 100,
            },
          ]}
        />
      </Modal>
      {/* </ModalConfirm> */}
      <Tooltip
        title={
          experimentData?.isDeleted
            ? experimentData?.shipedPageId
              ? 'This experiment is already completed.'
              : 'This experiment was deleted.'
            : 'Delete Experiment'
        }
      >
        <Button
          disabled={experimentData?.isDeleted}
          onClick={() => {
            setExperimentDelete(true)
          }}
          type='primary'
          className={'text-neutral-950 bg-transparent border-none'}
        >
          <GrClose size={'20'} />
          {/* Delete Document */}
        </Button>
      </Tooltip>
      {/* <CustomButton
        variant='negative'
        tooltipText={
          experimentData?.isDeleted
            ? experimentData?.shipedPageId
              ? 'This experiment is already completed.'
              : 'This experiment was deleted.'
            : 'Delete Experiment'
        }
        tooltipPlacement='top'
        size='small'
        startIcon={<RiDeleteBin2Line size={18} />}
        onClick={() => {
          setExperimentDelete(true)
        }}
        isDisabled={experimentData?.isDeleted}
      /> */}
    </>
  )
}

export default DeleteExperiment
