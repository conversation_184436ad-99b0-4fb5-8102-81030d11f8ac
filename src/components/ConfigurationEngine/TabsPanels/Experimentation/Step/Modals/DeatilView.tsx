import React, { useState } from 'react'
import { CiCircleInfo } from 'react-icons/ci'
import { sortValues } from '../../../../../../globals/utils'
import { Button, Modal, Table, Tooltip } from '../../../../../atoms'
import { EntryLink } from '../../experiment-const'
import { Tag } from '../ExperimentationHome'

/**
 *
 * @param experimentData - The data for the experiment to be viewed.
 * @param ActiveDomain - The active domain for the experiment.
 * This component renders a modal view of the experiment details.
 * It displays the experiment title, description, routing URL, and a table of experimentation pages.
 * The modal can be opened by clicking the "View" button, and it can be closed by clicking the close button or outside the modal area.
 * @returns
 */
const DeatilView = ({
  experimentData,
  ActiveDomain,
}: {
  experimentData: ExperimentationData
  ActiveDomain?: string
}) => {
  const [experimentDataview, setExperimentData] = useState<boolean>(false)
  const handleClose = () => {
    setExperimentData(false)
  }

  return (
    <>
      <Modal
        open={experimentDataview}
        onCancel={handleClose}
        onClose={handleClose}
        footer={null}
        centered
        title={
          <div className='pr-6 -mt-1'>
            <h3 className='text-xl font-semibold flex items-center gap-4 justify-between'>
              {experimentData?.experimentTitle}
              <Tag
                status={
                  experimentData?.statusOfExperiment

                  // experimentData?.isShiped
                  //   ? 'Done'
                  //   : experimentData?.isDeleted
                  //   ? 'Deleted'
                  //   : 'InProgress'
                }
                isPublished={experimentData?.isPublished}
              />
            </h3>
            <p className='text-gray-500 mt-2'>
              {experimentData?.experimentDescription}
            </p>
            <div className='text-sm text-gray-400 mt-4'>
              Routing URL:{' '}
              <a
                target='_blank'
                rel='noopener noreferrer'
                href={`${ActiveDomain}${experimentData?.masterPage}`}
                className='font-medium text-gray-700'
              >
                {experimentData?.masterPage}{' '}
                {/* <RxExternalLink className='inline ml-1 w-6 h-6' /> */}
              </a>
            </div>
          </div>
        }
        className='w-full '
      >
        <Table
          dataSource={experimentData?.experimentationPages}
          pagination={{
            // showSizeChanger: true,
            // pageSizeOptions: [10, 20, 50, 100],
            defaultPageSize: 20,
            // showQuickJumper: true,
            // showPrevNextJumpers: true,
          }}
          scroll={{ x: 'auto' }}
          locale={{ emptyText: 'No data For Page To View' }}
          columns={[
            // {
            //   title: 'Title',
            //   dataIndex: 'title',
            //   key: 'title',
            //   sorter: (a: ExperimentationPage, b: ExperimentationPage) =>
            //     sortValues(a, b, 'title'),
            //   width: 150,
            // },
            {
              title: 'Title',
              dataIndex: 'internalName',
              key: 'internalName',
              sorter: (a: ExperimentationPage, b: ExperimentationPage) =>
                sortValues(a, b, 'internalName'),
              width: 250,
              render: (link: string, row) =>
                row?.id ? (
                  <a
                    href={`${EntryLink}/${row?.id}`}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='hover:text-blue-400 flex justify-between items-center'
                  >
                    {`${row?.internalName ?? 'N/A'}`}
                  </a>
                ) : (
                  'N/A'
                ),
            },
            // {
            //   title: 'Slug',
            //   dataIndex: 'slug',
            //   key: 'slug',
            //   sorter: (a: ExperimentationPage, b: ExperimentationPage) =>
            //     sortValues(a, b, 'slug'),
            //   width: 150,
            // },

            // {
            //   title: 'Page Status',
            //   dataIndex: 'PageStatusExpWise',
            //   key: 'PageStatusExpWise',
            //   sorter: (a: ExperimentationPage, b: ExperimentationPage) =>
            //     sortValues(a, b, 'PageStatusExpWise'),
            //   filters: [
            //     { text: 'Active', value: 'active' },
            //     { text: 'Inactive', value: 'inactive' },
            //   ],
            //   onFilter: (value: string, record: ExperimentationPage) =>
            //     record.status === value,
            //   width: 200,
            // },
            // {
            //   title: 'Contentful Entry',
            //   dataIndex: 'link',
            //   key: 'link',
            //   render: (link: string) => (
            //     <a
            //       href={link}
            //       target='_blank'
            //       rel='noopener noreferrer'
            //       className='hover:text-blue-400'
            //     >
            //       View Entry
            //       <RxExternalLink className='inline ml-1 w-6 h-6' />
            //     </a>
            //   ),
            //   width: 200,
            // },
            // {
            //   title: 'Status',
            //   dataIndex: 'status',
            //   key: 'status',
            //   render: (status: string) => (
            //     <span
            //       className={
            //         status === 'active' ? 'text-green-500' : 'text-red-500'
            //       }
            //     >
            //       {status || 'N/A'}
            //     </span>
            //   ),
            //   width: 150,
            // },
            // {
            //   title: 'Active',
            //   dataIndex: 'isActive',
            //   key: 'isActive',
            //   render: (isActive: boolean) => (
            //     <span className={isActive ? 'text-green-500' : 'text-red-500'}>
            //       {isActive ? 'Yes' : 'No'}
            //     </span>
            //   ),
            //   width: 100,
            // },
            // {
            //   title: 'Internal URL',
            //   dataIndex: 'link',
            //   key: 'link',
            //   width: 200,
            //   render: (link: string, row) => {
            //     const { shipedPageId } = experimentData || {}
            //     const slug = row?.slug
            //     const isShippedPage = shipedPageId && row?.id === shipedPageId

            //     return isShippedPage ? (
            //       slug
            //     ) : (
            //       <a
            //         href={`${ActiveDomain}${slug}`}
            //         target='_blank'
            //         rel='noopener noreferrer'
            //         className='hover:text-blue-400 flex justify-between items-center'
            //       >
            //         {slug}
            //       </a>
            //     )
            //   },
            // },
            {
              title: 'External URL',
              dataIndex: 'slug',
              key: 'slug',
              width: 200,
              render: (_, row, index) => {
                const {
                  shipedPageId,
                  isDeleted,
                  masterPage,
                  historyCount = 0,
                } = experimentData || {}
                const variant = `${masterPage}?variant=${historyCount + index}`
                const isShippedOrDeleted =
                  (shipedPageId && row?.id === shipedPageId) || isDeleted

                return isShippedOrDeleted ? (
                  variant
                ) : (
                  <a
                    href={`${ActiveDomain}${variant}`}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='hover:text-blue-400 flex justify-between items-center'
                  >
                    {variant}
                  </a>
                )
              },
            },

            {
              title: 'Experiment results',
              dataIndex: 'id',
              key: 'id',
              render: (id: string) => (
                <span
                  className={`${
                    experimentData?.shipedPageId &&
                    id === experimentData?.shipedPageId &&
                    'p-2 text-accent4-800 bg-accent4-100 rounded-md'
                  }`}
                >
                  {experimentData?.shipedPageId
                    ? id === experimentData?.shipedPageId
                      ? 'Winning variant '
                      : ''
                    : '-'}
                </span>
              ),
              width: 100,
            },
          ]}
        />
        {/* </Box> */}
      </Modal>
      <Tooltip title={'View'}>
        <Button
          onClick={() => {
            setExperimentData(true)
          }}
          type='primary'
          className={'text-neutral-950 bg-transparent border-none'}
        >
          <CiCircleInfo size={20} />
          {/* Delete Document */}
        </Button>
      </Tooltip>
      {/* <CustomButton
        variant='secondary'
        tooltipText='View'
        size='small'
        tooltipPlacement='top'
        startIcon={<Info className='h-6 w-6' />}
        onClick={() => {
          setExperimentData(true)
          // if (dataofExp?.status) {
          //   setExperimentData(dataofExp?.data)
          // }
        }}
      /> */}
    </>
  )
}

export default DeatilView
