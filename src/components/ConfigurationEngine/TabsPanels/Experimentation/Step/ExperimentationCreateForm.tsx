import { Box, TextInput } from '@contentful/f36-components'
import React from 'react'
import FormControlComp from '../../../../Shared/FormControlComp'

const ExperimentationStepOne = ({
  payload,
  setPayload,
}: {
  payload: any
  setPayload: Function
}) => {
  return (
    <Box
      className='overflow-y-auto w-full '
      style={{
        // width: '100%',
        height: '96%',
        // display: 'flex',
        // justifyContent: 'center',
        // alignItems: 'center',
        // flexDirection: 'column',
        // overflow: 'scroll',
      }}
    >
      <Box
        className='w-full flex justify-center items-center flex-col px-2  gap-5'
        // style={{
        //   width: '100%',
        //   // height: '96%',
        //   display: 'flex',
        //   justifyContent: 'center',
        //   alignItems: 'center',
        //   flexDirection: 'column',
        //   overflow: 'scroll',
        // }}
      >
        <FormControlComp
          label='Experimentation Title'
          isRequired
          className='input-50'
        >
          <TextInput
            value={payload?.experimentTitle ?? ''}
            name='experimentTitle'
            id='experimentTitle'
            placeholder='Experimentation Title'
            onChange={(e) =>
              setPayload((prev: any) => ({
                ...prev,
                experimentTitle: e?.target?.value,
              }))
            }
          />
        </FormControlComp>
        <FormControlComp
          label='Experimentation Description'
          isRequired
          className='input-50'
        >
          <TextInput
            value={payload?.experimentDescription ?? ''}
            name='experimentDescription'
            id='experimentDescription'
            placeholder='Experimentation Description'
            onChange={(e) =>
              setPayload((prev: any) => ({
                ...prev,
                experimentDescription: e?.target?.value,
              }))
            }
          />
        </FormControlComp>
        <FormControlComp
          label='Routing URL'
          isRequired
          className='input-50'
          tooltip='This is the end user URL'
        >
          <TextInput
            value={payload?.masterPage ?? ''}
            name='masterPage'
            id='masterPage'
            placeholder='Routing URL'
            onChange={(e) =>
              setPayload((prev: any) => ({
                ...prev,
                masterPage: e?.target?.value?.trim()?.toLowerCase(),
              }))
            }
          />
        </FormControlComp>

        {/* </Grid> */}
      </Box>
    </Box>
  )
}

export default ExperimentationStepOne
