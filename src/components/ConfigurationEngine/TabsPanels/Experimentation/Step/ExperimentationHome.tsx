import { Checkbox, Spinner, TextInput } from '@contentful/f36-components'
import { ConfigProvider } from 'antd'
import React, { useMemo, useState } from 'react'
import { FaPlus } from 'react-icons/fa6'
import { PiFlaskLight } from 'react-icons/pi'
import Info from '../../../../../assets/icons/Info'
import { ENV_VARIABLES } from '../../../../../constant/variables'
import { getBranchDomainByTag } from '../../../../../globals/utils'
import { Button, notification, Table, Tooltip } from '../../../../atoms'
import BuildButton from '../../../../BuildButton'
import { domainsConfig } from '../../../../Crosspost/utils'
import { getPreviewUrlByBranch } from '../../../../Preview'
import { experimentationSteps } from '../experiment-const'
import { ExperimentationCRUD } from '../experimentation-helper'
import CompleteExperiment from './Modals/CompleteExperiment'
import DeatilView from './Modals/DeatilView'
import DeleteExperiment from './Modals/DeleteExperiment'
import ViewHistory from './Modals/ViewHistory'

// ExperimentationData fields from which you want to allow searching
const searchableColumns: (keyof ExperimentationData)[] = [
  'experimentTitle',
  'experimentDescription',
  'masterPage',
]

/**
 *
 * @param setActiveStep - Function to set the active step in the experimentation process.
 * @param data - The root data for the experiment.
 * @param SaveConfigurationandFetchData - Function to save the configuration and fetch updated data.
 * @param isLoading - A boolean indicating if the data is currently loading.
 * @param setIsLoading - Function to set the loading state.
 * @param selectedDomain - The selected domain for the experiment.
 * @param ActiveDomain - The active domain for the experiment.
 * @param isPageView - Optional boolean to indicate if the view is a page view.
 * @param isButtonDisabled - Boolean to indicate if the build button should be disabled.
 * @param setIsButtonDisabled - Function to set the button disabled state.
 * @param loading - Optional boolean to indicate if the table is loading.
 * This component renders the first step of the experimentation process.
 * It displays a list of experiments with options to create a new experiment,
 * search for existing experiments, and manage their statuses.
 * It includes a table with experiment details, such as status, title, routing URL,
 * and actions like viewing details, history, and managing the experiment's lifecycle.
 * @returns
 */
const ExperimentationStepZero = ({
  setActiveStep,
  data,
  SaveConfigurationandFetchData,
  isLoading,
  setIsLoading,
  selectedDomain = '',
  ActiveDomain,
  isPageView,
  isButtonDisabled,
  setIsButtonDisabled,
  loading,
}: {
  setActiveStep: Function
  data: any
  SaveConfigurationandFetchData: Function
  isLoading: boolean
  setIsLoading: Function
  selectedDomain: string
  ActiveDomain: string
  isPageView?: boolean
  isButtonDisabled: boolean
  setIsButtonDisabled: Function
  loading?: boolean
}) => {
  const ActiveDomainLink =
    domainsConfig?.find(
      (el) => el?.key === selectedDomain && ENV_VARIABLES.isMainPreview
    )?.urlwithoutbackslash ??
    `${getBranchDomainByTag(
      domainsConfig?.find((el) => el?.key === selectedDomain)?.domainKey ??
        'domainAltusGroupCom',
      ENV_VARIABLES.MainPreviewBranch
    )}`
  // const [isButtonDisabled, setIsButtonDisabled] = useState(true)
  const FullData = ExperimentationCRUD.getAll(data)?.data?.map((el) => ({
    ...el,
    statusOfExperiment: el?.isShiped
      ? 'Done'
      : el?.isDeleted
      ? 'Deleted'
      : el?.isPublished
      ? 'Active'
      : 'Standby',
  }))

  const [search, setSerach] = useState('')
  const [loadingbox, setCheckboxLoading] = useState('')
  const filteredData = useMemo(() => {
    if (!search || !searchableColumns?.length) return FullData
    return FullData?.filter((row: ExperimentationData) => {
      return searchableColumns?.some((columnId) => {
        const value = row?.[columnId]
        return String(value)?.toLowerCase()?.includes(search?.toLowerCase())
      })
    })
  }, [FullData, search])
  const handlePreviewUrlByBranch = (branch: string, slug: string) => {
    const previewUrl = getPreviewUrlByBranch(branch, selectedDomain ?? 'agl')
    window.open(previewUrl + slug, '_blank')
  }
  return (
    <div
      style={{
        maxWidth: '80vw',
        width: '100%',
        height: '96%',
        padding: '10px 20px',
      }}
    >
      {/* </ModalConfirm> */}

      <div
      // sx={{
      //   width: '100%',
      // }}
      >
        <div className='w-full flex gap-3 '>
          <div
            className='w-2/3 flex justify-start mb-5 gap-4'
            // marginBottom='spacingS'
            // style={{ width: '30%' }}
            // display='flex'
            // justifyContent='space-between'
          >
            <h3>Experiments</h3>

            <div className='flex justify-center items-center gap-4'>
              <Button
                type='primary'
                onClick={() => {
                  setActiveStep(experimentationSteps.detailform)
                }}
              >
                <FaPlus /> Create Experiment
              </Button>
            </div>

            <BuildButton
              builddisabled={isButtonDisabled}
              setBuildDisabled={setIsButtonDisabled}
              selectedDomain={selectedDomain}
              ActiveDomainLink={ActiveDomainLink}
            />
          </div>
          <div
            className='w-1/3 flex justify-between mb-5 '
            // marginBottom='spacingS'
            // style={{ width: '30%' }}
            // display='flex'
            // justifyContent='space-between'
          >
            <TextInput
              placeholder='Search'
              onChange={(e) => {
                setSerach(e?.target?.value ?? '')
              }}
            />
          </div>
        </div>
        {/* <Alert
          type='warning'
          title='Publish Required'
          description='You must publish the page before launching the experiment to Masters. Failing to do so may result in inaccurate data or experiment failure.'
          hasIcon
          icon={<AiFillWarning className='text-yellow-500' />}
        /> */}
        {/* <div
      style={{
        height: `calc(100vh - 270px)`,
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
      }}
    > */}
        <div>
          <Table
            // sticky={{
            //   offsetHeader: 64,
            // }}
            loading={loading}
            dataSource={filteredData}
            pagination={{
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              defaultPageSize: 10,
              showQuickJumper: true,
              showPrevNextJumpers: true,
            }}
            scroll={{
              x: 'auto',
              y: isPageView ? 'calc(100vh - 450px)' : 'calc(100vh - 300px)',
            }}
            columns={[
              {
                title: 'Status',
                dataIndex: 'statusOfExperiment',
                key: 'statusOfExperiment',
                render: (v: Status, row) => {
                  return (
                    <Tooltip title={`${statusText?.[v]}`}>
                      <span
                        className={` h-3 w-3 rounded-full block  ${
                          statusDot?.[`${v}`]
                        }`}
                      ></span>
                    </Tooltip>
                  )
                },
                // type Status = 'InProgress' | 'Done' | 'Deleted'
                width: 90,
                filters: [
                  { text: 'Active', value: 'Active' },
                  { text: 'Standby', value: 'Standby' },
                  { text: 'Done', value: 'Done' },
                  { text: 'Deleted', value: 'Deleted' },
                ],
                onFilter: (value, record) =>
                  record?.statusOfExperiment === value,
              },
              {
                title: 'Title',
                dataIndex: 'experimentTitle',
                key: 'experimentTitle',
                // width: 300,
                sorter: (a, b) =>
                  a.experimentTitle.localeCompare(b.experimentTitle),
                render: (experimentTitle: string, row) => {
                  const experimentUrl = `${ActiveDomain}${row?.masterPage}`
                  return (
                    // <Tooltip title={experimentUrl}>
                    <div
                      style={{ display: 'flex', flexDirection: 'column' }}
                      // className='hover:scale-105'
                    >
                      <a
                        href={experimentUrl}
                        target='_blank'
                        download
                        rel='noreferrer'
                        style={{
                          transition: 'all 0.2s',
                        }}
                      >
                        {experimentTitle}
                      </a>
                      <span
                        style={{
                          color: 'gray',
                          fontSize: '12px',
                          // wordBreak: 'break-all',
                        }}
                      >
                        {experimentUrl}
                      </span>
                    </div>
                    // </Tooltip>
                  )
                },
              },
              {
                title: 'Routing URL',
                dataIndex: 'masterPage',
                key: 'masterPage',
                sorter: (a, b) => a.masterPage.localeCompare(b.masterPage),
                render: (value, row) => (
                  <div className='flex gap-5'>
                    <ConfigProvider
                      theme={{
                        token: {
                          colorPrimary: '#000',
                        },
                      }}
                    >
                      <Button
                        type='primary'
                        ghost
                        color='default'
                        onClick={() =>
                          handlePreviewUrlByBranch(
                            ENV_VARIABLES.MainPreviewBranch,
                            value
                          )
                        }
                        disabled={row?.isDeleted}
                      >
                        Open {ENV_VARIABLES.MainPreviewBranch}
                      </Button>
                      <Button
                        type='primary'
                        color='default'
                        disabled={!row?.isPublished}
                        onClick={() =>
                          handlePreviewUrlByBranch(
                            ENV_VARIABLES?.mainBranch,
                            value
                          )
                        }
                      >
                        Open {ENV_VARIABLES?.mainBranch}
                      </Button>
                    </ConfigProvider>
                  </div>
                ),
                // width: 250,
              },
              {
                title: (
                  <Tooltip
                    title={
                      'This experiment has been Live and is publicly available.'
                    }
                  >
                    <p className='flex gap-3 justify-center items-center'>
                      Live
                      <Info />
                    </p>
                  </Tooltip>
                ),
                // showSorterTooltip: false,
                dataIndex: 'isPublished',
                key: 'isPublished',
                // width: 20,
                // sorter: (a, b) => Number(a.isPublished) - Number(b.isPublished),
                render: (isPublished, row: ExperimentationData) => (
                  <div className='flex items-center justify-center'>
                    {loadingbox?.trim() === row?.experimentationId?.trim() ? (
                      <Spinner />
                    ) : (
                      <Checkbox
                        isDisabled={Boolean(
                          row?.isDeleted || loadingbox?.trim()?.length
                        )}
                        isChecked={Boolean(isPublished)}
                        // isIndeterminate={}
                        onChange={async (v) => {
                          setCheckboxLoading(row?.experimentationId)
                          const res = ExperimentationCRUD.edit(
                            data,
                            row?.experimentationId,
                            { isPublished: v.target.checked }
                          )
                          try {
                            if (res?.status) {
                              await SaveConfigurationandFetchData(res?.data)
                              setIsButtonDisabled(false)
                              if (Boolean(isPublished)) {
                                notification.success({
                                  message: (
                                    <p
                                      className={'fSansBld'}
                                      style={{
                                        lineHeight: 1,
                                        marginBottom: '15px',
                                      }}
                                    >
                                      {`${row?.experimentTitle} experiment will be deactivated`}
                                    </p>
                                  ),
                                  description:
                                    'Do not forget to push your changes to live.',
                                })
                              } else {
                                notification.success({
                                  message: (
                                    <p
                                      className={'fSansBld'}
                                      style={{
                                        lineHeight: 1,
                                        marginBottom: '15px',
                                      }}
                                    >
                                      {`${row?.experimentTitle} experiment will be live`}
                                    </p>
                                  ),
                                  description:
                                    'Do not forget to push your changes to live.',
                                })
                              }
                            }
                            setCheckboxLoading('')
                          } catch (error) {
                            setCheckboxLoading('')
                            notification.error({
                              message: (
                                <p
                                  className={'fSansBld'}
                                  style={{
                                    lineHeight: 1,
                                    marginBottom: '15px',
                                  }}
                                >
                                  {`${row?.experimentTitle} Something went wrong `}
                                </p>
                              ),
                            })
                          }
                        }}
                      />
                    )}
                  </div>
                ),
              },
              {
                title: 'Action',
                dataIndex: 'action',
                key: 'action',
                width: 200,
                render: (_, row) => (
                  <div className='flex gap-3'>
                    <DeatilView
                      experimentData={row}
                      ActiveDomain={ActiveDomain}
                    />
                    <ViewHistory
                      data={data}
                      masterPage={row?.masterPage}
                      ActiveDomain={ActiveDomain}
                    />
                    <a
                      href={`https://console.statsig.com/${ENV_VARIABLES.statsigConsoleId}/experiments/${row?.experimentationId}/setup`}
                      target='_blank'
                      rel='noopener noreferrer'
                      className='hover:text-blue-400 flex'
                    >
                      <Tooltip title={'Open in Statsig'}>
                        <Button
                          type='primary'
                          className={
                            'text-neutral-950 bg-transparent border-none'
                          }
                        >
                          <PiFlaskLight size={20} />
                          {/* Delete Document */}
                        </Button>
                      </Tooltip>
                      {/* <CustomButton
                      variant='transparent'
                      tooltipText='Open in Statsig'
                      size='small'
                      tooltipPlacement='top'
                      startIcon={<AiFillExperiment className='h-6 w-6 ' />}
                    /> */}
                    </a>

                    <CompleteExperiment
                      SaveConfigurationandFetchData={
                        SaveConfigurationandFetchData
                      }
                      data={data}
                      experimentData={row}
                      setIsLoading={setIsLoading}
                      isLoading={isLoading}
                      ActiveDomain={ActiveDomain}
                    />
                    <DeleteExperiment
                      SaveConfigurationandFetchData={
                        SaveConfigurationandFetchData
                      }
                      data={data}
                      experimentData={row}
                      setIsLoading={setIsLoading}
                      isLoading={isLoading}
                      ActiveDomain={ActiveDomain}
                    />
                  </div>
                ),
              },
            ]}
          />
        </div>
        {/* </div> */}
      </div>
    </div>
  )
}

export default ExperimentationStepZero

type Status = 'Active' | 'Done' | 'Deleted' | 'Standby'

interface TagProps {
  status: Status
  isPublished?: boolean
}

// This component renders a tag based on the status of an experiment.
// It uses different styles and text based on the status provided.
// The styles and text are defined in the statusStyles, statusDot, and statusText objects
// The statusStyles object contains the background color, text color, and ring color for each status
// The statusDot object contains the background color and ring color for the status dot
// The statusText object contains the text to be displayed for each status
// The component returns a span element with the appropriate classes and text based on the status prop
const statusStyles: Record<string, string> = {
  Standby: 'bg-accent9-100 text-accent9-800 ring-accent9-300',
  Done: 'bg-primary2-100 text-primary2-800 ring-primary2-300',
  Deleted: 'bg-accent10-100 text-accent10-800 ring-accent10-300',
  Active: 'bg-accent3-100 text-accent3-800 ring-accent3-300',
}
const statusDot: Record<string, string> = {
  Standby: 'bg-accent9-300 ring-accent9-300',
  Done: 'bg-primary2-300 ring-primary2-300',
  Deleted: 'bg-accent10-300 ring-accent10-300',
  Active: 'bg-accent3-300 ring-accent3-300',
}

const statusText: Record<string, string> = {
  Standby: 'Experiment is on standby',
  Done: 'Experiment is over',
  Deleted: 'Experiment was terminated',
  Active: 'Experiment is live',
}
export const Tag: React.FC<TagProps> = ({ status }) => {
  const classes = `inline-flex items-center rounded-md px-2 py-1 text-sm font-medium ring-1 ring-inset  ${
    statusStyles?.[`${status}`]
  }`

  return <span className={classes}>{statusText?.[status]}</span>
}
