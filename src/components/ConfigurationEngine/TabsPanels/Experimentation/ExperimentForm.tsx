import { Box } from '@contentful/f36-components'
import { init } from 'contentful-ui-extensions-sdk'
import React, { useEffect, useState } from 'react'
import { ENV_VARIABLES } from '../../../../constant/variables'
import {
  getAssetData,
  getBranchDomainByTag,
  updateEntryData,
} from '../../../../globals/utils'
import { notification } from '../../../atoms'
import { domainsConfig } from '../../../Crosspost/utils'
import ProgressBar from '../../../ProgressBar'
import { fetchEntryDetails } from '../Notifications/utils'
import { experimentationSteps } from './experiment-const'
import { hasDuplicateSlugs } from './experimentation-helper'
import ExperimentationStepOne from './Step/ExperimentationCreateForm'
import ExperimentationStepTwo from './Step/ExperimentationCreateViewConfirm'
import ExperimentationEntrySelector from './Step/ExperimentationEntrySelector'
import ExperimentationFinal from './Step/ExperimentationFinal'
import ExperimentationStepZero from './Step/ExperimentationHome'
import ExperimentationModalControlsSection from './Step/ExperimentationModalControlsSection'
import ExperimentRunning from './Step/ExperimentRunning'

/**
 *
 * @param data - The root data for the experiment.
 * @param contentId - The ID of the content to be updated.
 * @param refetchData - Function to refetch the data after updates.
 * @param activeStepinitial - The initial step to be displayed in the experiment form.
 * @param initialPageData - The initial data for the page being experimented on.
 * @param selectedDomain - The domain selected for the experiment.
 * @param isExperimentRunning - Flag to indicate if the experiment is currently running.
 * @param loading - Flag to indicate if the form is in a loading state.
 * @param domainTagFullName - The full name of the domain tag.
 * @returns A React component that renders the experiment form with various steps and controls.
 */
const ExperimentForm = ({
  data,
  contentId,
  refetchData,
  activeStepinitial = experimentationSteps?.dahsboard,
  initialPageData,
  selectedDomain = '',
  isExperimentRunning,
  loading,
  domainTagFullName,
}: {
  data: ExperimentRootData
  contentId: string
  refetchData: Function
  activeStepinitial?: number
  initialPageData: null | any
  selectedDomain: string
  isExperimentRunning?: boolean
  loading?: boolean
  domainTagFullName: string
}) => {
  const ActiveDomain =
    domainsConfig?.find(
      (el) => el?.key === selectedDomain && ENV_VARIABLES?.isMainPreview
    )?.url ??
    `${getBranchDomainByTag(
      domainTagFullName,
      process.env.REACT_APP_BRANCH_FRONT ?? 'dev'
    )}/`
  const [activeStep, setActiveStep] = useState(activeStepinitial)
  const [loadingText, setLoadingText] = useState('')
  const [payload, setPayload] = useState({
    masterPage: '',
    experimentDescription: '',
    experimentTitle: '',
    // experimentationId: '',
    // end_time: undefined,
    // start_time: undefined,
  })
  const [selectedMetaDataIndex, setSelectedMetaDataIndex] = useState<
    string | null
  >(null)
  const [isButtonDisabled, setIsButtonDisabled] = useState(true)
  const [extension, setExtension] = useState<any>(null)
  const [finalData, setFinalData] = useState<Partial<ExperimentationData>>({})
  const [carouselData, setCarouselData] = useState<(any | null)[]>([
    initialPageData,
    null,
  ])
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    setCarouselData([null, initialPageData])
  }, [initialPageData])

  /**
   *
   * @param data - The experiment root data to be saved and fetched.
   * This function updates the entry data with the provided experiment root data,
   * then refetches the data and enables the button.
   * @returns A promise that resolves when the data is saved and fetched.
   * @description
   * The function constructs a payload with the experiment root data,
   * formats it into a specific structure, and updates the entry data in Contentful.
   * After updating, it calls the `refetchData` function to refresh the data.
   * Finally, it sets the button state to enabled.
   * This is typically used when the user has completed the experiment configuration
   * and is ready to save their changes and view the updated data.
   */
  const SaveConfigurationandFetchData = async (data: ExperimentRootData) => {
    let updatedPayload = {
      data: {
        'en-CA': {
          content: [
            {
              content: [
                {
                  data: {},
                  marks: [],
                  nodeType: 'text',
                  value: JSON.stringify(data),
                },
              ],
              data: {},
              nodeType: 'paragraph',
            },
          ],
          data: {},
          nodeType: 'document',
        },
      },
    }
    await updateEntryData(contentId, updatedPayload, true)
    await refetchData()
    setIsButtonDisabled(false)
  }
  const SingleEntrySelector = async (contentTypes: string[]) => {
    if (!extension) return

    const selected = await extension.dialogs.selectSingleEntry({
      locale: extension.locales.default,
      contentTypes: contentTypes,
    })

    if (!selected) return

    return selected
    // const detailedEntry = await fetchEntryDetails(selected?.sys?.id, extension)

    // return detailedEntry
  }

  const pageSelector = async (index: number) => {
    const contentTypes = ['page']

    const selected = await SingleEntrySelector(contentTypes)

    if (!selected) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            Error fetching entry details or you have not selected any page
          </p>
        ),
      })
      return
    }
    if (selected && !selected?.fields?.slug?.['en-CA']) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            This Page do not have slug please add slug
          </p>
        ),
      })
      return
    }

    if (
      selected &&
      !!selected?.fields?.experimentationId?.['en-CA'] &&
      selected?.fields?.isExperimentation?.['en-CA']
    ) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            This page is currently used in experiment.
          </p>
        ),
        description: 'please select another page.',
      })
      return
    }

    const hasDuplicatedSlugs = hasDuplicateSlugs(
      [
        ...carouselData?.map((el) => ({
          slug: el?.selected?.fields?.slug?.['en-CA'],
        })),
        { slug: selected?.fields?.slug?.['en-CA'] },
      ]?.filter((el) => el?.slug)
    )
    if (hasDuplicatedSlugs) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            Page Variants can not have same slug.
          </p>
        ),
        description: 'please select pages with unique slug',
      })
      return
    }
    const detailedEntry = await fetchEntryDetails(selected?.sys?.id, extension)
    const pageThumbnailFull: any = await getAssetData(
      selected?.fields?.pageThumbnail?.['en-CA']?.sys?.id
    ).then((res) => res)
    const pageThumbnail = {
      url: pageThumbnailFull?.fields?.file?.['en-CA'].url,
      status: pageThumbnailFull?.sys?.fieldStatus?.['*']?.['en-CA'],
    }
    setCarouselData((prev) => {
      const prevData = [...prev]
      prevData[index] = { detailedEntry, selected, pageThumbnail }

      const newData = [...prevData]?.filter((el) => el) ?? []
      newData?.unshift(null)
      return [...newData]
    })
  }

  useEffect(() => {
    init((ext: any) => {
      ext?.window?.startAutoResizer()
      setExtension(ext)
    })
  }, [])

  /**
   * Render the ExperimentForm component.
   * This component displays the experiment form with various steps and controls.
   * It handles the experiment creation process, including selecting pages,
   * filling out details, confirming the experiment, and finalizing it.
   * If the experiment is running, it displays the ExperimentRunning component.
   * @returns {JSX.Element} The rendered ExperimentForm component.
   * @description
   * The ExperimentForm component manages the state of the experiment creation process.
   * It uses various steps defined in the `experimentationSteps` object to guide the user
   * through the experiment setup. The component conditionally renders different steps
   * based on the current active step. It also handles loading states and button
   * disabling logic to ensure the user completes necessary actions before proceeding.
   * If the experiment is running, it displays the `ExperimentRunning` component,
   * which shows the current status of the experiment.
   * The component also includes a progress bar to indicate the user's progress through the steps.
   * The `ExperimentationModalControlsSection` component provides controls for saving
   * the configuration, navigating between steps, and managing the experiment data.
   * The component is designed to be flexible and can handle various scenarios,
   * such as loading initial data, handling user interactions, and displaying notifications.
   */
  return isExperimentRunning ? (
    <ExperimentRunning
      data={data}
      initialPageData={initialPageData}
      ActiveDomain={ActiveDomain}
    />
  ) : (
    <Box
      style={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        height: '100%',
      }}
    >
      <Box style={{ width: '100%', height: 'auto' }}>
        <Box style={{ height: '4px' }} />
        <ProgressBar
          completedSteps={activeStep}
          totalSteps={Object.keys(experimentationSteps ?? {}).length - 1}
        />
        <Box style={{ height: '4px' }} />
      </Box>
      {activeStep === experimentationSteps.dahsboard && (
        <ExperimentationStepZero
          isLoading={isLoading}
          setIsLoading={setIsLoading}
          isButtonDisabled={isButtonDisabled}
          setIsButtonDisabled={setIsButtonDisabled}
          setActiveStep={setActiveStep}
          data={data}
          selectedDomain={selectedDomain}
          ActiveDomain={ActiveDomain}
          SaveConfigurationandFetchData={SaveConfigurationandFetchData}
          isPageView={
            initialPageData &&
            typeof initialPageData === 'object' &&
            Object.keys(initialPageData)?.length
          }
          loading={loading}
        />
      )}
      {activeStep === experimentationSteps.entrySelector && (
        <ExperimentationEntrySelector
          carouselSelector={pageSelector}
          carouselAddMore={() => {
            setCarouselData((prev) => [null, ...prev])
          }}
          setCarouselData={setCarouselData}
          carouselData={carouselData}
        />
      )}
      {activeStep === experimentationSteps.detailform && (
        <ExperimentationStepOne payload={payload} setPayload={setPayload} />
      )}

      {activeStep === experimentationSteps.confirmExperiment && (
        <ExperimentationStepTwo
          data={data}
          ActiveDomain={ActiveDomain}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
          payload={payload}
          carouselData={carouselData}
          setSelectedMetaDataIndex={setSelectedMetaDataIndex}
          selectedMetaDataIndex={selectedMetaDataIndex}
        />
      )}
      {activeStep === experimentationSteps.finalExperiment && (
        <ExperimentationFinal
          isLoading={isLoading}
          // setIsLoading={setIsLoading}
          payload={finalData}
          // finalData={finalData}
          // setFinalData={setFinalData}
          loadingText={loadingText}
          ActiveDomain={ActiveDomain}
        />
      )}
      {/* Controls Section Footer Buttons */}
      {activeStep !== experimentationSteps.dahsboard && (
        <ExperimentationModalControlsSection
          data={data}
          activeStep={activeStep}
          SaveConfigurationandFetchData={SaveConfigurationandFetchData}
          carouselData={carouselData}
          currentDomain={domainTagFullName}
          isLoading={isLoading}
          payload={payload}
          selectedMetaDataIndex={selectedMetaDataIndex}
          setCarouselData={setCarouselData}
          setFinalData={setFinalData}
          setIsLoading={setIsLoading}
          setLoadingText={setLoadingText}
          setPayload={setPayload}
          setSelectedMetaDataIndex={setSelectedMetaDataIndex}
          setActiveStep={setActiveStep}
          refetchData={refetchData}
        />
      )}
    </Box>
  )
}

export default ExperimentForm
