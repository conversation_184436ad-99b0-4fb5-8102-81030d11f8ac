import {
  FormControl,
  Tabs,
  TextInput,
  Tooltip,
} from '@contentful/f36-components'
import React from 'react'
import Info from '../../../../assets/icons/Info'
import { ComponentProps } from '../../interface'

function Title(props: ComponentProps) {
  return (
    <Tabs.Panel id='title' className='tabPanelRoot'>
      <FormControl
        id='original-domain'
        style={{
          padding: '1rem',
          paddingBottom: '0rem',
          paddingLeft: '0rem',
        }}
      >
        <div className='formLabelWithIcon'>
          <FormControl.Label>Title</FormControl.Label>
          <Tooltip
            placement='top'
            id='tooltip-1'
            content='Provide GTM Tag for website.'
          >
            <Info />
          </Tooltip>
        </div>
        <TextInput
          size='medium'
          placeholder='SEO Title'
          value={props.value}
          onChange={(e) => props.handleChange('title', e.target.value)}
        />
      </FormControl>
    </Tabs.Panel>
  )
}

export default Title
