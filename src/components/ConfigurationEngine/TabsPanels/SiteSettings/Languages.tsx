import {
  Box,
  ButtonGroup,
  FormControl,
  Switch,
  Tabs,
  TextInput,
  ToggleButton,
  Tooltip,
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import Info from '../../../../assets/icons/Info'
import { Languages } from '../../../../globals/utils'
import { ComponentProps } from '../../interface'

function SupportedLanguages(props: ComponentProps) {
  const [selectedLangs, setSelectedLangs] = useState<string[]>([])

  const [activeButton, setActiveButton] = useState('')

  const handleLangsChange = (lang: string) => {
    if (selectedLangs.includes(lang)) {
      setSelectedLangs(selectedLangs.filter((item) => item !== lang))
    } else {
      setSelectedLangs([...selectedLangs, lang])
    }
  }

  useEffect(() => {
    props.handleChange('supportedLanguages', selectedLangs)
  }, [selectedLangs])

  return (
    <Tabs.Panel id='languages' className='tabPanelRoot'>
      <FormControl
        id='original-domain'
        style={{
          padding: '1rem',
          paddingBottom: '0rem',
          paddingLeft: '0rem',
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
            gap: 40,
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'start',
              flexDirection: 'column',
            }}
          >
            <div className='formLabelWithIcon formLabelPadding'>
              <FormControl.Label>Enabled Languages</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Add scripts to the footer of the website.'
              >
                <Info />
              </Tooltip>
            </div>
            {Languages.map((language) => (
              <Switch
                key={language.value}
                isChecked={selectedLangs.includes(language.value)}
                onChange={() => handleLangsChange(language.value)}
                style={{
                  padding: '0px !important',
                }}
              >
                {language.title}
              </Switch>
            ))}
          </div>

          <span
            style={{
              backgroundColor: 'gray',
              height: '250px',
              width: '1px',
            }}
          ></span>

          <div
            style={{
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'start',
              flexDirection: 'column',
            }}
          >
            <div className='formLabelWithIcon'>
              <FormControl.Label>URL Structure</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Add scripts to the footer of the website.'
              >
                <Info />
              </Tooltip>
            </div>
            <ButtonGroup>
              <ToggleButton
                isActive={activeButton === 'suffix'}
                aria-label='Italic'
                size='small'
                onToggle={() => {
                  setActiveButton('suffix')
                }}
              >
                Is Suffix
              </ToggleButton>
              <ToggleButton
                isActive={activeButton === 'prefix'}
                aria-label='Italic'
                size='small'
                onToggle={() => {
                  setActiveButton('prefix')
                }}
              >
                Is Prefix
              </ToggleButton>
              <ToggleButton
                isActive={activeButton === 'param'}
                aria-label='Italic'
                size='small'
                onToggle={() => {
                  setActiveButton('param')
                }}
              >
                Is Parameter
              </ToggleButton>
            </ButtonGroup>
            {activeButton === 'param' && (
              <>
                <Box marginTop={'spacingM'} />
                <TextInput
                  placeholder='Enter Parameter'
                  defaultValue={'lang'}
                />
              </>
            )}
          </div>
        </div>
      </FormControl>
    </Tabs.Panel>
  )
}

export default SupportedLanguages
