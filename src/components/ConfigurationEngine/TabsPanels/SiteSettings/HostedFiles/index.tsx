import { Box, Checkbox, Spinner, TextInput } from '@contentful/f36-components'
import React, { useEffect, useMemo, useState } from 'react'
import { BsInfoCircleFill } from 'react-icons/bs'
import { RiFilter3Line } from 'react-icons/ri'
// import { LuListFilter } from 'react-icons/lu'
// import { TfiFilter } from 'react-icons/tfi'
import { useSelector } from 'react-redux'
import { ENV_VARIABLES } from '../../../../../constant/variables'
import {
  getBranchDomainByTag,
  SaveConfigurationandData,
  sortValues,
} from '../../../../../globals/utils'
import {
  fetchHostedFiles,
  updateHostedFile,
  updateHostedFileState,
} from '../../../../../redux/slices/dashboard/hostedFiles'
import { RootState, useAppDispatch } from '../../../../../redux/store'
import { Alert, notification, Table, Tooltip } from '../../../../atoms'
import BuildButton from '../../../../BuildButton'
import { domainsConfig } from '../../../../Crosspost/utils'
import AddHostedFileModal from './AddHostedFileModal'
import DeleteHostedFileModal from './DeleteHostedFileModal'
import EditHostedFileModal from './EditHostedFileModal'
import './index.scss'

const searchableColumns: (keyof HostedFile)[] = [
  'id',
  'name',
  'source',
  'fileId',
]

function HostedFiles(props: any) {
  const dispatch = useAppDispatch()
  const state = useSelector((state: RootState) => state.hostedFiles)
  const { dataSource, loading, id } = state
  const [loadingbox, setCheckboxLoading] = useState('')
  const [isButtonDisabled, setIsButtonDisabled] = useState(true)

  const [search, setSerach] = useState('')
  const selectedDomain = props.selectedDomain
  const ActiveDomainLink =
    domainsConfig?.find(
      (el) => el?.key === selectedDomain && ENV_VARIABLES.isMainPreview
    )?.urlwithoutbackslash ??
    `${getBranchDomainByTag(
      domainsConfig?.find((el) => el?.key === selectedDomain)?.domainKey ??
        'domainAltusGroupCom',
      ENV_VARIABLES.MainPreviewBranch ?? 'dev'
    )}`
  const FullData = dataSource?.map((el) => ({
    ...el,
    // statusOfFile: Boolean(el?.isActive) ? 'Active' : 'Inactive',
  }))
  useEffect(() => {
    dispatch(fetchHostedFiles(selectedDomain?.toUpperCase()))
  }, [dispatch])
  const filteredData = useMemo(() => {
    if (!search || !searchableColumns?.length) return FullData
    return FullData?.filter((row: HostedFile) => {
      return searchableColumns?.some((columnId) => {
        const value = row?.[columnId]
        return String(value)?.toLowerCase()?.includes(search?.toLowerCase())
      })
    })
  }, [FullData, search])
  // useEffect(()=>{
  // setTimeout(() => {
  //   setIsButtonDisabled(false)
  // }, 500);
  //   },[])
  return (
    <Box
      style={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        height: '100%',
      }}
    >
      <div
        style={{
          maxWidth: '80vw',
          width: '100%',
          height: '96%',
          padding: '10px 20px',
        }}
      >
        {id?.toString() || loading ? (
          ''
        ) : (
          <Alert
            type='warning'
            showIcon
            icon={<BsInfoCircleFill />}
            className='mb-5'
            description='Configuration missing: Please create a configuration file in Contentful for this site in MSA. Contact the developer for assistance.'
          />
        )}

        <div className='w-full flex gap-3 flex-col md:flex-row'>
          <div
            className='md:w-2/3 flex justify-start mb-5 gap-4 items-center'
            // marginBottom='spacingS'
            // style={{ width: '30%' }}
            // display='flex'
            // justifyContent='space-between'
          >
            <h3>Hosted Files</h3>
            {id && (
              <AddHostedFileModal setIsButtonDisabled={setIsButtonDisabled} />
            )}
            {id && (
              <BuildButton
                builddisabled={isButtonDisabled}
                setBuildDisabled={setIsButtonDisabled}
                selectedDomain={selectedDomain}
                ActiveDomainLink={ActiveDomainLink}
              />
            )}
          </div>
          <div
            className='md:w-1/3 flex justify-between mb-5'
            // marginBottom='spacingS'
            // style={{ width: '30%' }}
            // display='flex'
            // justifyContent='space-between'
          >
            <TextInput
              placeholder='Search'
              onChange={(e) => {
                setSerach(e?.target?.value ?? '')
              }}
            />
          </div>
        </div>
        <div
          style={
            {
              // width: 'calc(100vw - 400px)',
              // height : "100px"
              // height : "calc(100vh - 300px)"
            }
          }
        >
          <Table
            dataSource={filteredData}
            loading={loading}
            // pagination={false}
            scroll={{ y: 'calc(100vh - 300px)' }}
            locale={{
              emptyText: `No data for hosted files to view for ${selectedDomain}`,
            }}
            columns={[
              {
                title: 'Status',
                dataIndex: 'isActive',
                key: 'isActive',
                render: (_, row: HostedFile) => {
                  const v = Boolean(row?.isActive) ? 'Active' : 'Inactive'
                  return (
                    <Tooltip title={`${statusText?.[v]}`}>
                      <span
                        className={` h-3 w-3 rounded-full block ${statusDot?.[v]}`}
                      ></span>
                    </Tooltip>
                  )
                },
                filterIcon: <RiFilter3Line size={20} />,
                // filterIcon : <LuListFilter size={20}  />,
                // filterIcon : <TfiFilter size={20}  />,
                // type Status = 'InProgress' | 'Done' | 'Deleted'
                width: 20,
                filters: [
                  { text: 'Inactive', value: 'Inactive' },
                  { text: 'Active', value: 'Active' },
                ],
                onFilter: (value, record) =>
                  (Boolean(record?.isActive) ? 'Active' : 'Inactive') === value,
              },
              {
                title: 'File',
                dataIndex: 'name',
                key: 'name',
                width: 120,
                sorter: (a, b) => sortValues(a, b, 'name'),
                render: (text: string, row) => {
                  const fileUrl = `${ActiveDomainLink}${row?.source}${
                    row?.extension ? `.${row.extension}` : ''
                  }`
                  return (
                    // <Tooltip title={fileUrl}>
                    <div
                      style={{ display: 'flex', flexDirection: 'column' }}
                      // className='hover:scale-105'
                    >
                      <a
                        href={fileUrl}
                        target='_blank'
                        download
                        rel='noreferrer'
                        style={{
                          transition: 'all 0.2s',
                        }}
                      >
                        {text}
                      </a>
                      <span
                        style={{
                          color: 'gray',
                          fontSize: '12px',
                          // wordBreak: 'break-all',
                        }}
                      >
                        {fileUrl}
                      </span>
                    </div>
                    // </Tooltip>
                  )
                },
              },
              // {
              //   title: 'Link',
              //   dataIndex: 'source',
              //   key: 'source',
              //   width: 20,
              //   render: (text: string) => (
              //     <Tooltip title={`${ActiveDomainLink}${text}`}>
              //       <a
              //         href={`${ActiveDomainLink}${text}`}
              //         target='_blank'
              //         rel='noreferrer'
              //         className='flex items-center justify-center text-xl'
              //       >
              //         <BsLink45Deg />
              //       </a>
              //     </Tooltip>
              //   ),
              // },
              {
                // title: (
                //   <Tooltip
                //     title={
                //       'This experiment has been Live and is publicly available.'
                //     }
                //   >
                //     <p className='flex gap-3 justify-center items-center'>
                //       Live
                //       <Info />
                //     </p>
                //   </Tooltip>
                // ),
                title: 'Live',
                width: 20,
                // showSorterTooltip: false,
                dataIndex: 'isActive',
                key: 'isActive',
                // sorter: (a, b) => Number(a.isActive) - Number(b.isActive),
                render: (isActive, row: HostedFile) =>
                  loadingbox?.trim() === row?.id?.trim() ? (
                    <Spinner />
                  ) : (
                    <Checkbox
                      isDisabled={Boolean(loadingbox?.trim()?.length)}
                      isChecked={Boolean(isActive)}
                      onChange={async (v) => {
                        if (!state?.id) {
                          return
                        }
                        setCheckboxLoading(row?.id)

                        const payload = {
                          ...row,
                          isActive: v.target.checked,
                          updatedAt: new Date()?.toISOString(),
                        }
                        const res = updateHostedFile(state, payload)
                        await SaveConfigurationandData(
                          [...res.dataSource],
                          state?.id,
                          true
                        )
                        dispatch(updateHostedFileState([...res.dataSource]))
                        setIsButtonDisabled(false)
                        if (Boolean(isActive)) {
                          notification.success({
                            message: (
                              <>
                                <p
                                  className={'fSansBld'}
                                  style={{ lineHeight: '13px' }}
                                >
                                  The file will be publicly unavailable.
                                </p>

                                <code
                                  style={{
                                    lineHeight: 1,
                                    marginBottom: '15px',
                                    display: 'inline-block',
                                  }}
                                  className={'fs1 cn2'}
                                >
                                  {row?.name}
                                </code>
                              </>
                            ),
                            description:
                              'Do not forget to push your changes to live.',
                          })
                        } else {
                          notification.success({
                            message: (
                              <>
                                <p
                                  className={'fSansBld'}
                                  style={{ lineHeight: ' 13px' }}
                                >
                                  The file will be publicly available.
                                </p>
                                <code
                                  style={{
                                    lineHeight: 1,
                                    marginBottom: '15px',
                                    display: 'inline-block',
                                  }}
                                  className={'fs1 cn2'}
                                >
                                  {row?.name}
                                </code>
                              </>
                            ),
                            description:
                              'Do not forget to push your changes to live.',
                          })
                        }

                        setCheckboxLoading('')
                      }}
                    />
                  ),
              },
              // {
              //   title: 'Asset Id',
              //   dataIndex: 'assetId',
              //   key: 'assetId',
              //   width: 30,
              //   render: (text: string) => (
              //     <a
              //       href={`https://app.contentful.com/spaces/8jgyidtgyr4v/environments/${ ENV_VARIABLES.contentfulEnvironment}/assets/${text}`}
              //       target='_blank'
              //       rel='noreferrer'
              //     >
              //       {text}
              //     </a>
              //   ),
              // },
              // {
              //   title: 'destination Id',
              //   dataIndex: 'destination',
              //   key: 'destination',
              //   width: 90,
              //   // :90,
              //   render: (text: string) => (
              //     <a
              //       href={`${text}`}
              //       className='max-w-2'
              //       target='_blank'
              //       rel='noreferrer'
              //     >
              //       {/* {text} */}
              //       asset link
              //     </a>
              //   ),
              // },
              {
                title: 'Created on',
                dataIndex: 'createdAt',
                key: 'createdAt',
                sorter: (a, b) => sortValues(a, b, 'createdAt'),
                // sorter: (a, b) =>
                //   new Date(a.expData.date) - new Date(b.expData.date),
                // sorter :
                render: (date) => (
                  <span className='text-sm text-gray-500'>
                    {date ? new Date(date).toLocaleString() : '-'}
                  </span>
                ),
                width: 40,
              },
              {
                title: 'Updated on',
                dataIndex: 'updatedAt',
                key: 'updatedAt',
                sorter: (a, b) => sortValues(a, b, 'updatedAt'),
                // sorter: (a, b) =>
                //   new Date(a.expData.date) - new Date(b.expData.date),
                // sorter :
                render: (date) => (
                  <span className='text-sm text-gray-500'>
                    {date ? new Date(date).toLocaleString() : '-'}
                  </span>
                ),
                width: 40,
              },
              {
                //title: 'Actions',
                dataIndex: ['id', 'assetId'],
                width: 35,

                render: (_, row) => (
                  <div className='flex gap-4'>
                    <EditHostedFileModal
                      id={row?.id}
                      HostedFileData={row}
                      setIsButtonDisabled={setIsButtonDisabled}
                      ActiveDomainLink={ActiveDomainLink}
                    />
                    <DeleteHostedFileModal
                      id={row?.id}
                      HostedFileData={row}
                      setIsButtonDisabled={setIsButtonDisabled}
                    />
                  </div>
                ),
              },
            ]}
          />
        </div>
        {/* <Modal
        open={isAddDocumentModalOpen}
        onCancel={handleClose}
        onClose={handleClose}
        destroyOnClose
        closable={!isLoading}
        maskClosable={!isLoading}
        cancelButtonProps={{
          disabled: isLoading,
        }}
        okButtonProps={{
          disabled: isLoading,
          loading: isLoading,
        }}
        onOk={() => {}}
        // onOk={()=>{
        //   // console.log({
        //   //   experimentData,
        //   //   unpublishPage,
        //   //   sele : experimentData?.experimentationPages?.filter((page) =>
        //   //     unpublishPage?.some((pageId) => pageId === page?.id) // Only include pages that are in the unpublishPage array
        //   //   )
        //   // })
        // }}
        okText='Confirm'
        centered
        title={<h4>Add Document Form njnnn</h4>}
        className=' '
      >

      </Modal> */}
      </div>
      {/* <Alert
        icon={<BsInfoCircle  size={'20'}/>}
        showIcon
        className='p-2'
        description='It will take few extra minute to reflect your updated content after build is done.'
      /> */}
    </Box>
  )
}

export default HostedFiles
type Status = 'Active' | 'Inactive'
const statusText: Record<Status, string> = {
  Active: 'File is available on live',
  Inactive: 'File is not available on live',
}
const statusDot: Record<Status, string> = {
  Inactive: 'bg-accent9-300 ring-accent9-300',
  Active: 'bg-accent3-300 ring-accent3-300',
}
