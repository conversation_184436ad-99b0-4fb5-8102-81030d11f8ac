import { Form } from 'antd'
import React, { useEffect, useState } from 'react'
import { GrClose } from 'react-icons/gr'
import { LuPencilLine } from 'react-icons/lu'
import { useSelector } from 'react-redux'
import { ENV_VARIABLES } from '../../../../../constant/variables'
import {
  createOrUpdateAsset,
  SaveConfigurationandData,
} from '../../../../../globals/utils'
import {
  isUpdateTimeFileIdExists,
  isUpdateTimeSourseExists,
  updateHostedFile,
  updateHostedFileState,
} from '../../../../../redux/slices/dashboard/hostedFiles'
import { RootState, useAppDispatch } from '../../../../../redux/store'
import {
  Button,
  Dragger,
  Input,
  Modal,
  notification,
  Tooltip,
} from '../../../../atoms'

const EditHostedFileModal = ({
  id,
  HostedFileData,
  setIsButtonDisabled,
  ActiveDomainLink,
}: {
  id?: string
  setIsButtonDisabled: Function
  HostedFileData?: HostedFile
  ActiveDomainLink: string
}) => {
  const state = useSelector((state: RootState) => state.hostedFiles)
  const dispatch = useAppDispatch()
  const [isVisible, setIsVisible] = React.useState<boolean>(false)
  const [form] = Form.useForm()
  const [isUploading, setIsUploading] = useState(false)
  const handleUpload = async (values: any) => {
    if (!state?.id || !id || !HostedFileData) {
      return
    }

    const { source, file, name, fileId } = values
    const exitstdoc = isUpdateTimeSourseExists(state, id, source)
    if (exitstdoc) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            {`Source "${source}" already exists.`}
          </p>
        ),
      })
      return
    }

    if (fileId) {
      const existsFileId = isUpdateTimeFileIdExists(state, id, fileId)
      if (existsFileId) {
        notification.error({
          message: (
            <p
              className={'fSansBld'}
              style={{ lineHeight: 1, marginBottom: '15px' }}
            >
              {`File ID "${fileId}" already exists.`}
            </p>
          ),
        })
        return
      }
    }

    const selectedFile = file?.[0]?.originFileObj
    if (selectedFile) {
      try {
        setIsUploading(true)

        const data = await createOrUpdateAsset(
          selectedFile,
          'en-CA',
          HostedFileData?.assetId,
          'This file was uploaded via hosted files. DO NOT modify this file. It will break the system.'
        )
        const fileUrl = data?.fields?.file?.['en-CA']?.url?.replace(
          `//${ENV_VARIABLES.assetdomain}/${ENV_VARIABLES.contentfulSpaceID}/${data?.sys?.id}`,
          ''
        )
        const extension = fileUrl
          ? fileUrl?.split('.')?.pop()?.split('?')[0] || ''
          : ''
        const payload = {
          ...HostedFileData,
          // id: `${new Date()?.toISOString()?.replace(/[^a-zA-Z0-9_-]/g, '')}`,
          source: source,
          name,
          assetId: data?.sys?.id,
          destination: fileUrl ? `${fileUrl}` : '',
          extension,
          updatedAt: new Date()?.toISOString(),
        }
        const res = updateHostedFile(state, payload)
        await SaveConfigurationandData([...res.dataSource], state?.id, true)
        dispatch(updateHostedFileState([...res.dataSource]))
        // await dispatch(addHostedFile(payload))
        // await onAssetUploaded(per, source) // Close modal after successful upload
        setIsVisible(false)
        setIsButtonDisabled(false)
        notification.success({
          message: (
            <>
              <p className={'fSansBld'} style={{ lineHeight: ' 13px' }}>
                The file is Updated.
              </p>

              <code
                style={{
                  lineHeight: 1,
                  marginBottom: '15px',
                  display: 'inline-block',
                }}
                className={'fs1 cn2'}
              >
                {payload?.name}
              </code>
            </>
          ),
          description: 'Do not forget to push your changes to live.',
        })
        form.resetFields()
      } catch (error) {
        console.error('Error uploading document:', error)
        notification.error({
          message: (
            <p
              className={'fSansBld'}
              style={{ lineHeight: 1, marginBottom: '15px' }}
            >
              Failed to upload document. Please try again.
            </p>
          ),
          // closeIcon: (
          //   <div className={'btnRoot'}>
          //     <GrClose size={'20'} />
          //   </div>
          // ),
          // icon: <SlClose size={'20'} />,
          // duration: 5,
          // showProgress: true,
        })
      } finally {
        setIsUploading(false)
      }
    } else {
      try {
        setIsUploading(true)

        const payload = {
          ...HostedFileData,
          source: source,
          fileId: fileId || '',
          name,
          updatedAt: new Date()?.toISOString(),
        }
        console.log(payload)
        const res = updateHostedFile(state, payload)
        await SaveConfigurationandData([...res.dataSource], state?.id, true)
        dispatch(updateHostedFileState([...res.dataSource]))
        // await onAssetUploaded(per, source) // Close modal after successful upload
        setIsVisible(false)
        setIsButtonDisabled(false)
        notification.success({
          message: (
            <>
              <p className={'fSansBld'} style={{ lineHeight: ' 13px' }}>
                The file is Updated.
              </p>

              <code
                style={{
                  lineHeight: 1,
                  marginBottom: '15px',
                  display: 'inline-block',
                }}
                className={'fs1 cn2'}
              >
                {payload?.name}
              </code>
            </>
          ),
          description: 'Do not forget to push your changes to live.',
        })
        form.resetFields()
      } catch (error) {
        console.error('Error uploading document:', error)
        notification.error({
          message: (
            <p
              className={'fSansBld'}
              style={{ lineHeight: 1, marginBottom: '15px' }}
            >
              Failed to upload document. Please try again.
            </p>
          ),
          // closeIcon: (
          //   <div className={'btnRoot'}>
          //     <GrClose size={'20'} />
          //   </div>
          // ),
          // icon: <SlClose size={'20'} />,
          // duration: 5,
          // showProgress: true,
        })
      } finally {
        setIsUploading(false)
      }
    }
  }
  const normFile = (e: any) => {
    if (Array.isArray(e)) {
      return e
    }
    return e?.fileList
  }
  useEffect(() => {
    form.setFieldsValue(HostedFileData)
  }, [HostedFileData, form, isVisible])
  const fileUrl = `${ActiveDomainLink}${HostedFileData?.source}${
    HostedFileData?.extension ? `.${HostedFileData.extension}` : ''
  }`
  return (
    <>
      <Tooltip title={`Update this file`}>
        <Button
          type='primary'
          className={'bg-transparent text-neutral-950'}
          onClick={() => {
            setIsVisible(true)
          }}
        >
          <LuPencilLine size={'20'} />

          {/* Edit Document */}
        </Button>
      </Tooltip>
      <Modal
        title='Edit hosted file'
        open={isVisible}
        onCancel={() => {
          setIsVisible(false)
          form.resetFields()
        }}
        centered
        footer={null}
      >
        <Form form={form} layout='vertical' onFinish={handleUpload}>
          <Form.Item
            name='fileId'
            label='File ID'
            className={'inputField'}
            rules={[
              {
                required: false,
                message: 'File ID is optional',
              },
              {
                pattern: /^[a-zA-Z0-9-_.]+$/,
                message:
                  'File ID can only contain letters, numbers, _ , .  and -',
              },
            ]}
          >
            <Input placeholder='File ID' />
          </Form.Item>
          <Form.Item
            name='name'
            label='Internal name'
            className={'inputField'}
            rules={[
              {
                required: true,
                message: 'Internal name is required',
              },
            ]}
          >
            <Input placeholder='Enter Internal Name' />
          </Form.Item>
          <Form.Item
            name='source'
            label='Live URL'
            className={'inputField'}
            rules={[
              {
                required: true,
                message: 'Live URL is required',
              },
              {
                validator: (_, value) => {
                  if (!value.startsWith('/')) {
                    return Promise.reject(new Error("URL must start with '/'."))
                  }
                  if (value === '/' || value === '/home') {
                    return Promise.reject(
                      new Error("URL cannot be '/' or '/home'.")
                    )
                  }
                  const regxPattern = HostedFileData?.isLegacy
                    ? /^[a-zA-Z0-9/_\s-]+$/
                    : /^[a-zA-Z0-9/-]+$/

                  if (!regxPattern.test(value.slice(1))) {
                    return Promise.reject(
                      new Error(
                        `URL can only contain letters, numbers, '-'${
                          HostedFileData?.isLegacy ? ", '_', space" : ''
                        } and '/'`
                      )
                    )
                  }
                  if (
                    /\./.test(value) ||
                    (!HostedFileData?.isLegacy && /\s/.test(value))
                  ) {
                    return Promise.reject(
                      new Error(
                        `URL cannot contain a file extension${
                          !HostedFileData?.isLegacy ? ' or spaces' : ''
                        }.`
                      )
                    )
                  }
                  return Promise.resolve()
                },
              },
              // {
              //   pattern: /^\/(?!$|home$).+/,
              //   message:
              //     "Invalid File URL. It must start with '/' and cannot be '/', 'home', or '/home'.",
              // },
              // {
              //   pattern: /^[^.\s]+$/, // Ensures no file extensions
              //   message: "URL cannot contain a file extension.",
              // },
            ]}
          >
            <Input placeholder='Enter the URL where the file should be available.' />
          </Form.Item>
          {/* <Form.Item
          name="file"
          label="Upload Document"
          valuePropName="fileList"
          getValueFromEvent={(e: any) => (Array.isArray(e) ? e : e && e.fileList)}
        //   rules={[{ required: true, message: 'Please upload a file.' }]}
        >
          <Upload
            beforeUpload={() => false} // Prevent auto-upload
            multiple={false}
            accept=".pdf,.doc,.docx" // Adjust file types as needed
          >
            <Button icon={<UploadOutlined />}>Select File</Button>
          </Upload>
        </Form.Item>
        <Form.Item label="Dragger"> */}
          <div>
            <p style={{ marginTop: 0, marginBottom: '15px' }}>
              <span>Current file : </span>
              <a href={fileUrl} target='_blank' download rel='noreferrer'>
                {fileUrl}
              </a>
            </p>
            <p style={{ marginTop: 0, marginBottom: '15px' }}>
              <a
                href={`https://app.contentful.com/spaces/8jgyidtgyr4v/environments/${ENV_VARIABLES.contentfulEnvironment}/assets/${HostedFileData?.assetId}`}
                target='_blank'
                download
                rel='noreferrer'
              >
                Current Asset Link
              </a>
            </p>
          </div>
          <Form.Item
            className={'inputField'}
            name='file'
            id='file'
            valuePropName='fileList'
            getValueFromEvent={normFile}
            // noStyle
          >
            <Dragger
              name='files'
              beforeUpload={() => false}
              multiple={false}
              maxCount={1}
              accept='.pdf,.csv,.xlsx,.xls,.rtf'
              itemRender={(originNode, file, fileList, actions) => (
                <div className='justify-between items-center flex mt-2'>
                  <span>{file.name}</span>
                  <GrClose
                    size={'20'}
                    style={{ cursor: 'pointer' }}
                    onClick={() => actions.remove()}
                  />
                </div>
              )}
            >
              <p className='ant-upload-text'>
                Click or drag and drop the new file to replace the existing
                file.
              </p>
              {/* <p className='ant-upload-hint'>
                Support for a single or bulk upload.
              </p> */}
            </Dragger>
          </Form.Item>
          {/* </Form.Item> */}
          <Form.Item className='flex justify-center inputField'>
            <Button
              type='primary'
              htmlType='submit'
              loading={isUploading}
              // block
              className=' bg-neutral-950'
            >
              {isUploading ? 'Updating...' : 'Upload and replace hosted file'}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default EditHostedFileModal
