.ant-popconfirm-title {
  margin: 3px 0 15px 0 !important;
}

.ant-popconfirm-description {
  margin: 0 0 15px 0 !important;
}

.ant-popconfirm-message {
  margin: 0 !important;
}

.ant-popconfirm-title,
.ant-popconfirm-description,
.ant-notification-notice-description {
  line-height: 1;
}

a:hover {
  background-color: transparent ;
}

.ant-notification-notice-closable {
  padding: 25px 15px !important;
}

.ant-notification-notice-close {
  right: 15px !important;
  color: black !important;
  top: 23px !important;
}

.ant-notification-notice-message {
  margin-bottom: 0 !important;
}

p,
a,
h1,
* {
  font-family: 'Aeonik Regular', sans-serif;
}

.ant-notification-notice-description {
  max-width: 270px;
}

.ant-tooltip-inner {
  padding: 8px !important;
}

.ant-pagination-item {
  border-color: black !important;
}

.ant-btn-color-primary {
  box-shadow: none !important;
}

.ant-pagination-item {
  font-size: 18px;
  font-family: 'Aeonik bold', sans-serif;
  border: none !important;

  a {
    color: black !important;
  }
}

.ant-upload-drag {
  background-color: #e6eafb !important;
  border-color: #546fe4 !important;
}

.ant-modal-close-icon {
  color: black;

  svg {
    width: 20px;
    height: 20px;
  }
}

.ant-modal-close {
  inset-inline-end: 17px !important;
  top: 15px !important;

  &:hover {
    background-color: transparent;
  }
}
//.ant-table-filter-dropdown-btns {
//  display: none !important;
//}
.ant-dropdown-menu-item-selected {
  background-color: transparent !important;
}

.inputField:last-child{
  margin-bottom: 0 !important;
}

.ant-form-item-label{
  line-height: 14px;
}

.ant-modal-title{
  margin-bottom: 35px !important;
}
.ant-statistic-content-value{
  color: white !important;
}

.ant-btn-variant-link{
  display: none !important;
}

.ant-table-filter-dropdown-btns{
  justify-content: end !important;
}

.ant-table-filter-dropdown-btns .ant-btn-variant-solid, .ant-table-filter-dropdown-btns .ant-btn-variant-solid:hover{
 background: #000 !important;
}