import {
  Box,
  FormControl,
  Radio,
  Stack,
  Tabs,
  Tooltip,
} from '@contentful/f36-components'
import React, { useState } from 'react'
import Info from '../../../../assets/icons/Info'

function PSNavigation() {
  const [hideHeader, setHideHeader] = useState('no')
  const [hideFooter, setHideFooter] = useState('no')

  const [isNavTranslucent, setIsNavTranslucent] = useState('no')

  return (
    <Tabs.Panel id='ps-navigation' className='tabPanelRoot'>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.3rem',
        }}
      >
        <FormControl
          style={{
            paddingLeft: '0rem !import',
            width: '100%',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Hide Header Navigation</FormControl.Label>
            <Tooltip
              placement='top'
              id='dv-pri-axes-tooltip'
              content='Choose to hide the header navigation from this page.'
            >
              <Info />
            </Tooltip>
          </div>
          <Stack flexDirection='row'>
            <Radio
              id='radio1'
              name='hide-header'
              value='yes'
              isChecked={hideHeader === 'yes'}
              onChange={() => setHideHeader('yes')}
            >
              Yes
            </Radio>
            <Radio
              id='radio2'
              name='hide-header'
              value='no'
              isChecked={hideHeader === 'no'}
              onChange={() => setHideHeader('no')}
            >
              No
            </Radio>
          </Stack>
        </FormControl>
      </Box>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.3rem',
        }}
      >
        <FormControl
          style={{
            paddingLeft: '0rem !import',
            width: '100%',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Hide Footer Navigation</FormControl.Label>
            <Tooltip
              placement='top'
              id='dv-pri-axes-tooltip'
              content='Choose to hide the footer navigation from this page.'
            >
              <Info />
            </Tooltip>
          </div>
          <Stack flexDirection='row'>
            <Radio
              id='radio1'
              name='hide-footer'
              value='yes'
              isChecked={hideFooter === 'yes'}
              onChange={() => setHideFooter('yes')}
            >
              Yes
            </Radio>
            <Radio
              id='radio2'
              name='hide-footer'
              value='no'
              isChecked={hideFooter === 'no'}
              onChange={() => setHideFooter('no')}
            >
              No
            </Radio>
          </Stack>
        </FormControl>
      </Box>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.3rem',
        }}
      >
        <FormControl
          style={{
            paddingLeft: '0rem !import',
            width: '100%',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Is Navigation Translucent</FormControl.Label>
            <Tooltip
              placement='top'
              id='dv-pri-axes-tooltip'
              content={`Choose to have this page's header navigation translucent`}
            >
              <Info />
            </Tooltip>
          </div>
          <Stack flexDirection='row'>
            <Radio
              id='radio1'
              name='nav-radio'
              value='yes'
              isChecked={isNavTranslucent === 'yes'}
              onChange={() => setIsNavTranslucent('yes')}
            >
              Yes
            </Radio>
            <Radio
              id='radio2'
              name='nav-radio'
              value='no'
              isChecked={isNavTranslucent === 'no'}
              onChange={() => setIsNavTranslucent('no')}
            >
              No
            </Radio>
          </Stack>
        </FormControl>
      </Box>
    </Tabs.Panel>
  )
}

export default PSNavigation
