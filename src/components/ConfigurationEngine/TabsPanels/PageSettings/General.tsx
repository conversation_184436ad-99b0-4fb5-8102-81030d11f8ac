import {
  Box,
  FormControl,
  Select,
  Tabs,
  Tooltip,
} from '@contentful/f36-components'
import React from 'react'
import Info from '../../../../assets/icons/Info'

function PSGeneral() {
  return (
    <Tabs.Panel id='ps-general' className='tabPanelRoot'>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.3rem',
        }}
      >
        <FormControl
          style={{
            paddingLeft: '0rem !import',
            width: '100%',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Page Template</FormControl.Label>
            <Tooltip
              placement='top'
              id='dv-pri-axes-tooltip'
              content='Select a template to define the structure and layout of the page.'
            >
              <Info />
            </Tooltip>
          </div>
          <Select id='optionSelect-controlled' name='optionSelect-controlled'>
            {[
              'Generic',
              'Insight Article',
              'Press Release',
              'Home',
              'Object',
            ].map((template) => (
              <Select.Option value={template}>{template}</Select.Option>
            ))}
          </Select>
        </FormControl>
      </Box>
      {/* <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.3rem',
        }}
      >
        <FormControl
          style={{
            paddingLeft: '0rem !import',
            width: '100%',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Thumbnail Image</FormControl.Label>
            <Tooltip
              placement='top'
              id='dv-pri-axes-tooltip'
              content='Upload or select an image to represent the page in summaries and links.'
            >
              <Info />
            </Tooltip>
          </div>
          <SingleMediaEditor
              viewType={'card'}
              sdk={sdk}
              parameters={{
                instance: {
                  showCreateEntityAction: true,
                  showLinkEntityAction: true,
                },
              }}
              fieldId={'pageThumbnail'}
            />
        </FormControl>
      </Box> */}
    </Tabs.Panel>
  )
}

export default PSGeneral
