import {
  Box,
  FormControl,
  Radio,
  Stack,
  Tabs,
  Tooltip,
} from '@contentful/f36-components'
import React, { useState } from 'react'
import Info from '../../../../assets/icons/Info'

function PSDiscovery() {
  const [value, setValue] = useState('no')

  return (
    <Tabs.Panel id='ps-discovery' className='tabPanelRoot'>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.3rem',
        }}
      >
        <FormControl
          style={{
            paddingLeft: '0rem !import',
            width: '100%',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Hide From Algolia</FormControl.Label>
            <Tooltip
              placement='top'
              id='dv-pri-axes-tooltip'
              content='Select this option to prevent the page from appearing in search results powered by Algolia.'
            >
              <Info />
            </Tooltip>
          </div>
          <Stack flexDirection='row'>
            <Radio
              id='radio1'
              name='radio-controlled'
              value='yes'
              isChecked={value === 'yes'}
              onChange={() => setValue('yes')}
            >
              Yes
            </Radio>
            <Radio
              id='radio2'
              name='radio-controlled'
              value='no'
              isChecked={value === 'no'}
              onChange={() => setValue('no')}
            >
              No
            </Radio>
          </Stack>
        </FormControl>
      </Box>
    </Tabs.Panel>
  )
}

export default PSDiscovery
