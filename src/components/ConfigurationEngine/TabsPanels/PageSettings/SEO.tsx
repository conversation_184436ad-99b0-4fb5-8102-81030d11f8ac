import {
  Box,
  FormControl,
  Radio,
  Stack,
  Tabs,
  Tooltip,
} from '@contentful/f36-components'
import React, { useState } from 'react'
import Info from '../../../../assets/icons/Info'

function PSSEO() {
  const [noFollow, setNoFollow] = useState('no')

  const [noIndex, setNoIndex] = useState('no')

  return (
    <Tabs.Panel id='ps-seo' className='tabPanelRoot'>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.3rem',
        }}
      >
        <FormControl
          style={{
            paddingLeft: '0rem !import',
            width: '100%',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>
              Exclude links from search rankings{' '}
            </FormControl.Label>
            <Tooltip
              placement='top'
              id='dv-pri-axes-tooltip'
              content='Choose to prevent links on this page from being followed by search engines for ranking purposes.'
            >
              <Info />
            </Tooltip>
          </div>
          <Stack flexDirection='row'>
            <Radio
              id='radio1'
              name='noFollow'
              value='yes'
              isChecked={noFollow === 'yes'}
              onChange={() => setNoFollow('yes')}
            >
              Yes
            </Radio>
            <Radio
              id='radio2'
              name='noFollow'
              value='no'
              isChecked={noFollow === 'no'}
              onChange={() => setNoFollow('no')}
            >
              No
            </Radio>
          </Stack>
        </FormControl>
      </Box>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.3rem',
        }}
      >
        <FormControl
          style={{
            paddingLeft: '0rem !import',
            width: '100%',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Hide page from search engines</FormControl.Label>
            <Tooltip
              placement='top'
              id='dv-pri-axes-tooltip'
              content='Enable this to prevent the page from being indexed and appearing in search engine results.'
            >
              <Info />
            </Tooltip>
          </div>
          <Stack flexDirection='row'>
            <Radio
              id='radio1'
              name='noIndex'
              value='yes'
              isChecked={noIndex === 'yes'}
              onChange={() => setNoIndex('yes')}
            >
              Yes
            </Radio>
            <Radio
              id='radio2'
              name='noIndex'
              value='no'
              isChecked={noIndex === 'no'}
              onChange={() => setNoIndex('no')}
            >
              No
            </Radio>
          </Stack>
        </FormControl>
      </Box>
    </Tabs.Panel>
  )
}

export default PSSEO
