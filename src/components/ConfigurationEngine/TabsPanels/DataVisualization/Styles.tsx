import {
  Box,
  FormControl,
  IconButton,
  Select,
  Switch,
  Tabs,
  TextInput,
  Tooltip,
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import Info from '../../../../assets/icons/Info'
import ColorPickerPopup from '../../Components/ColorPickerPopup'

function DvStyles(props: any) {
  const { onChange, value, loading } = props

  const [idForColorPicker, setIdForColorPicker] = useState('')

  const [showColorPicker, setShowColorPicker] = useState(false)

  const [stylesData, setStylesData] = useState({
    showGrid: false,
    gridFormat: 'split',
    gridBgColor: '',
    gridLineColor: '',
    gridLineStyle: '',
    graphBgColor: '',
    borderColor: '',
    borderWidth: '',
    borderType: '',
    isBorderShadow: false,
  })

  const handleStylesDataChange = (key: string, value: string | boolean) => {
    const dataToUpdate = {
      ...stylesData,
      [key]: value,
    }

    setStylesData(dataToUpdate)

    onChange({ styles: { ...dataToUpdate } })
  }

  useEffect(() => {
    if (!value) return
    if (JSON.stringify(value) !== JSON.stringify(stylesData)) {
      setStylesData(value)
    }
  }, [value])

  return (
    <Tabs.Panel id='dv-styles' className='tabPanelRoot'>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.5rem',
        }}
      >
        <FormControl
          id='original-domain'
          style={{
            paddingLeft: '0rem !import',
            width: '100%',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Show Grid</FormControl.Label>
            <Tooltip
              placement='top'
              id='tooltip-1'
              content='Toggle this option to display or hide the grid lines on the chart background.'
            >
              <Info />
            </Tooltip>
          </div>
          <Switch
            name='allow-cookies-uncontrolled'
            id='allow-cookies-uncontrolled'
            onChange={() =>
              handleStylesDataChange('showGrid', !stylesData.showGrid)
            }
            className='switchRoot'
            isChecked={stylesData.showGrid}
            isDisabled={loading}
          >
            {stylesData ? 'Show ' : 'Hide '} Grid
          </Switch>
        </FormControl>
      </Box>
      {stylesData.showGrid && (
        <Box
          style={{
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            justifyContent: 'start',
            gap: '30px',
            paddingBlock: '10px',
          }}
        >
          <FormControl id='original-domain'>
            <div className='formLabelWithIcon'>
              <FormControl.Label>Chart Bg Color</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Select the background color for the entire chart area.'
              >
                <Info />
              </Tooltip>
            </div>
            <div
              className={`bg edit`}
              onClick={() => {
                setShowColorPicker(!showColorPicker)
                setIdForColorPicker('graphBgColor')
              }}
            >
              {stylesData.graphBgColor ? (
                <div
                  className='color'
                  style={{
                    backgroundColor: stylesData.graphBgColor,
                  }}
                ></div>
              ) : (
                <span>Pick</span>
              )}
            </div>
          </FormControl>
          <span
            style={{
              height: '80px',
              width: '1px',
              backgroundColor: 'rgba(0, 0, 0, 0.12)',
            }}
          ></span>

          <FormControl id='original-domain'>
            <div className='formLabelWithIcon'>
              <FormControl.Label>Grid Bg Color</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Choose the background color for the grid area within the chart.'
              >
                <Info />
              </Tooltip>
            </div>

            <div
              className={`bg edit`}
              onClick={() => {
                setShowColorPicker(!showColorPicker)
                setIdForColorPicker('gridBgColor')
              }}
            >
              {stylesData.gridBgColor ? (
                <div
                  className='color'
                  style={{
                    backgroundColor: stylesData.gridBgColor,
                  }}
                ></div>
              ) : (
                <span>Pick</span>
              )}
            </div>
          </FormControl>
          <span
            style={{
              height: '80px',
              width: '1px',
              backgroundColor: 'rgba(0, 0, 0, 0.12)',
            }}
          ></span>
          <FormControl id='original-domain'>
            <div className='formLabelWithIcon'>
              <FormControl.Label>Grid Line Color</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Set the color for the grid lines in the chart.'
              >
                <Info />
              </Tooltip>
            </div>

            <div
              className={`bg edit`}
              onClick={() => {
                setShowColorPicker(!showColorPicker)
                setIdForColorPicker('gridLineColor')
              }}
            >
              {stylesData.gridLineColor ? (
                <div
                  className='color'
                  style={{
                    backgroundColor: stylesData.gridLineColor,
                  }}
                ></div>
              ) : (
                <span>Pick</span>
              )}
            </div>
          </FormControl>
        </Box>
      )}

      {stylesData.showGrid && (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
          }}
        >
          {/* <span
            style={{
              height: '1px',
              width: '100%',
              backgroundColor: 'rgba(0, 0, 0, 0.12)',
            }}
          ></span> */}
          <Box
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'start',
              paddingTop: '0.3rem',
            }}
          >
            <FormControl
              id='original-domain'
              style={{
                width: '50%',
              }}
            >
              <div className='formLabelWithIcon'>
                <FormControl.Label>Grid Format</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='tooltip-1'
                  content='Choose the format for the grid lines: horizontal, vertical, or both.'
                >
                  <Info />
                </Tooltip>
              </div>
              <Select
                id='optionSelect-controlled'
                name='optionSelect-controlled'
                value={stylesData.gridFormat}
                onChange={(e) =>
                  handleStylesDataChange('gridFormat', e.target.value)
                }
                placeholder='Select Title Alignment'
                isDisabled={loading}
              >
                <Select.Option value='split'>Split</Select.Option>
                <Select.Option value='horizontal'>Horizontal</Select.Option>
                <Select.Option value='vertical'>Vertical</Select.Option>
              </Select>
            </FormControl>
            <FormControl
              id='original-domain'
              style={{
                width: '50%',
              }}
            >
              <div className='formLabelWithIcon'>
                <FormControl.Label>Grid Line Style</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='tooltip-1'
                  content='Choose the type of grid lines for the chart: solid, dashed, or dotted.'
                >
                  <Info />
                </Tooltip>
              </div>
              <Select
                id='optionSelect-controlled'
                name='optionSelect-controlled'
                value={stylesData.gridLineStyle}
                onChange={(e) =>
                  handleStylesDataChange('gridLineStyle', e.target.value)
                }
                placeholder='Select Title Alignment'
                isDisabled={loading}
              >
                <Select.Option value='solid'>Solid</Select.Option>
                <Select.Option value='dashed'>Dashed</Select.Option>
                <Select.Option value='dotted'>Dotted</Select.Option>
              </Select>
            </FormControl>
          </Box>
        </div>
      )}
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
        }}
      >
        {/* <span
          style={{
            height: '1px',
            width: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.12)',
          }}
        ></span> */}
        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
            paddingTop: '0.5rem',
          }}
        >
          <FormControl
            id='original-domain'
            style={{
              width: '50%',
            }}
          >
            <div className='formLabelWithIcon'>
              <FormControl.Label>Border Width</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Set the width of the border around the chart.'
              >
                <Info />
              </Tooltip>
            </div>
            <TextInput.Group
              style={{
                width: '100%',
              }}
            >
              <TextInput
                aria-label='Content type name'
                id='content-type-name'
                placeholder='10'
                type='number'
                value={stylesData.borderWidth}
                onChange={(e) =>
                  handleStylesDataChange('borderWidth', e.target.value)
                }
                isDisabled={loading}
              />
              <IconButton
                variant='secondary'
                aria-label='Unlock'
                icon={<></>}
                style={{
                  cursor: 'default',
                }}
              >
                px
              </IconButton>
            </TextInput.Group>
          </FormControl>
          <FormControl
            id='original-domain'
            style={{
              width: '50%',
            }}
          >
            <div className='formLabelWithIcon'>
              <FormControl.Label>Border Type</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Choose the type of border for the chart: solid, dashed, or dotted.'
              >
                <Info />
              </Tooltip>
            </div>
            <Select
              id='optionSelect-controlled'
              name='optionSelect-controlled'
              value={stylesData.borderType}
              onChange={(e) =>
                handleStylesDataChange('borderType', e.target.value)
              }
              placeholder='Select border type'
              style={{
                width: '100%',
              }}
              isDisabled={loading}
            >
              <Select.Option value='solid'>Solid</Select.Option>
              <Select.Option value='dashed'>Dashed</Select.Option>
              <Select.Option value='dotted'>Dotted</Select.Option>
            </Select>
          </FormControl>
        </Box>
        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
            paddingTop: '1rem',
          }}
        >
          <FormControl id='original-domain' style={{ width: '50%' }}>
            <div className='formLabelWithIcon'>
              <FormControl.Label>Border Shadow</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Enable or disable shadow for the chart border to create a depth effect.'
              >
                <Info />
              </Tooltip>
            </div>
            <Switch
              name='allow-cookies-uncontrolled'
              id='allow-cookies-uncontrolled'
              onChange={() =>
                handleStylesDataChange(
                  'isBorderShadow',
                  !stylesData.isBorderShadow
                )
              }
              className='switchRoot'
              isChecked={stylesData.isBorderShadow}
              style={{
                paddingTop: '0.5rem',
              }}
              isDisabled={loading}
            >
              {stylesData.isBorderShadow ? 'Add ' : 'Hide '}
              border shadow
            </Switch>
          </FormControl>
          <FormControl id='original-domain' style={{ width: '50%' }}>
            <div className='formLabelWithIcon'>
              <FormControl.Label>Border Color</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content={`Select the color for the chart's border.`}
              >
                <Info />
              </Tooltip>
            </div>

            <div
              className={`bg edit`}
              onClick={() => {
                setShowColorPicker(!showColorPicker)
                setIdForColorPicker('borderColor')
              }}
            >
              {stylesData.borderColor ? (
                <div
                  className='color'
                  style={{
                    backgroundColor: stylesData.borderColor,
                  }}
                ></div>
              ) : (
                <span>Pick</span>
              )}
            </div>
          </FormControl>
        </Box>
      </div>
      <ColorPickerPopup
        isOpen={showColorPicker}
        handleClose={() => {
          setShowColorPicker(false)
        }}
        selectedColors={stylesData[idForColorPicker as keyof typeof stylesData]}
        onColorPick={(color: any) => {
          handleStylesDataChange(idForColorPicker, color.value)
          setShowColorPicker(false)
        }}
      />
    </Tabs.Panel>
  )
}

export default DvStyles
