import {
  Box,
  FormControl,
  Switch,
  Tabs,
  Tooltip,
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import Info from '../../../../assets/icons/Info'
import ColorPickerPopup from '../../Components/ColorPickerPopup'

function DvAxes(props: any) {
  const { onChange, value, loading } = props

  const [axesData, setAxesData] = useState({
    showAxes: false,
    reverseAxisType: false,
    xAxisShow: false,
    showXAxisLine: false,
    xAxisLineColor: '',
    reverseXAxis: false,
    yAxisShow: false,
    showYAxisLine: false,
    yAxisLineColor: '',
    reverseYAxis: false,
  })

  const [idForColorPicker, setIdForColorPicker] = useState('')

  const [showColorPicker, setShowColorPicker] = useState(false)

  const handleAxesDataChange = (key: string, value: string | boolean) => {
    const dataToUpdate = {
      ...axesData,
      [key]: value,
    }

    setAxesData(dataToUpdate)

    onChange({ axes: { ...dataToUpdate } })
  }

  useEffect(() => {
    if (!value) return
    if (JSON.stringify(value) !== JSON.stringify(axesData)) {
      setAxesData(value)
    }
  }, [value])

  return (
    <Tabs.Panel id='dv-axes' className='tabPanelRoot'>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.3rem',
        }}
      >
        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
          }}
        >
          <Box
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'start',
            }}
          >
            <FormControl
              id='pri-dv-axes'
              style={{
                paddingLeft: '0rem !import',
              }}
            >
              <div className='formLabelWithIcon'>
                <FormControl.Label>Show Axes</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='dv-pri-axes-tooltip'
                  content='Toggle this option to display or hide both X and Y axes on the chart.'
                >
                  <Info />
                </Tooltip>
              </div>
              <Switch
                name='showAxes dv axes'
                id='showAxes-dv-pri-axes'
                onChange={() =>
                  handleAxesDataChange('showAxes', !axesData.showAxes)
                }
                className='switchRoot'
                isChecked={axesData.showAxes}
                isDisabled={loading}
              >
                {axesData.showAxes ? 'Show ' : 'Hide '}
                Axes
              </Switch>
            </FormControl>
          </Box>
          {axesData.showAxes && (
            <Box
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'start',
              }}
            >
              <FormControl
                id='pri-dv-reverse-axis-type'
                style={{
                  paddingLeft: '0rem !import',
                }}
              >
                <div className='formLabelWithIcon'>
                  <FormControl.Label>Reverse Axis Type</FormControl.Label>
                  <Tooltip
                    placement='top'
                    id='dv-pri-reverse-axis-type-tooltip'
                    content='Select the type of axis inversion for the chart: category or value.'
                  >
                    <Info />
                  </Tooltip>
                </div>
                <Switch
                  name='showAxes dv reverse x-axis direction'
                  id='showAxes-dv-pri-reverse-x-axis-direction'
                  onChange={() =>
                    handleAxesDataChange(
                      'reverseAxisType',
                      !axesData.reverseAxisType
                    )
                  }
                  className='switchRoot'
                  isChecked={axesData.reverseAxisType}
                  isDisabled={loading}
                >
                  {axesData.reverseAxisType ? 'Y-axis type-' : 'X-axis type-'}
                  category
                </Switch>
              </FormControl>
            </Box>
          )}
        </Box>
      </Box>
      {axesData.showAxes && (
        <>
          <Box
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'start',
            }}
          >
            <Box
              className='w-50'
              style={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'start',
                alignItems: 'start',
                paddingTop: '0.3rem',
              }}
            >
              <FormControl
                id='pri-dv-x-axis'
                style={{
                  paddingLeft: '0rem !import',
                }}
              >
                <div className='formLabelWithIcon'>
                  <FormControl.Label>Show X-axis</FormControl.Label>
                  <Tooltip
                    placement='top'
                    id='dv-pri-x-axis-tooltip'
                    content='Toggle this option to display or hide the X-axis on the chart.'
                  >
                    <Info />
                  </Tooltip>
                </div>
                <Switch
                  name='showAxes dv x-axis'
                  id='showAxes-dv-pri-x-axis'
                  onChange={() =>
                    handleAxesDataChange('xAxisShow', !axesData.xAxisShow)
                  }
                  className='switchRoot'
                  isChecked={axesData.xAxisShow}
                  isDisabled={loading}
                >
                  {axesData.xAxisShow ? 'Show ' : 'Hide '}
                  X-Axis
                </Switch>
              </FormControl>
              {axesData?.xAxisShow && (
                <>
                  <FormControl
                    id='pri-dv-x-axis-line'
                    style={{
                      paddingLeft: '0rem !import',
                    }}
                  >
                    <div className='formLabelWithIcon'>
                      <FormControl.Label>Show X-axis Line</FormControl.Label>
                      <Tooltip
                        placement='top'
                        id='dv-pri-x-axis-line-tooltip'
                        content='Toggle this option to display or hide the line representing the X-axis.'
                      >
                        <Info />
                      </Tooltip>
                    </div>
                    <Switch
                      name='showAxes dv y-axis-line'
                      id='showAxes-dv-pri-y-axis-line'
                      onChange={() =>
                        handleAxesDataChange(
                          'showXAxisLine',
                          !axesData.showXAxisLine
                        )
                      }
                      className='switchRoot'
                      isChecked={axesData.showXAxisLine}
                      isDisabled={loading}
                    >
                      {axesData.showXAxisLine ? 'Show ' : 'Hide '}
                      X-Axis Line
                    </Switch>
                  </FormControl>
                  <FormControl id='dv-pri-x-axis-lineColor'>
                    <div className='formLabelWithIcon'>
                      <FormControl.Label>X-axis Line Color</FormControl.Label>
                      <Tooltip
                        placement='top'
                        id='dv-pri-x-axis-lineColor-tooltip'
                        content='Select the color for the X-axis line.'
                      >
                        <Info />
                      </Tooltip>
                    </div>
                    <div
                      className={`bg edit`}
                      onClick={() => {
                        setShowColorPicker(!showColorPicker)
                        setIdForColorPicker('xAxisLineColor')
                      }}
                    >
                      {axesData.xAxisLineColor ? (
                        <div
                          className='color'
                          style={{
                            backgroundColor: axesData.xAxisLineColor,
                          }}
                        ></div>
                      ) : (
                        <span>Pick</span>
                      )}
                    </div>
                  </FormControl>

                  <FormControl
                    id='pri-dv-reverse-x-axis-direction+'
                    style={{
                      paddingLeft: '0rem !import',
                    }}
                  >
                    <div className='formLabelWithIcon'>
                      <FormControl.Label>Reverse X-axis</FormControl.Label>
                      <Tooltip
                        placement='top'
                        id='dv-pri-reverse-x-axis-direction-tooltip'
                        content='Enable this option to invert the direction of the X-axis.'
                      >
                        <Info />
                      </Tooltip>
                    </div>
                    <Switch
                      name='showAxes dv reverse x-axis direction'
                      id='showAxes-dv-pri-reverse-x-axis-direction'
                      onChange={() =>
                        handleAxesDataChange(
                          'reverseXAxis',
                          !axesData.reverseXAxis
                        )
                      }
                      className='switchRoot'
                      isChecked={axesData.reverseXAxis}
                      isDisabled={loading}
                    >
                      {axesData.reverseXAxis ? 'Inverse ' : 'LeftToRight '}
                    </Switch>
                  </FormControl>
                </>
              )}
            </Box>
            <Box
              className='w-50'
              style={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'start',
                alignItems: 'start',
                paddingTop: '0.3rem',
              }}
            >
              <FormControl
                id='pri-dv-y-axis'
                style={{
                  paddingLeft: '0rem !import',
                }}
              >
                <div className='formLabelWithIcon'>
                  <FormControl.Label>Show Y-axis</FormControl.Label>
                  <Tooltip
                    placement='top'
                    id='dv-pri-y-axis-tooltip'
                    content='Toggle this option to display or hide the Y-axis on the chart.'
                  >
                    <Info />
                  </Tooltip>
                </div>
                <Switch
                  name='showAxes dv y-axis'
                  id='showAxes-dv-pri-y-axis'
                  onChange={() =>
                    handleAxesDataChange('yAxisShow', !axesData.yAxisShow)
                  }
                  className='switchRoot'
                  isChecked={axesData.yAxisShow}
                  isDisabled={loading}
                >
                  {axesData.yAxisShow ? 'Show ' : 'Hide '}
                  Y-Axis
                </Switch>
              </FormControl>
              {axesData?.yAxisShow && (
                <>
                  <FormControl
                    id='pri-dv-y-axis-line'
                    style={{
                      paddingLeft: '0rem !import',
                    }}
                  >
                    <div className='formLabelWithIcon'>
                      <FormControl.Label>Show Y-axis Line</FormControl.Label>
                      <Tooltip
                        placement='top'
                        id='dv-pri-y-axis-line-tooltip'
                        content='Toggle this option to display or hide the line representing the Y-axis.'
                      >
                        <Info />
                      </Tooltip>
                    </div>
                    <Switch
                      name='showAxes dv y-axis-line'
                      id='showAxes-dv-pri-y-axis-line'
                      onChange={() =>
                        handleAxesDataChange(
                          'showYAxisLine',
                          !axesData.showYAxisLine
                        )
                      }
                      className='switchRoot'
                      isChecked={axesData.showYAxisLine}
                      isDisabled={loading}
                    >
                      {axesData.showYAxisLine ? 'Show ' : 'Hide '}
                      Y-Axis Line
                    </Switch>
                  </FormControl>
                  <FormControl id='dv-pri-x-axis-lineColor'>
                    <div className='formLabelWithIcon'>
                      <FormControl.Label>Y-axis Line Color</FormControl.Label>
                      <Tooltip
                        placement='top'
                        id='dv-pri-y-axis-lineColor-tooltip'
                        content='Select the color for the Y-axis line.'
                      >
                        <Info />
                      </Tooltip>
                    </div>
                    <div
                      className={`bg edit`}
                      onClick={() => {
                        setShowColorPicker(!showColorPicker)
                        setIdForColorPicker('yAxisLineColor')
                      }}
                    >
                      {axesData.yAxisLineColor ? (
                        <div
                          className='color'
                          style={{
                            backgroundColor: axesData.yAxisLineColor,
                          }}
                        ></div>
                      ) : (
                        <span>Pick</span>
                      )}
                    </div>
                  </FormControl>

                  <FormControl
                    id='pri-dv-reverse-y-axis-direction+'
                    style={{
                      paddingLeft: '0rem !import',
                    }}
                  >
                    <div className='formLabelWithIcon'>
                      <FormControl.Label>Reverse Y-axis</FormControl.Label>
                      <Tooltip
                        placement='top'
                        id='dv-pri-reverse-y-axis-direction-tooltip'
                        content='Enable this option to invert the direction of the Y-axis.'
                      >
                        <Info />
                      </Tooltip>
                    </div>
                    <Switch
                      name='showAxes dv reverse x-axis direction'
                      id='showAxes-dv-pri-reverse-x-axis-direction'
                      onChange={() =>
                        handleAxesDataChange(
                          'reverseYAxis',
                          !axesData.reverseYAxis
                        )
                      }
                      className='switchRoot'
                      isChecked={axesData.reverseYAxis}
                      isDisabled={loading}
                    >
                      {axesData.reverseYAxis ? 'Inverse' : 'BottomToTop'}
                    </Switch>
                  </FormControl>
                </>
              )}
            </Box>
          </Box>
        </>
      )}
      <ColorPickerPopup
        isOpen={showColorPicker}
        handleClose={() => {
          setShowColorPicker(false)
        }}
        selectedColors={axesData[idForColorPicker as keyof typeof axesData]}
        onColorPick={(color: any) => {
          handleAxesDataChange(idForColorPicker, color.value)
          setShowColorPicker(false)
        }}
      />
    </Tabs.Panel>
  )
}

export default DvAxes
