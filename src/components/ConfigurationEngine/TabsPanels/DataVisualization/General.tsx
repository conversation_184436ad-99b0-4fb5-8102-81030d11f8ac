import {
  Box,
  FormControl,
  Select,
  Switch,
  Tabs,
  Tooltip,
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import Info from '../../../../assets/icons/Info'

function DvGeneral(props: any) {
  const { onChange, value, loading } = props

  const [generalData, setGeneralData] = useState({
    showTitle: false,
    titleAlignment: 'left',
    isZoomable: false,
    showLegend: false,
    legendAlignment: 'left',
    legendOrientation: 'horizontal',
  })

  const handleGeneralDataChange = (key: string, value: string | boolean) => {
    const dataToUpdate = {
      ...generalData,
      [key]: value,
    }

    setGeneralData(dataToUpdate)

    onChange({ general: { ...dataToUpdate } })
  }

  useEffect(() => {
    if (!value) return
    if (JSON.stringify(value) !== JSON.stringify(generalData)) {
      setGeneralData(value)
    }
  }, [value])

  return (
    <Tabs.Panel id='dv-general' className='tabPanelRoot'>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.5rem',
        }}
      >
        <FormControl
          id='original-domain'
          style={{
            paddingLeft: '0rem !import',
            width: '30%',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Show Title</FormControl.Label>
            <Tooltip
              placement='top'
              id='tooltip-1'
              content='Toggle this option to display or hide the title of the chart.'
            >
              <Info />
            </Tooltip>
          </div>
          <Switch
            name='allow-cookies-uncontrolled'
            id='allow-cookies-uncontrolled'
            onChange={() =>
              handleGeneralDataChange('showTitle', !generalData.showTitle)
            }
            className='switchRoot'
            isChecked={generalData.showTitle}
            style={{
              paddingTop: '0.5rem',
            }}
            isDisabled={loading}
          >
            {generalData.showTitle ? 'Show ' : 'Hide '}
            title
          </Switch>
        </FormControl>
        {generalData.showTitle && (
          <FormControl
            id='original-domain'
            style={{
              width: '70%',
            }}
          >
            <div className='formLabelWithIcon'>
              <FormControl.Label>Title Alignment</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Choose the legendAlignment for the chart title: left, center, or right.'
              >
                <Info />
              </Tooltip>
            </div>
            <Select
              id='optionSelect-controlled'
              name='optionSelect-controlled'
              value={generalData.titleAlignment}
              onChange={(e) =>
                handleGeneralDataChange('legendAlignment', e.target.value)
              }
              placeholder='Select Title Alignment'
              style={{
                width: '100%',
              }}
              isDisabled={loading}
            >
              <Select.Option value='left'>Left</Select.Option>
              <Select.Option value='center'>Center</Select.Option>
              <Select.Option value='right'>Right</Select.Option>
            </Select>
          </FormControl>
        )}
      </Box>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.5rem',
        }}
      >
        <FormControl
          id='original-domain'
          style={{
            paddingLeft: '0rem !import',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Is Graph Zoomable</FormControl.Label>
            <Tooltip
              placement='top'
              id='tooltip-1'
              content='Enable or disable zooming functionality for the graph, allowing users to focus on specific data points.'
            >
              <Info />
            </Tooltip>
          </div>
          <Switch
            name='allow-cookies-uncontrolled'
            id='allow-cookies-uncontrolled'
            onChange={() =>
              handleGeneralDataChange('isZoomable', !generalData.isZoomable)
            }
            isChecked={generalData.isZoomable}
            className='switchRoot'
            isDisabled={loading}
          >
            {generalData.isZoomable ? 'Enable ' : 'Disable '}
            graph zoom
          </Switch>
        </FormControl>
      </Box>

      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.5rem',
        }}
      >
        <FormControl
          id='original-domain'
          style={{
            paddingLeft: '0rem !import',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Show Legend</FormControl.Label>
            <Tooltip
              placement='top'
              id='tooltip-1'
              content='Toggle this option to display or hide the legend on the chart.'
            >
              <Info />
            </Tooltip>
          </div>
          <Switch
            name='allow-cookies-uncontrolled'
            id='allow-cookies-uncontrolled'
            onChange={() =>
              handleGeneralDataChange('showLegend', !generalData.showLegend)
            }
            isChecked={generalData.showLegend}
            className='switchRoot'
            isDisabled={loading}
          >
            {generalData.showLegend ? 'Show ' : 'Hide '}
            Legend
          </Switch>
        </FormControl>
      </Box>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.5rem',
        }}
      >
        {generalData.showLegend && (
          <FormControl
            id='original-domain'
            style={{
              width: '50%',
            }}
          >
            <div className='formLabelWithIcon'>
              <FormControl.Label>Legend Alignment</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Choose the legendAlignment for the legend: left, center, or right.'
              >
                <Info />
              </Tooltip>
            </div>
            <Select
              id='optionSelect-controlled'
              name='optionSelect-controlled'
              value={generalData.legendAlignment}
              onChange={(e) =>
                handleGeneralDataChange('legendAlignment', e.target.value)
              }
              placeholder='Select Title Alignment'
              isDisabled={loading}
            >
              <Select.Option value='left'>Left</Select.Option>
              <Select.Option value='center'>Center</Select.Option>
              <Select.Option value='right'>Right</Select.Option>
            </Select>
          </FormControl>
        )}
        {generalData.showLegend && (
          <FormControl
            id='original-domain'
            style={{
              width: '50%',
            }}
          >
            <div className='formLabelWithIcon'>
              <FormControl.Label>Legend Orientation</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Select the legendOrientation for the legend: horizontal or vertical.'
              >
                <Info />
              </Tooltip>
            </div>
            <Select
              id='optionSelect-controlled'
              name='optionSelect-controlled'
              value={generalData.legendOrientation}
              onChange={(e) =>
                handleGeneralDataChange('legendOrientation', e.target.value)
              }
              placeholder='Select Legend Orientation'
              isDisabled={loading}
            >
              <Select.Option value='horizontal'>Horizontal</Select.Option>
              <Select.Option value='vertical'>Vertical</Select.Option>
            </Select>
          </FormControl>
        )}
      </Box>
    </Tabs.Panel>
  )
}

export default DvGeneral
