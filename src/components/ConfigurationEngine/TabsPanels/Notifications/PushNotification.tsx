import {
  AssetCard,
  AssetStatus,
  Box,
  Button,
  MenuItem,
  ModalControls,
  Select,
  Text,
  TextInput,
} from '@contentful/f36-components'
import { LinkIcon, PlusIcon } from '@contentful/f36-icons'
import { init } from 'contentful-ui-extensions-sdk'
import React, { useContext, useEffect, useState } from 'react'
import { GlobalContext } from '../../../../contexts/globalContext'
import {
  CreatePushNotification,
  generateRandomId,
  sendMessage,
} from '../../../../globals/firebase/utils'
import {
  createOrUpdateAsset,
  getAssetData,
  getEntryDataById,
} from '../../../../globals/utils'
import NextButton from '../../../Buttons/NextButton'
import PrevButton from '../../../Buttons/PrevButton'
import SaveButton from '../../../Buttons/SaveButton'
import ModalConfirm from '../../../ConfirmModal'
import ProgressBar from '../../../ProgressBar'
import FormControlComp from '../../../Shared/FormControlComp'
import CustomCard from './CustomCard'
import { fetchEntryDetails } from './utils'

function PushNotification() {
  const { currentLocale } = useContext(GlobalContext)

  const [status, setStatus] = useState('')

  const [showConfirmBox, setShowConfirmBox] = useState(false)

  const [titleSource, setTitleSource] = useState('title')

  const [descriptionSource, setDescriptionSource] = useState('seoDescription')

  const [activeStep, setActiveStep] = useState(0)

  const [extension, setExtension] = useState<any>(null)

  const [isLoading, setIsLoading] = useState(false)

  const [externalLink, setExternalLink] = useState('')

  const [title, setTitle] = useState('')

  const [url, setURL] = useState('')

  const [description, setDescription] = useState('')

  const [selectedCTA, setSelectedCTA] = useState<any>(null)

  const [thumbnail, setThumbnail] = useState({
    url: '',
    status: '',
  })
  const [selectedPage, setSelectedPage] = useState<any>(null)

  const [typeOfNotification, setTypeOfNotification] = useState('')

  const clearAllStates = () => {
    setSelectedPage(null)
    setSelectedCTA(null)
    setThumbnail({
      url: '',
      status: '',
    })
    setTitle('')
    setURL('')
    setDescription('')
    setTypeOfNotification('')
    setStatus('')
    setExternalLink('')
    setActiveStep(0)
  }

  const handleFileChange = async (event: any) => {
    setIsLoading(true)
    const file = event.target.files[0]

    if (!file) return

    //Create or update asset
    const asset: any = await createOrUpdateAsset(file, currentLocale)

    setThumbnail({
      url: asset?.fields?.file?.['en-CA'].url,
      status: asset.sys.fieldStatus?.['*']?.['en-CA'],
    })

    setIsLoading(false)
  }

  const selectSingleAsset = async () => {
    if (!extension) return

    const asset = await extension.dialogs.selectSingleAsset()

    if (!asset) return

    setThumbnail({
      url: asset.fields?.file?.['en-CA'].url,
      status: asset.sys.fieldStatus?.['*']?.['en-CA'],
    })
  }

  const SingleEntrySelector = async (contentTypes: string[]) => {
    if (!extension) return

    const selected = await extension.dialogs.selectSingleEntry({
      locale: extension.locales.default,
      contentTypes: contentTypes,
    })

    if (!selected) return

    const detailedEntry = await fetchEntryDetails(selected?.sys?.id, extension)

    return detailedEntry
  }

  const CTASelector = async () => {
    const contentTypes = ['linkComponent']
    const selected = await SingleEntrySelector(contentTypes)

    setSelectedCTA(selected)

    const internalLinkEntryId =
      selected?.fields?.internalLink?.['en-CA']?.sys?.id

    const externalLink = selected?.fields?.externalLink?.['en-CA']

    if (externalLink) setExternalLink(externalLink)

    if (!internalLinkEntryId) return

    const internalPage = await getEntryDataById(internalLinkEntryId).then(
      (res) => res
    )
    console.log('internalPage: ', internalPage)

    const title = internalPage?.fields?.title?.['en-CA']

    setTitle(title)

    const description = internalPage?.fields?.seoDescription?.['en-CA']

    setDescription(description)

    const pageThumbnail: any = await getAssetData(
      internalPage?.fields?.pageThumbnail?.['en-CA']?.sys?.id
    ).then((res) => res)

    const d = {
      url: pageThumbnail?.fields?.file?.['en-CA'].url,
      status: pageThumbnail?.sys?.fieldStatus?.['*']?.['en-CA'],
    }

    setThumbnail(d)
  }

  const pageSelector = async () => {
    const contentTypes = ['page']

    const selected = await SingleEntrySelector(contentTypes)

    const title = selected?.fields?.title?.['en-CA']

    const description = selected?.fields?.seoDescription?.['en-CA']

    setTitle(title)
    setDescription(description)

    setSelectedPage(selected)

    const pageThumbnail: any = await getAssetData(
      selected?.fields?.pageThumbnail?.['en-CA']?.sys?.id
    ).then((res) => res)

    const d = {
      url: pageThumbnail?.fields?.file?.['en-CA'].url,
      status: pageThumbnail?.sys?.fieldStatus?.['*']?.['en-CA'],
    }

    setThumbnail(d)
  }

  const handleClose = () => {
    setShowConfirmBox(false)
  }

  const handleOnConfirm = async () => {
    setShowConfirmBox(false)
    await CreateRealTimeNotification()
  }

  const CreateRealTimeNotification = async () => {
    setIsLoading(true)

    let payload: any = {
      title: title || selectedPage?.fields?.seoTitle?.['en-CA'],
      body: description || selectedPage?.fields?.seoDescription?.['en-CA'],
      icon: thumbnail.url,
      url: url || externalLink || selectedPage?.fields?.slug?.['en-CA'],
      id: generateRandomId(),
    }

    const argsArray = [
      'domainAltusGroupCom',
      'domainFinanceActiveCom',
      'domainVerifinoCom',
      'domainReonomyCom',
      'domainOne11Com',
    ]

    if (typeOfNotification === '1') {
      const promises = argsArray.map((arg) => {
        return CreatePushNotification({ ...payload, domain: arg })
      })

      const results = await Promise.all(promises)
      const allSuccessful = results.every((res) => res)
      setStatus(allSuccessful ? 'success' : 'error')
    }

    if (typeOfNotification === '2') {
      const promises = argsArray.map((arg) => {
        return sendMessage(payload, arg)
      })

      const results = await Promise.all(promises)
      const allSuccessful = results.every((res) => res)
      setStatus(allSuccessful ? 'success' : 'error')
    }

    setIsLoading(false)

    setTimeout(() => {
      clearAllStates()
    }, 5000)
  }

  useEffect(() => {
    init((ext: any) => {
      ext?.window?.startAutoResizer()
      setExtension(ext)
    })
  }, [])

  return (
    <Box
      style={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        // position: 'relative',
        height: '100%',
      }}
    >
      <Box style={{ width: '100%', height: activeStep === 0 ? '70%' : 'auto' }}>
        <Box
          style={{
            height: '4px',
          }}
        />
        <ProgressBar completedSteps={activeStep} totalSteps={2} />
        <Box
          style={{
            height: '4px',
          }}
        />
        {activeStep !== 2 && (
          <Box
            style={{
              paddingInline: '1rem',
            }}
          >
            <h2 className='modalTitle'>
              {activeStep === 0
                ? 'Way of Push Notification'
                : 'Add Source & Details'}
            </h2>
            <Box
              style={{
                paddingBlock: '0.5rem',
              }}
            >
              <p className='modalSubTitle'>
                <span className='subtitleHeading'>Description: </span>

                <span>
                  {activeStep === 0
                    ? 'Choose the way of push notification to be sent'
                    : 'Select the Notification Source and add related details'}
                </span>
              </p>
            </Box>
            <hr
              className='endHorizontalLine'
              style={{
                width: '100%',
              }}
            />
          </Box>
        )}
        {activeStep === 0 && (
          <Box
            style={{
              width: '100%',
              height: '96%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'column',
            }}
          >
            <FormControlComp
              label='Type of Notification'
              tooltip=''
              isRequired
              className='smallSelect'
            >
              <Select
                value={typeOfNotification}
                onChange={(e) => setTypeOfNotification(e.target.value)}
              >
                <Select.Option value='' isDisabled>
                  Select
                </Select.Option>
                {TypeOfNotifications.map((option) => (
                  <Select.Option value={option.value}>
                    {option.title}
                  </Select.Option>
                ))}
              </Select>
            </FormControlComp>
          </Box>
        )}
        {activeStep === 1 && (
          <Box
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'start',
              flexDirection: 'row',
              gap: '15px',
              height: '96%',
            }}
          >
            <Box
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'start',
                flexDirection: 'column',
              }}
            >
              <Box
                style={{
                  paddingInline: 15,
                  width: '100%',
                }}
              >
                <h6
                  style={{
                    marginBlock: 10,
                  }}
                >
                  Source
                </h6>
                <hr
                  className='endHorizontalLine'
                  style={{
                    width: '100%',
                  }}
                />
              </Box>
              {!selectedCTA && !url && (
                <FormControlComp label='Page Source' tooltip='' isRequired>
                  {selectedPage ? (
                    <CustomCard
                      data={selectedPage}
                      onRemoveEntry={() => setSelectedPage(null)}
                      field={'formFloating'}
                    />
                  ) : (
                    <Button
                      onClick={pageSelector}
                      variant='secondary'
                      startIcon={<LinkIcon />}
                      size='small'
                    >
                      Link existing entry
                    </Button>
                  )}
                </FormControlComp>
              )}
              <Box
                style={{
                  height: '14px',
                }}
              />
              {!selectedPage && !url && (
                <FormControlComp label='CTA Source' tooltip='' isRequired>
                  {selectedCTA ? (
                    <CustomCard
                      data={selectedCTA}
                      onRemoveEntry={() => setSelectedCTA(null)}
                      field={'formFloating'}
                    />
                  ) : (
                    <Button
                      onClick={CTASelector}
                      variant='secondary'
                      startIcon={<LinkIcon />}
                      size='small'
                    >
                      Select existing entry
                    </Button>
                  )}
                </FormControlComp>
              )}
              <Box
                style={{
                  height: '14px',
                }}
              />
              {!selectedPage && !selectedCTA && (
                <FormControlComp label='External URL' tooltip='' isRequired>
                  <TextInput
                    value={url}
                    placeholder='Enter URL'
                    onChange={(e) => setURL(e.target.value)}
                  />
                </FormControlComp>
              )}

              {selectedPage && (
                <>
                  <Box
                    style={{
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'center',
                      paddingInline: 15,
                      gap: '10px',
                      width: '100%',
                    }}
                  >
                    <p
                      style={{
                        width: '250px',
                        fontSize: '15px',
                        fontWeight: '500',
                      }}
                    >
                      Notification Title From:
                    </p>
                    <Select
                      onChange={(e) => {
                        setTitleSource(e.target.value)
                        const v = e.target.value
                        const title = selectedPage?.fields?.[v]?.['en-CA']
                        if (title) setTitle(title)
                      }}
                      value={titleSource}
                    >
                      <Select.Option value='' isDisabled>
                        Select
                      </Select.Option>
                      <Select.Option value='title'>Page Title</Select.Option>
                      <Select.Option value='seoTitle'>SEO Title</Select.Option>
                      <Select.Option value='shortTitle'>
                        Algolia Title
                      </Select.Option>
                    </Select>
                  </Box>
                  <Box
                    style={{
                      height: '10px',
                    }}
                  />
                  <Box
                    style={{
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'center',
                      paddingInline: 15,
                      gap: '10px',
                      width: '100%',
                    }}
                  >
                    <p
                      style={{
                        width: '250px',
                        fontSize: '15px',
                        fontWeight: '500',
                      }}
                    >
                      Notification Description From:
                    </p>
                    <Select
                      onChange={(e) => {
                        setDescriptionSource(e.target.value)
                        const v = e.target.value
                        const title = selectedPage?.fields?.[v]?.['en-CA']
                        if (title) setDescription(title)
                      }}
                      value={descriptionSource}
                    >
                      <Select.Option value='' isDisabled>
                        Select
                      </Select.Option>
                      <Select.Option value='title'>Page Title</Select.Option>
                      <Select.Option value='seoTitle'>SEO Title</Select.Option>
                      <Select.Option value='shortTitle'>
                        Algolia Title
                      </Select.Option>
                      <Select.Option value='seoDescription'>
                        SEO Description
                      </Select.Option>
                    </Select>
                  </Box>
                </>
              )}
            </Box>
            <Box
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'start',
                flexDirection: 'column',
              }}
            >
              <Box
                style={{
                  paddingInline: 15,
                  width: '100%',
                }}
              >
                <h6
                  style={{
                    marginBlock: 10,
                  }}
                >
                  Details
                </h6>
                <hr
                  className='endHorizontalLine'
                  style={{
                    width: '100%',
                  }}
                />
              </Box>
              <FormControlComp label='Thumbnail Image' tooltip=''>
                {thumbnail.url ? (
                  <AssetCard
                    status={thumbnail.status as AssetStatus}
                    type='image'
                    // title='Everest'
                    src={thumbnail.url}
                    size='small'
                    actions={[
                      <MenuItem
                        key='delete'
                        onClick={() => {
                          setThumbnail({
                            url: '',
                            status: '',
                          })
                        }}
                      >
                        Remove
                      </MenuItem>,
                    ]}
                  />
                ) : (
                  <div className='dv-inputDiv'>
                    <div className={`dv-newFile `}>
                      <input
                        type='file'
                        hidden
                        id='new-file'
                        onChange={handleFileChange}
                        disabled={isLoading}
                      />
                      <Button
                        variant='secondary'
                        startIcon={<PlusIcon />}
                        size='small'
                        onClick={() =>
                          document.getElementById('new-file')?.click()
                        }
                        isLoading={isLoading}
                      >
                        Add new Asset
                      </Button>
                    </div>
                    <Button
                      variant='secondary'
                      startIcon={<LinkIcon />}
                      size='small'
                      onClick={selectSingleAsset}
                      isDisabled={isLoading}
                    >
                      Add existing Asset
                    </Button>
                  </div>
                )}
              </FormControlComp>
              <Box
                style={{
                  height: '10px',
                }}
              />
              <FormControlComp label='Title' tooltip='' isRequired>
                <TextInput
                  value={title}
                  placeholder='Enter Title'
                  onChange={(e) => setTitle(e.target.value)}
                />
              </FormControlComp>
              <Box
                style={{
                  height: '10px',
                }}
              />
              <FormControlComp label='Description' tooltip='' isRequired>
                <TextInput
                  value={description}
                  placeholder='Enter Description'
                  onChange={(e) => setDescription(e.target.value)}
                />
              </FormControlComp>
            </Box>
          </Box>
        )}
        {activeStep === 2 && (
          <Box
            className='summaryRoot formRoot'
            style={{
              width: '100%',
            }}
          >
            <h6
              style={{
                marginTop: 0,
                marginBottom: 10,
              }}
            >
              Summary
            </h6>
            <p className='summaryTitle'>
              <b>
                {typeOfNotification === '1'
                  ? 'Real Time On Page'
                  : 'Instant Browser'}{' '}
                Notification{' '}
              </b>{' '}
              will be created with the following details.
              <br /> If everything looks good, click Apply to send
              thenotification. You can always go back to update / amend.
            </p>
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '15px',
                paddingTop: '30px',
                width: '100%',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'start',
                  alignItems: 'start',
                  width: '100%',
                }}
              >
                <span
                  className='domainName'
                  style={{
                    fontSize: '15px',
                    color: '#667082',
                  }}
                >
                  Title:
                </span>
                <span
                  className='SlugName'
                  style={{
                    fontWeight: '500',
                    fontSize: '15px',
                    width: 'auto',
                    maxWidth: '90%',
                  }}
                >
                  {title}
                </span>
              </div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'start',
                  alignItems: 'start',
                  width: '100%',
                }}
              >
                <span
                  className='domainName'
                  style={{
                    fontSize: '15px',
                    color: '#667082',
                  }}
                >
                  Description:
                </span>
                <span
                  className='SlugName'
                  style={{
                    fontWeight: '500',
                    fontSize: '15px',
                    width: 'auto',
                    maxWidth: '90%',
                  }}
                >
                  {description}
                </span>
              </div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'start',
                  alignItems: 'start',
                  width: '100%',
                }}
              >
                <span
                  className='domainName'
                  style={{
                    fontSize: '15px',
                    color: '#667082',
                  }}
                >
                  URL:
                </span>
                <span
                  className='SlugName'
                  style={{
                    fontWeight: '500',
                    fontSize: '15px',
                    width: 'auto',
                    maxWidth: '90%',
                  }}
                >
                  {url || externalLink || selectedPage.fields?.slug?.['en-CA']}
                </span>
              </div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'start',
                  alignItems: 'start',
                  width: '100%',
                }}
              >
                <span
                  className='domainName'
                  style={{
                    fontSize: '15px',
                    color: '#667082',
                  }}
                >
                  Thumbnail:
                </span>
                <span
                  className='SlugName'
                  style={{
                    fontWeight: '500',
                    fontSize: '15px',
                    width: 'auto',
                    maxWidth: '90%',
                  }}
                >
                  <AssetCard
                    status={thumbnail.status as AssetStatus}
                    type='image'
                    // title='Everest'
                    src={thumbnail.url}
                    size='small'
                  />
                </span>
              </div>
              {/* <div
                style={{
                  display: 'flex',
                  justifyContent: 'end',
                  alignItems: 'end',
                  width: '100%',
                }}
              >
                <span
                  className='domainName'
                  style={{
                    fontSize: '15px',
                    color: '#667082',
                  }}
                >
                  URL:
                </span>
                <span
                  className='SlugName'
                  style={{
                    fontWeight: '500',
                    fontSize: '15px',
                    width: 'auto',
                  }}
                >
                  {url || externalLink || selectedPage.fields?.slug?.['en-CA']}
                </span>
              </div> */}
            </div>
          </Box>
        )}
      </Box>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'end',
          alignItems: 'center',
        }}
      >
        <hr
          className='endHorizontalLine'
          style={{
            width: '100%',
          }}
        />
        <ModalControls
          style={{
            paddingRight: '1rem',
            paddingBottom: '1rem',
          }}
        >
          {status === 'success' ? (
            <Text fontColor='green500'> Notification created successfully</Text>
          ) : status === 'error' ? (
            <Text fontColor='red500'> Notification creation failed</Text>
          ) : (
            <></>
          )}
          {activeStep === 0 ? (
            <NextButton
              helpText='Next'
              onClick={() => setActiveStep(1)}
              isDisabled={!typeOfNotification}
            />
          ) : activeStep === 1 ? (
            <>
              <PrevButton
                helpText='Amend/Update'
                onClick={() => setActiveStep(0)}
              />
              <NextButton
                helpText='Next'
                onClick={() => setActiveStep(2)}
                isDisabled={!typeOfNotification}
              />
            </>
          ) : (
            <>
              <PrevButton
                helpText='Amend/Update'
                onClick={() => setActiveStep(1)}
              />
              <SaveButton
                helpText='Create Notification'
                onClick={() => {
                  setShowConfirmBox(true)
                }}
                isLoading={isLoading}
                // isDisabled={!checkSecondStepDisable()}
              />
            </>
          )}
        </ModalControls>
      </Box>
      <ModalConfirm
        children={
          <p
            style={{
              fontSize: '15x',
            }}
          >
            Once you create a notification, it can't be undone.
          </p>
        }
        handleClose={handleClose}
        onConfirm={handleOnConfirm}
        open={showConfirmBox}
      />
    </Box>
  )
}

export default PushNotification

const TypeOfNotifications = [
  {
    title: 'Real Time On Page Notification',
    value: 1,
  },
  {
    title: 'Instant Browser Notification',
    value: 2,
  },
]
