import {
  AssetCard,
  AssetStatus,
  Box,
  Button,
  MenuItem,
  ModalControls,
  Select,
  Switch,
  Text,
  TextInput,
  Tooltip,
} from '@contentful/f36-components'
import { LinkIcon, PlusIcon } from '@contentful/f36-icons'
import { init } from 'contentful-ui-extensions-sdk'
import React, { useContext, useEffect, useState } from 'react'
import Info from '../../../../assets/icons/Info'
import { GlobalContext } from '../../../../contexts/globalContext'
import {
  CreatePushNotification,
  generateRandomId,
  sendMessage,
} from '../../../../globals/firebase/utils'
import {
  createContentEntry,
  createOrUpdateAsset,
  getAssetData,
  getDomainDataByDomainName,
  getEntryDataById,
} from '../../../../globals/utils'
import NextButton from '../../../Buttons/NextButton'
import PrevButton from '../../../Buttons/PrevButton'
import SaveButton from '../../../Buttons/SaveButton'
import ModalConfirm from '../../../ConfirmModal'
import ProgressBar from '../../../ProgressBar'
import FormControlComp from '../../../Shared/FormControlComp'
import CustomCard from './CustomCard'
import './style.scss'
import { fetchEntryDetails } from './utils'

function NotificationStepperForm({ domain }: { domain: string }) {
  const { currentLocale } = useContext(GlobalContext)

  const [status, setStatus] = useState('')

  const [activeStep, setActiveStep] = useState(0)

  const [extension, setExtension] = useState<any>(null)

  const [isLoading, setIsLoading] = useState(false)

  const [typeOfPost, setTypeOfPost] = useState('')

  const [internalName, setInternalName] = useState('')

  const [externalLink, setExternalLink] = useState('')

  const [showConfirmBox, setShowConfirmBox] = useState(false)

  const [titleSource, setTitleSource] = useState('title')

  const [descriptionSource, setDescriptionSource] = useState('seoDescription')

  const [stickyNotificationTemplate, setStickyNotificationTemplate] =
    useState('')

  const [typeOfNotification, setTypeOfNotification] = useState('')

  const [title, setTitle] = useState('')

  const [url, setURL] = useState('')

  const [description, setDescription] = useState('')

  const [thumbnail, setThumbnail] = useState({
    url: '',
    status: '',
  })

  const [selectedPage, setSelectedPage] = useState<any>(null)

  const [specificPages, setSpecificPages] = useState<any>([])

  const [carouselData, setCarouselData] = useState<any>([])

  const [formFloatingPage, setFormFloatingPage] = useState<any>(null)

  const [categoriesPages, setCategoriesPages] = useState<any>([])

  const [isGloballyEnabled, setIsGloballyEnabled] = useState(false)

  const [isEnabled, setIsEnabled] = useState(true)

  const [isClosable, setIsClosable] = useState(false)

  const [selectedCTA, setSelectedCTA] = useState<any>(null)

  const [bgColor, setBgColor] = useState('Primary')

  const clearAllStates = () => {
    setSelectedPage(null)
    setSpecificPages([])
    setCategoriesPages([])
    setFormFloatingPage(null)
    setCarouselData(null)
    setSelectedCTA(null)
    setBgColor('')
    setIsClosable(false)
    setIsGloballyEnabled(false)
    setIsEnabled(true)
    setThumbnail({
      url: '',
      status: '',
    })
    setTitle('')
    setURL('')
    setDescription('')
    setInternalName('')
    setStickyNotificationTemplate('')
    setTypeOfNotification('')
    setTypeOfPost('')
    setActiveStep(0)
    setStatus('')
  }

  const handleClose = () => {
    setShowConfirmBox(false)
  }

  const handleRealTimeConfirm = async () => {
    setShowConfirmBox(false)
    await CreateRealTimeNotification()
  }

  const handleStickyConfirm = async () => {
    setShowConfirmBox(false)
    await CreateStickyNotification()
  }

  const handleFileChange = async (event: any) => {
    setIsLoading(true)
    const file = event.target.files[0]

    if (!file) return

    //Create or update asset
    const asset: any = await createOrUpdateAsset(file, currentLocale)

    setThumbnail({
      url: asset?.fields?.file?.['en-CA'].url,
      status: asset.sys.fieldStatus?.['*']?.['en-CA'],
    })

    setIsLoading(false)
  }

  const selectSingleAsset = async () => {
    if (!extension) return

    const asset = await extension.dialogs.selectSingleAsset()

    if (!asset) return

    setThumbnail({
      url: asset.fields?.file?.['en-CA'].url,
      status: asset.sys.fieldStatus?.['*']?.['en-CA'],
    })
  }

  const SingleEntrySelector = async (contentTypes: string[]) => {
    if (!extension) return

    const selected = await extension.dialogs.selectSingleEntry({
      locale: extension.locales.default,
      contentTypes: contentTypes,
    })

    if (!selected) return

    const detailedEntry = await fetchEntryDetails(selected?.sys?.id, extension)

    return detailedEntry
  }

  const MultiEntrySelector = async (contentTypes: string[]) => {
    if (!extension) return

    const selected = await extension.dialogs.selectMultipleEntries({
      locale: extension.locales.default,
      contentTypes: contentTypes,
    })

    if (!selected || selected?.length === 0) return

    const entriesPromises = selected.map((entry: any) =>
      fetchEntryDetails(entry?.sys?.id, extension)
    )

    const res = await Promise.all(entriesPromises)
      .then((entriesDetails) => entriesDetails)
      .catch((error) => {})

    return res
  }

  const pageSelector = async () => {
    const contentTypes = ['page']

    const selected = await SingleEntrySelector(contentTypes)

    const title = selected?.fields?.title?.['en-CA']

    const description = selected?.fields?.seoDescription?.['en-CA']

    const internalName = selected?.fields?.internalName?.['en-CA']

    setTitle(title)
    setInternalName(internalName)
    setDescription(description)

    setSelectedPage(selected)

    const pageThumbnail: any = await getAssetData(
      selected?.fields?.pageThumbnail?.['en-CA']?.sys?.id
    ).then((res) => res)

    const d = {
      url: pageThumbnail?.fields?.file?.['en-CA'].url,
      status: pageThumbnail?.sys?.fieldStatus?.['*']?.['en-CA'],
    }

    setThumbnail(d)
  }

  const formFloatingSelector = async () => {
    const contentTypes = ['componentForm']

    const selected = await SingleEntrySelector(contentTypes)

    const internalName =
      'Form Floating - ' + selected?.fields?.internalName?.['en-CA']

    setInternalName(internalName)

    setFormFloatingPage(selected)
  }

  const categoriesPageRemove = (_: string, id: string) => {
    const filtered = categoriesPages?.filter(
      (page: any) => page?.sys?.id !== id
    )
    setCategoriesPages(filtered)
  }

  const specificPageRemove = (_: string, id: string) => {
    const filtered = specificPages?.filter((page: any) => page?.sys?.id !== id)
    setSpecificPages(filtered)
  }

  const carouselRemove = (_: string, id: string) => {
    const filtered = carouselData?.filter((page: any) => page?.sys?.id !== id)
    setCarouselData(filtered)
  }

  const specificPageSelector = async () => {
    const contentTypes = ['page']

    const selected: any = await MultiEntrySelector(contentTypes)

    if (!selected || selected.length === 0) return

    let newSelected = [...specificPages, ...selected]

    setSpecificPages(newSelected)
  }

  const categoriesPageSelector = async () => {
    const contentTypes = ['page']

    const selected: any = await MultiEntrySelector(contentTypes)

    if (!selected || selected.length === 0) return

    let newSelected = [...categoriesPages, ...selected]

    setCategoriesPages(newSelected)
  }

  const carouselSelector = async () => {
    const contentTypes = ['carouselComponent']

    const selected = await MultiEntrySelector(contentTypes)

    if (!selected || selected.length === 0) return

    let newSelected = [...carouselData, ...selected]

    const internalName = newSelected?.[0]?.fields?.internalName?.['en-CA']

    setInternalName(internalName)

    setCarouselData(newSelected)
  }

  const CTASelector = async () => {
    const contentTypes = ['linkComponent']
    const selected = await SingleEntrySelector(contentTypes)

    setSelectedCTA(selected)

    const internalLinkEntryId =
      selected?.fields?.internalLink?.['en-CA']?.sys?.id

    const externalLink = selected?.fields?.externalLink?.['en-CA']

    if (externalLink) setExternalLink(externalLink)

    if (!internalLinkEntryId) return

    const internalPage = await getEntryDataById(internalLinkEntryId).then(
      (res) => res
    )

    const internalName = internalPage?.fields?.internalName?.['en-CA']

    setInternalName(internalName)

    const title = internalPage?.fields?.title?.['en-CA']

    setTitle(title)

    const description = internalPage?.fields?.seoDescription?.['en-CA']

    setDescription(description)

    const slug = internalPage?.fields?.slug?.['en-CA']

    setExternalLink(slug)

    const pageThumbnail: any = await getAssetData(
      internalPage?.fields?.pageThumbnail?.['en-CA']?.sys?.id
    ).then((res) => res)

    const d = {
      url: pageThumbnail?.fields?.file?.['en-CA'].url,
      status: pageThumbnail?.sys?.fieldStatus?.['*']?.['en-CA'],
    }

    setThumbnail(d)
  }

  const checkFirstStepDisable = () => {
    if (!typeOfPost) return false

    if (typeOfPost === '2') return true

    if (typeOfPost === '1' && !typeOfNotification) return false

    if (
      typeOfPost === '1' &&
      typeOfNotification === '1' &&
      !stickyNotificationTemplate
    )
      return false

    return true
  }

  const checkSecondStepDisable = () => {
    if (!title && !description && !internalName) return false

    if (
      typeOfPost === '2' &&
      (!formFloatingPage || !title || !description || !internalName)
    )
      return false
    if (
      typeOfPost === '1' &&
      typeOfNotification === '1' &&
      stickyNotificationTemplate === 'Carousel' &&
      carouselData.length === 0
    )
      return false

    if (
      typeOfPost === '1' &&
      typeOfNotification === '1' &&
      stickyNotificationTemplate !== 'Carousel' &&
      !selectedCTA &&
      !selectedPage &&
      !url
    )
      return false

    return true
  }

  const CreateRealTimeNotification = async () => {
    setIsLoading(true)

    let payload: any = {
      title: title || selectedPage?.fields?.seoTitle?.['en-CA'],
      body: description || selectedPage?.fields?.seoDescription?.['en-CA'],
      icon: thumbnail.url,
      url:
        getDomainDataByDomainName(domain)?.url +
        (url || externalLink || selectedPage?.fields?.slug?.['en-CA']),

      id: generateRandomId(),
      domain: getDomainDataByDomainName(domain)?.domainKey,
    }

    if (typeOfNotification === '2') {
      const res = await CreatePushNotification(payload)

      setStatus(res ? 'success' : 'error')
    }
    if (typeOfNotification === '3') {
      const res = await sendMessage(
        payload,
        getDomainDataByDomainName(domain)?.domainKey
      )
      setStatus(res ? 'success' : 'error')
    }

    setIsLoading(false)

    setTimeout(() => {
      clearAllStates()
    }, 3000)
  }

  const CreateStickyNotification = async () => {
    setIsLoading(true)

    let payload: any = {
      internalName: {
        'en-CA': internalName || '',
      },
      heading: {
        'en-CA': title || '',
      },
      description: {
        'en-CA': description || '',
      },
      backgroundColor: {
        'en-CA': bgColor || '',
      },
      template: {
        'en-CA':
          typeOfPost === '2'
            ? 'FormFloating'
            : stickyNotificationTemplate || 'Small',
      },
      isGlobal: {
        'en-CA': isGloballyEnabled,
      },
      isEnabled: {
        'en-CA': isEnabled,
      },
      isPersistent: {
        'en-CA': isClosable,
      },
    }

    if (selectedCTA) {
      payload = {
        ...payload,
        cta: {
          'en-CA': {
            sys: {
              type: 'Link',
              linkType: 'Entry',
              id: selectedCTA.sys.id,
            },
          },
        },
      }
    }

    if (specificPages.length > 0) {
      payload = {
        ...payload,
        SpecificPage: {
          'en-CA': specificPages.map((categoryPage: any) => {
            return {
              sys: {
                type: 'Link',
                linkType: 'Entry',
                id: categoryPage.sys.id,
              },
            }
          }),
        },
      }
    }

    if (formFloatingPage) {
      payload = {
        ...payload,
        formFloating: {
          'en-CA': {
            sys: {
              type: 'Link',
              linkType: 'Entry',
              id: formFloatingPage.sys.id,
            },
          },
        },
      }
    }

    if (categoriesPages?.length > 0) {
      payload = {
        ...payload,
        categoriesPage: {
          'en-CA': categoriesPages.map((categoryPage: any) => {
            return {
              sys: {
                type: 'Link',
                linkType: 'Entry',
                id: categoryPage.sys.id,
              },
            }
          }),
        },
      }
    }

    const tags = [
      {
        sys: {
          type: 'Link',
          linkType: 'Tag',
          id: getDomainDataByDomainName(domain as any)?.domainKey,
        },
      },
    ]
    const res = await createContentEntry('componentNotification', payload, tags)
    setStatus(res ? 'success' : 'error')
    setIsLoading(false)
    setTimeout(() => {
      clearAllStates()
    }, 3000)
  }

  useEffect(() => {
    init((ext: any) => {
      ext?.window?.startAutoResizer()
      setExtension(ext)
    })
  }, [])

  return (
    <Box
      style={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        // position: 'relative',
        height: '100%',
      }}
    >
      <Box style={{ width: '100%', height: activeStep === 0 ? '70%' : 'auto' }}>
        <Box
          style={{
            height: '4px',
          }}
        />
        <ProgressBar completedSteps={activeStep} totalSteps={3} />
        <Box
          style={{
            height: '4px',
          }}
        />
        {activeStep !== 3 && (
          <Box
            style={{
              paddingInline: '1rem',
            }}
          >
            <h2 className='modalTitle'>
              {activeStep === 0
                ? 'Type of Notification'
                : activeStep === 1
                  ? 'Add Source & Details'
                  : activeStep === 2 && 'Notification Settings'}
            </h2>
            <Box
              style={{
                paddingBlock: '0.5rem',
              }}
            >
              <p className='modalSubTitle'>
                <span className='subtitleHeading'>Description: </span>
                <span>
                  {activeStep === 0
                    ? 'Select which type of notification you want to create'
                    : activeStep === 1
                      ? 'Select the Notification Source and add related details'
                      : activeStep === 2 && 'Provide notification settings'}
                </span>
              </p>
            </Box>
            <hr
              className='endHorizontalLine'
              style={{
                width: '100%',
              }}
            />
          </Box>
        )}
        {activeStep === 0 && (
          <Box
            style={{
              width: '100%',
              height: '96%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'column',
            }}
          >
            <FormControlComp
              label='Type of Post'
              tooltip=''
              isRequired
              className='smallSelect'
            >
              <Select
                value={typeOfPost}
                onChange={(e) => {
                  setTypeOfPost(e.target.value)
                  if (e.target.value === '1') {
                    setFormFloatingPage(null)
                  } else {
                    setSpecificPages([])
                    setCategoriesPages([])
                    setSelectedCTA(null)
                    setSelectedPage(null)
                  }
                }}
              >
                <Select.Option value='' isDisabled>
                  Select
                </Select.Option>
                {TypesOfPost.map((option) => (
                  <Select.Option value={option.value}>
                    {option.title}
                  </Select.Option>
                ))}
              </Select>
            </FormControlComp>
            {typeOfPost === '1' && (
              <FormControlComp
                label='Type of Notification'
                tooltip=''
                isRequired
                className='smallSelect'
              >
                <Select
                  value={typeOfNotification}
                  onChange={(e) => {
                    setTypeOfNotification(e.target.value)
                    if (typeOfNotification !== '1') {
                      setStickyNotificationTemplate('')
                      setSelectedPage(null)
                      setSelectedCTA(null)
                      setURL('')
                      setExternalLink('')
                      setCarouselData([])
                      setThumbnail({
                        url: '',
                        status: '',
                      })
                    }
                  }}
                >
                  <Select.Option value='' isDisabled>
                    Select
                  </Select.Option>
                  {TypeOfNotifications.map((option) => (
                    <Select.Option value={option.value}>
                      {option.title}
                    </Select.Option>
                  ))}
                </Select>
              </FormControlComp>
            )}

            {typeOfPost === '1' && typeOfNotification === '1' && (
              <FormControlComp
                label='Template'
                tooltip=''
                isRequired
                className='smallSelect'
              >
                <Select
                  value={stickyNotificationTemplate}
                  onChange={(e) => {
                    setStickyNotificationTemplate(e.target.value)
                    if (e.target.value === 'Carousel') {
                      setSpecificPages([])
                      setCategoriesPages([])
                      setSelectedCTA(null)
                      setSelectedPage(null)
                    } else {
                      setCarouselData([])
                    }
                  }}
                >
                  <Select.Option value='' isDisabled>
                    Select
                  </Select.Option>
                  {StickyNotificationTemplates.map((option) => (
                    <Select.Option value={option.value}>
                      {option.title}
                    </Select.Option>
                  ))}
                </Select>
              </FormControlComp>
            )}
            {/* {typeOfPost === '1' && typeOfNotification === '2' && (
              <FormControlComp
                label='Type of Real Time Notification'
                tooltip=''
                isRequired
                className='smallSelect'
              >
                <Select
                  value={typeOfRealTimeNotification}
                  onChange={(e) =>
                    setTypeOfRealTimeNotification(e.target.value)
                  }
                >
                  <Select.Option value='' isDisabled>
                    Select
                  </Select.Option>
                  {TypeOfRealTimeNotifications.map((option) => (
                    <Select.Option value={option.value}>
                      {option.title}
                    </Select.Option>
                  ))}
                </Select>
              </FormControlComp>
            )} */}
          </Box>
        )}
        {activeStep === 1 && (
          <Box
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'start',
              flexDirection: 'row',

              height: '96%',
            }}
          >
            <Box
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'start',
                flexDirection: 'column',
              }}
            >
              <Box
                style={{
                  paddingInline: 15,
                  width: '100%',
                }}
              >
                <h6
                  style={{
                    marginBlock: 10,
                  }}
                >
                  Source
                </h6>
                <hr
                  className='endHorizontalLine'
                  style={{
                    width: '100%',
                  }}
                />
              </Box>
              {!selectedCTA &&
                !url &&
                typeOfPost === '1' &&
                typeOfNotification !== '1' && (
                  <>
                    <FormControlComp
                      label='Page Source'
                      tooltip=''
                      isRequired={typeOfPost === '1'}
                    >
                      {selectedPage ? (
                        <CustomCard
                          data={selectedPage}
                          onRemoveEntry={() => setSelectedPage(null)}
                          field={'formFloating'}
                        />
                      ) : (
                        <Button
                          onClick={pageSelector}
                          variant='secondary'
                          startIcon={<LinkIcon />}
                          size='small'
                        >
                          Link existing entry
                        </Button>
                      )}
                    </FormControlComp>
                    <Box
                      style={{
                        height: '14px',
                      }}
                    />
                  </>
                )}

              {!selectedPage &&
                !url &&
                typeOfPost === '1' &&
                stickyNotificationTemplate !== 'Carousel' && (
                  <FormControlComp
                    label='CTA Source'
                    tooltip=''
                    isRequired={typeOfPost === '1'}
                  >
                    {selectedCTA ? (
                      <CustomCard
                        data={selectedCTA}
                        onRemoveEntry={() => setSelectedCTA(null)}
                        field={'formFloating'}
                      />
                    ) : (
                      <Button
                        onClick={CTASelector}
                        variant='secondary'
                        startIcon={<LinkIcon />}
                        size='small'
                      >
                        Link existing entry
                      </Button>
                    )}
                  </FormControlComp>
                )}

              {!selectedPage &&
                !selectedCTA &&
                typeOfPost === '1' &&
                typeOfNotification !== '1' && (
                  <>
                    <Box
                      style={{
                        height: '14px',
                      }}
                    />
                    <FormControlComp
                      label='External URL'
                      tooltip=''
                      isRequired={typeOfPost === '1'}
                    >
                      <TextInput
                        value={url}
                        placeholder='Enter URL'
                        onChange={(e) => setURL(e.target.value)}
                      />
                    </FormControlComp>
                  </>
                )}

              {selectedPage && (
                <>
                  <Box
                    style={{
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'center',
                      paddingInline: 15,
                      gap: '10px',
                      width: '100%',
                    }}
                  >
                    <p
                      style={{
                        width: '250px',
                        fontSize: '15px',
                        fontWeight: '500',
                      }}
                    >
                      Notification Title From:
                    </p>
                    <Select
                      onChange={(e) => {
                        setTitleSource(e.target.value)
                        const v = e.target.value
                        const title = selectedPage?.fields?.[v]?.['en-CA']
                        if (title) setTitle(title)
                      }}
                      value={titleSource}
                    >
                      <Select.Option value='' isDisabled>
                        Select
                      </Select.Option>
                      <Select.Option value='title'>Page Title</Select.Option>
                      <Select.Option value='seoTitle'>SEO Title</Select.Option>
                      <Select.Option value='shortTitle'>
                        Algolia Title
                      </Select.Option>
                    </Select>
                  </Box>
                  <Box
                    style={{
                      height: '10px',
                    }}
                  />
                  <Box
                    style={{
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'center',
                      paddingInline: 15,
                      gap: '10px',
                      width: '100%',
                    }}
                  >
                    <p
                      style={{
                        width: '250px',
                        fontSize: '15px',
                        fontWeight: '500',
                      }}
                    >
                      Notification Description From:
                    </p>
                    <Select
                      onChange={(e) => {
                        setDescriptionSource(e.target.value)
                        const v = e.target.value
                        const title = selectedPage?.fields?.[v]?.['en-CA']
                        if (title) setDescription(title)
                      }}
                      value={descriptionSource}
                    >
                      <Select.Option value='' isDisabled>
                        Select
                      </Select.Option>
                      <Select.Option value='title'>Page Title</Select.Option>
                      <Select.Option value='seoTitle'>SEO Title</Select.Option>
                      <Select.Option value='shortTitle'>
                        Algolia Title
                      </Select.Option>
                      <Select.Option value='seoDescription'>
                        SEO Description
                      </Select.Option>
                    </Select>
                  </Box>
                </>
              )}

              {typeOfPost === '2' && (
                <FormControlComp label='Floating Form' tooltip=''>
                  {formFloatingPage ? (
                    <CustomCard
                      data={formFloatingPage}
                      onRemoveEntry={() => setFormFloatingPage(null)}
                      field={'formFloating'}
                    />
                  ) : (
                    <Button
                      onClick={formFloatingSelector}
                      variant='secondary'
                      startIcon={<LinkIcon />}
                      size='small'
                    >
                      Link existing entry
                    </Button>
                  )}
                </FormControlComp>
              )}
              {typeOfPost === '1' &&
                typeOfNotification === '1' &&
                stickyNotificationTemplate === 'Carousel' && (
                  <FormControlComp label='Carousel Data' tooltip=''>
                    <Box
                      style={{
                        display: 'flex',
                        gap: '10px',
                        width: '100%',
                        flexWrap: 'wrap',
                      }}
                    >
                      {carouselData?.map((carouselPage: any, index: number) => {
                        return (
                          <CustomCard
                            key={index}
                            data={carouselPage}
                            onRemoveEntry={carouselRemove}
                            field={'formFloating'}
                          />
                        )
                      })}
                    </Box>
                    <Box
                      style={{
                        height: '5px',
                      }}
                    />
                    <Button
                      onClick={carouselSelector}
                      variant='secondary'
                      startIcon={<LinkIcon />}
                      size='small'
                    >
                      Link existing entry
                    </Button>
                  </FormControlComp>
                )}
            </Box>
            <Box
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'start',
                flexDirection: 'column',
              }}
            >
              <Box
                style={{
                  paddingInline: 15,
                  width: '100%',
                }}
              >
                <h6
                  style={{
                    marginBlock: 10,
                  }}
                >
                  Details
                </h6>
                <hr
                  className='endHorizontalLine'
                  style={{
                    width: '100%',
                  }}
                />
              </Box>
              <FormControlComp
                label='Internal Name'
                tooltip=''
                isRequired={typeOfNotification !== '2'}
              >
                <TextInput
                  value={internalName}
                  placeholder='Enter Internal Name'
                  onChange={(e) => setInternalName(e.target.value)}
                />
              </FormControlComp>
              <Box
                style={{
                  height: '6px',
                }}
              />
              <FormControlComp
                label='Title'
                tooltip=''
                isRequired={typeOfNotification !== '2'}
              >
                <TextInput
                  value={title}
                  placeholder='Enter Title'
                  onChange={(e) => setTitle(e.target.value)}
                />
              </FormControlComp>
              <Box
                style={{
                  height: '6px',
                }}
              />
              <FormControlComp
                label='Description'
                tooltip=''
                isRequired={typeOfNotification !== '2'}
              >
                <TextInput
                  value={description}
                  placeholder='Enter Description'
                  onChange={(e) => setDescription(e.target.value)}
                />
              </FormControlComp>
              <Box
                style={{
                  height: '6px',
                }}
              />
              {typeOfPost === '1' && typeOfNotification !== '1' && (
                <FormControlComp label='Thumbnail Image' tooltip=''>
                  {thumbnail.url ? (
                    <AssetCard
                      status={thumbnail.status as AssetStatus}
                      type='image'
                      // title='Everest'
                      src={thumbnail.url}
                      size='small'
                      actions={[
                        <MenuItem
                          key='delete'
                          onClick={() => {
                            setThumbnail({
                              url: '',
                              status: '',
                            })
                          }}
                        >
                          Remove
                        </MenuItem>,
                      ]}
                    />
                  ) : (
                    <div className='dv-inputDiv'>
                      <div className={`dv-newFile `}>
                        <input
                          type='file'
                          hidden
                          id='new-file'
                          onChange={handleFileChange}
                          disabled={isLoading}
                        />
                        <Button
                          variant='secondary'
                          startIcon={<PlusIcon />}
                          size='small'
                          onClick={() =>
                            document.getElementById('new-file')?.click()
                          }
                          isLoading={isLoading}
                        >
                          Add new Asset
                        </Button>
                      </div>
                      <Button
                        variant='secondary'
                        startIcon={<LinkIcon />}
                        size='small'
                        onClick={selectSingleAsset}
                        isDisabled={isLoading}
                      >
                        Add existing Asset
                      </Button>
                    </div>
                  )}
                </FormControlComp>
              )}
            </Box>
          </Box>
        )}
        {activeStep === 2 && (
          <Box
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'start',
              flexDirection: 'column',
              gap: '5px',
            }}
          >
            <Box
              style={{
                paddingInline: 15,
                width: '100%',
              }}
            >
              <h6
                style={{
                  marginBlock: 10,
                }}
              >
                Settings
              </h6>
              <hr
                className='endHorizontalLine'
                style={{
                  width: '100%',
                }}
              />
            </Box>
            <Box
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'start',
                flexDirection: 'row',
                height: '96%',
              }}
            >
              <Box
                style={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'start',
                  alignItems: 'start',
                  flexDirection: 'column',
                }}
              >
                <FormControlComp tooltip=''>
                  <Switch
                    name='allow-cookies-controlled'
                    id='allow-cookies-controlled'
                    isChecked={isGloballyEnabled}
                    onChange={() =>
                      setIsGloballyEnabled((prevState) => !prevState)
                    }
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                    className='switchNoPadding'
                  >
                    Is Site Wide
                    <Tooltip
                      placement='right'
                      content={'asd'}
                      style={{
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <Info />
                    </Tooltip>
                  </Switch>
                </FormControlComp>
                <FormControlComp tooltip=''>
                  <Switch
                    name='allow-cookies-controlled'
                    id='allow-cookies-controlled'
                    isChecked={isEnabled}
                    onChange={() => setIsEnabled((prevState) => !prevState)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                    className='switchNoPadding'
                  >
                    Is Enabled
                    <Tooltip
                      placement='right'
                      content={'asd'}
                      style={{
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <Info />
                    </Tooltip>
                  </Switch>
                </FormControlComp>
                <FormControlComp tooltip=''>
                  <Switch
                    name='allow-cookies-controlled'
                    id='allow-cookies-controlled'
                    isChecked={isClosable}
                    onChange={() => setIsClosable((prevState) => !prevState)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                    className='switchNoPadding'
                  >
                    Is Closable
                    <Tooltip
                      placement='right'
                      content={'asd'}
                      style={{
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <Info />
                    </Tooltip>
                  </Switch>
                </FormControlComp>
                {stickyNotificationTemplate !== 'Carousel' && (
                  <Box
                    style={{
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'start',
                    }}
                  >
                    <FormControlComp label='Background Color' tooltip=''>
                      <Select
                        value={bgColor}
                        onChange={(e) => setBgColor(e.target.value)}
                      >
                        {BgColors.map((option) => (
                          <Select.Option value={option.value}>
                            {option.title}
                          </Select.Option>
                        ))}
                      </Select>
                    </FormControlComp>
                  </Box>
                )}
              </Box>

              <Box
                style={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'start',
                  alignItems: 'center',
                  flexDirection: 'column',
                }}
              >
                {stickyNotificationTemplate !== 'Carousel' && (
                  <FormControlComp label='Specific Page' tooltip=''>
                    <Box
                      style={{
                        display: 'flex',
                        gap: '10px',
                        width: '100%',
                        flexWrap: 'wrap',
                      }}
                    >
                      {specificPages.map((specificPage: any, index: number) => {
                        return (
                          <CustomCard
                            key={index}
                            data={specificPage}
                            onRemoveEntry={specificPageRemove}
                            field={'formFloating'}
                          />
                        )
                      })}
                    </Box>
                    <Box
                      style={{
                        height: '5px',
                      }}
                    />
                    <Button
                      onClick={specificPageSelector}
                      variant='secondary'
                      startIcon={<LinkIcon />}
                      size='small'
                    >
                      Link existing entry
                    </Button>
                  </FormControlComp>
                )}
                {stickyNotificationTemplate !== 'Carousel' && (
                  <FormControlComp label='Categories Page' tooltip=''>
                    <Box
                      style={{
                        display: 'flex',
                        gap: '10px',
                        width: '100%',
                        flexWrap: 'wrap',
                      }}
                    >
                      {categoriesPages.map(
                        (categoriesPage: any, index: number) => {
                          return (
                            <CustomCard
                              key={index}
                              data={categoriesPage}
                              onRemoveEntry={categoriesPageRemove}
                              field={'formFloating'}
                            />
                          )
                        }
                      )}
                    </Box>
                    <Box
                      style={{
                        height: '5px',
                      }}
                    />
                    <Button
                      onClick={categoriesPageSelector}
                      variant='secondary'
                      startIcon={<LinkIcon />}
                      size='small'
                    >
                      Link existing entry
                    </Button>
                  </FormControlComp>
                )}
              </Box>
            </Box>
          </Box>
        )}
        {activeStep === 3 && (
          <Box
            className='summaryRoot formRoot'
            style={{
              width: '100%',
            }}
          >
            <h6
              style={{
                marginTop: 0,
                marginBottom: 10,
              }}
            >
              Summary
            </h6>
            <p className='summaryTitle'>
              <b>
                {typeOfPost === '1' ? (
                  <>
                    {typeOfNotification === '1'
                      ? 'Page Sticky'
                      : typeOfNotification === '2'
                        ? 'Real Time On Page'
                        : 'Instant Browser'}{' '}
                  </>
                ) : (
                  'Floating form '
                )}
                Notification{' '}
              </b>{' '}
              will be created with the following details.
              <br /> If everything looks good, click Apply to send
              thenotification. You can always go back to update / amend.
            </p>
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '20px', // Adjust the gap between columns as needed
                rowGap: '20px',
                columnGap: '10px',
                paddingTop: '30px',
                width: '100%',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'start',
                  alignItems: 'start',
                  width: '100%',
                }}
              >
                <span
                  className='domainName'
                  style={{
                    fontSize: '15px',
                    color: '#667082',
                  }}
                >
                  Title:
                </span>
                <span
                  className='SlugName'
                  style={{
                    fontWeight: '500',
                    fontSize: '15px',
                    width: 'auto',
                    maxWidth: '90%',
                  }}
                >
                  {title}
                </span>
              </div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'start',
                  alignItems: 'start',
                  width: '100%',
                }}
              >
                <span
                  className='domainName'
                  style={{
                    fontSize: '15px',
                    color: '#667082',
                  }}
                >
                  Description:
                </span>
                <span
                  className='SlugName'
                  style={{
                    fontWeight: '500',
                    fontSize: '15px',
                    width: '80%',
                    maxWidth: '90%',
                  }}
                >
                  {description}
                </span>
              </div>
              {(url ||
                externalLink ||
                selectedPage?.fields?.slug?.['en-CA']) && (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'start',
                    alignItems: 'start',
                    width: '100%',
                  }}
                >
                  <span
                    className='domainName'
                    style={{
                      fontSize: '15px',
                      color: '#667082',
                    }}
                  >
                    URL:
                  </span>
                  <span
                    className='SlugName'
                    style={{
                      fontWeight: '500',
                      fontSize: '15px',
                      width: 'auto',
                      maxWidth: '90%',
                    }}
                  >
                    {url ||
                      externalLink ||
                      selectedPage?.fields?.slug?.['en-CA']}
                  </span>
                </div>
              )}
              {typeOfPost === '1' && typeOfNotification !== '1' && (
                <>
                  {selectedPage && (
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'start',
                        alignItems: 'start',
                        width: '100%',
                      }}
                    >
                      <span
                        className='domainName'
                        style={{
                          fontSize: '15px',
                          color: '#667082',
                        }}
                      >
                        Page Resource:
                      </span>
                      <a
                        className='SlugName'
                        style={{
                          fontWeight: '500',
                          fontSize: '15px',
                          width: 'auto',
                          maxWidth: '90%',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          color: '#414D63',
                        }}
                        target='_blank'
                        href={handleUrlClick(selectedPage)}
                        rel='noreferrer'
                      >
                        {selectedPage?.fields?.internalName?.['en-CA']}
                      </a>
                    </div>
                  )}
                  {selectedCTA && (
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'start',
                        alignItems: 'start',
                        width: '100%',
                      }}
                    >
                      <span
                        className='domainName'
                        style={{
                          fontSize: '15px',
                          color: '#667082',
                        }}
                      >
                        CTA Resource:
                      </span>
                      <a
                        className='SlugName'
                        style={{
                          fontWeight: '500',
                          fontSize: '15px',
                          width: 'auto',
                          maxWidth: '90%',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          color: '#414D63',
                        }}
                        target='_blank'
                        href={handleUrlClick(selectedCTA)}
                        rel='noreferrer'
                      >
                        {selectedCTA?.fields?.internalName?.['en-CA']}
                      </a>
                    </div>
                  )}
                </>
              )}

              {typeOfPost === '2' && (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'start',
                    alignItems: 'start',
                    width: '100%',
                  }}
                >
                  <span
                    className='domainName'
                    style={{
                      fontSize: '15px',
                      color: '#667082',
                    }}
                  >
                    Floating form:
                  </span>
                  <a
                    className='SlugName'
                    style={{
                      fontWeight: '500',
                      fontSize: '15px',
                      width: 'auto',
                      maxWidth: '90%',
                      textDecoration: 'underline',
                      cursor: 'pointer',
                      color: '#414D63',
                    }}
                    target='_blank'
                    href={handleUrlClick(formFloatingPage)}
                    rel='noreferrer'
                  >
                    {formFloatingPage?.fields?.internalName?.['en-CA']}
                  </a>
                </div>
              )}
              {(typeOfPost === '2' || typeOfNotification === '1') && (
                <>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'start',
                      width: '100%',
                    }}
                  >
                    <span
                      className='domainName'
                      style={{
                        fontSize: '15px',
                        color: '#667082',
                      }}
                    >
                      Is Global:
                    </span>
                    <span
                      className='SlugName'
                      style={{
                        fontWeight: '500',
                        fontSize: '15px',
                        width: 'auto',
                        maxWidth: '90%',
                      }}
                    >
                      {isGloballyEnabled ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'start',
                      width: '100%',
                    }}
                  >
                    <span
                      className='domainName'
                      style={{
                        fontSize: '15px',
                        color: '#667082',
                      }}
                    >
                      Is Enabled:
                    </span>
                    <span
                      className='SlugName'
                      style={{
                        fontWeight: '500',
                        fontSize: '15px',
                        width: 'auto',
                        maxWidth: '90%',
                      }}
                    >
                      {isEnabled ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'start',
                      width: '100%',
                    }}
                  >
                    <span
                      className='domainName'
                      style={{
                        fontSize: '15px',
                        color: '#667082',
                      }}
                    >
                      Is Closable:
                    </span>
                    <span
                      className='SlugName'
                      style={{
                        fontWeight: '500',
                        fontSize: '15px',
                        width: 'auto',
                        maxWidth: '90%',
                      }}
                    >
                      {isClosable ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'start',
                      width: '100%',
                    }}
                  >
                    <span
                      className='domainName'
                      style={{
                        fontSize: '15px',
                        color: '#667082',
                      }}
                    >
                      Background Color:
                    </span>
                    <span
                      className='SlugName'
                      style={{
                        fontWeight: '500',
                        fontSize: '15px',
                        width: 'auto',
                        maxWidth: '90%',
                      }}
                    >
                      {bgColor}
                    </span>
                  </div>
                </>
              )}
              {specificPages.length > 0 && (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'start',
                    alignItems: 'start',
                    width: '100%',
                  }}
                >
                  <span
                    className='domainName'
                    style={{
                      fontSize: '15px',
                      color: '#667082',
                    }}
                  >
                    Specific Pages:
                  </span>
                  <div
                    style={{
                      width: '90%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'start',
                      gap: '10px',
                    }}
                  >
                    {specificPages.map((page: any) => (
                      <a
                        className='SlugName'
                        style={{
                          fontWeight: '500',
                          fontSize: '15px',
                          width: 'auto',
                          maxWidth: '90%',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          color: '#414D63',
                        }}
                        target='_blank'
                        href={handleUrlClick(page)}
                        rel='noreferrer'
                      >
                        {page?.fields?.internalName?.['en-CA']}
                      </a>
                    ))}
                  </div>
                </div>
              )}
              {categoriesPages.length > 0 && (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'start',
                    alignItems: 'start',
                    width: '100%',
                  }}
                >
                  <span
                    className='domainName'
                    style={{
                      fontSize: '15px',
                      color: '#667082',
                    }}
                  >
                    Categories Pages:
                  </span>
                  <div
                    style={{
                      width: '90%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'start',
                      gap: '10px',
                    }}
                  >
                    {categoriesPages.map((page: any) => (
                      <a
                        className='SlugName'
                        style={{
                          fontWeight: '500',
                          fontSize: '15px',
                          width: 'auto',
                          maxWidth: '90%',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          color: '#414D63',
                        }}
                        target='_blank'
                        href={handleUrlClick(page)}
                        rel='noreferrer'
                      >
                        {page?.fields?.internalName?.['en-CA']}
                      </a>
                    ))}
                  </div>
                </div>
              )}
              {typeOfPost === '1' &&
                typeOfNotification === '1' &&
                stickyNotificationTemplate === 'Carousel' &&
                carouselData.length > 0 && (
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'start',
                      width: '100%',
                    }}
                  >
                    <span
                      className='domainName'
                      style={{
                        fontSize: '15px',
                        color: '#667082',
                      }}
                    >
                      Carousels:
                    </span>
                    <div
                      style={{
                        width: '80%',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'start',
                        gap: '10px',
                      }}
                    >
                      {carouselData.map((page: any) => (
                        <a
                          className='SlugName'
                          style={{
                            fontWeight: '500',
                            fontSize: '15px',
                            width: 'auto',
                            maxWidth: '90%',
                            textDecoration: 'underline',
                            cursor: 'pointer',
                            color: '#414D63',
                          }}
                          target='_blank'
                          href={handleUrlClick(page)}
                          rel='noreferrer'
                        >
                          {page?.fields?.internalName?.['en-CA']}
                        </a>
                      ))}
                    </div>
                  </div>
                )}
              {thumbnail.url && (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'start',
                    alignItems: 'start',
                    width: '100%',
                  }}
                >
                  <span
                    className='domainName'
                    style={{
                      fontSize: '15px',
                      color: '#667082',
                    }}
                  >
                    Thumbnail:
                  </span>
                  <span
                    className='SlugName'
                    style={{
                      fontWeight: '500',
                      fontSize: '15px',
                      width: 'auto',
                      maxWidth: '90%',
                    }}
                  >
                    <AssetCard
                      status={thumbnail.status as AssetStatus}
                      type='image'
                      // title='Everest'
                      src={thumbnail.url}
                      size='small'
                    />
                  </span>
                </div>
              )}
            </div>
          </Box>
        )}
      </Box>

      <Box
        style={{
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'end',
          alignItems: 'center',
          // position: 'sticky',
          // bottom: '0px',
          // backgroundColor: 'white',
        }}
      >
        <hr
          className='endHorizontalLine'
          style={{
            width: '100%',
          }}
        />
        <ModalControls
          style={{
            paddingRight: '1rem',
            paddingBottom: '1rem',
          }}
        >
          {status === 'success' ? (
            <Text fontColor='green500'> Notification created successfully</Text>
          ) : status === 'error' ? (
            <Text fontColor='red500'> Notification creation failed</Text>
          ) : (
            <></>
          )}
          {activeStep === 0 ? (
            <NextButton
              helpText='Next'
              onClick={() => setActiveStep(1)}
              isDisabled={!checkFirstStepDisable()}
            />
          ) : activeStep === 1 ? (
            <>
              <PrevButton
                helpText='Amend/Update'
                onClick={() => setActiveStep(0)}
              />
              {typeOfPost === '1' && typeOfNotification !== '1' ? (
                <NextButton
                  helpText='Next'
                  type='positive'
                  onClick={() => setActiveStep(3)}
                  isDisabled={!checkSecondStepDisable()}
                />
              ) : (
                <NextButton
                  helpText='Next'
                  type='positive'
                  onClick={() => setActiveStep(2)}
                  isDisabled={!checkSecondStepDisable()}
                />
              )}
            </>
          ) : activeStep === 2 ? (
            <>
              <PrevButton
                helpText='Amend/Update'
                onClick={() => setActiveStep(1)}
              />
              <NextButton
                helpText='Next'
                type='positive'
                onClick={() => setActiveStep(3)}
                isDisabled={!checkSecondStepDisable()}
              />
            </>
          ) : (
            <>
              <PrevButton
                helpText='Amend/Update'
                onClick={() =>
                  setActiveStep(
                    typeOfPost === '1' && typeOfNotification !== '1' ? 1 : 2
                  )
                }
              />
              <SaveButton
                helpText='Create Notification'
                onClick={() => setShowConfirmBox(true)}
                isLoading={isLoading}
                // isDisabled={!checkSecondStepDisable()}
              />
            </>
          )}
        </ModalControls>
        <ModalConfirm
          children={
            <p
              style={{
                fontSize: '15x',
              }}
            >
              Once you create a notification, it can't be undone.
            </p>
          }
          handleClose={handleClose}
          onConfirm={
            typeOfNotification === '1'
              ? handleStickyConfirm
              : handleRealTimeConfirm
          }
          open={showConfirmBox}
        />
      </Box>
    </Box>
  )
}

export default NotificationStepperForm

const TypesOfPost = [
  {
    title: 'Notification',
    value: 1,
  },
  {
    title: 'Floating Subscription Form',
    value: 2,
  },
]

const TypeOfNotifications = [
  {
    title: 'Sticky Notification',
    value: 1,
  },
  {
    title: 'Real Time On Page Notification',
    value: 2,
  },
  {
    title: 'Instant Browser Notification',
    value: 3,
  },
]

const StickyNotificationTemplates = [
  {
    title: 'Small',
    value: 'Small',
  },
  {
    title: 'Medium',
    value: 'Medium',
  },
  {
    title: 'Carousel',
    value: 'Carousel',
  },
]

const BgColors = [
  {
    title: 'Primary',
    value: 'Primary',
  },
  {
    title: 'Secondary',
    value: 'Secondary',
  },
]

const handleUrlClick = (data: any) => {
  let url = data.sys.urn.split(':::content:')[1]

  const final = 'https://app.contentful.com/' + url

  return final
}
