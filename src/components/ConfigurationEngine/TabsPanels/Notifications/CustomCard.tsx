import { EntryCard, MenuItem } from '@contentful/f36-components'
import React from 'react'
import { AssetThumbnail } from '../../../InputFields/Reference'

function CustomCard({ data, onRemoveEntry, field, file }: any) {
  const handleOnEdit = () => {
    let url = data.sys.urn.split(':::content:')[1]

    const final = 'https://app.contentful.com/' + url

    window.open(final, '_blank')
  }

  return (
    <EntryCard
      status={data?.sys?.fieldStatus?.['*']?.['en-CA']}
      contentType={data?.sys?.contentType?.sys?.id}
      title={data?.fields?.internalName?.['en-CA']}
      actions={[
        <MenuItem key='copy' onClick={handleOnEdit}>
          Edit
        </MenuItem>,
        <MenuItem
          key='delete'
          onClick={() => onRemoveEntry(field, data?.sys?.id)}
        >
          Remove
        </MenuItem>,
      ]}
      thumbnailElement={file ? <AssetThumbnail file={file} /> : <></>}
      description={data?.fields?.template?.['en-CA']}
    />
  )
}

export default CustomCard
