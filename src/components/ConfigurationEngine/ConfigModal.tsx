import { EditorAppSDK, HomeAppSDK } from '@contentful/app-sdk'
import { Box, Modal, Select } from '@contentful/f36-components'
import React, { useContext, useState } from 'react'
import logo from '../../assets/logo-small.jpg'
import { GlobalContext } from '../../contexts/globalContext'
import { Domains, domainsConfig } from '../Crosspost/utils'
import { EntityProvider } from '../InputFields/Reference'
import GlobalConfig from './Components/GlobalConfig'
import MSAConfig from './Components/MSAConfig'
import './index.scss'

interface ConfigSettingsModalProps {
  onClose: (value: unknown) => void
  sdk: EditorAppSDK | HomeAppSDK
  entryId?: string
}

function ConfigModal(props: ConfigSettingsModalProps) {
  const { sdk } = props

  const { isConfigSettingsModalOpen, setIsConfigSettingsModalOpen } =
    useContext(GlobalContext)

  const [selectedValue, setSelectedValue] = useState<string>('global')

  const [selectedDomain, setSelectedDomain] = useState<string>('agl')

  const [selectedTab, setSelectedTab] = useState<string>('global')

  const handleClose = () => setIsConfigSettingsModalOpen(false)

  return (
    <EntityProvider sdk={sdk} key={'config'}>
      <Modal
        className={'configEngineModal'}
        onClose={() => handleClose()}
        isShown={isConfigSettingsModalOpen}
        position='center'
        shouldCloseOnOverlayClick={false}
        shouldCloseOnEscapePress={false}
      >
        {() => (
          <>
            <Modal.Header title='' onClose={() => handleClose()}>
              <Box
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'start',
                  gap: '2px',
                }}
              >
                <img src={logo} height={50} width={50} alt='' />
                <Box
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'start',
                    border: '1px solid #CFD9E0',
                    boxShadow: 'inset 0px 2px 0px rgba(225, 228, 232, 0.2)',
                    borderRadius: '6px',
                    paddingInline: '10px',
                  }}
                >
                  <p
                    style={{
                      paddingBottom: '1px',
                    }}
                  >
                    Configure
                  </p>
                  <Select
                    value={selectedValue}
                    onChange={(event: React.ChangeEvent<HTMLSelectElement>) => {
                      if (event.target.value === 'global') {
                        setSelectedTab('global')
                      } else {
                        setSelectedTab('msa')
                        setSelectedDomain(event.target.value as Domains)
                      }
                      setSelectedValue(event.target.value)
                    }}
                    size='small'
                    testId='original-domain-select'
                    className='customSelect'
                  >
                    <Select.Option value={'global'}>
                      Global Settings
                    </Select.Option>
                    {domainsConfig?.map((item) => (
                      <Select.Option key={item.key} value={item.key}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Box>
              </Box>
              {/* <Box
                style={{
                  display       : 'flex',
                  alignItems    : 'center',
                  justifyContent: 'start',
                  gap           : '2px',
                  position      : 'absolute',
                  right         : 70,
                }}
              >
                <TextInput
                  icon={<Search />}
                  size='small'
                  placeholder='Quickly find a setting'
                />
              </Box> */}
            </Modal.Header>

            <Modal.Content className='configModalContent'>
              {selectedTab === 'global' && <GlobalConfig />}
              {selectedTab === 'msa' && (
                <MSAConfig sdk={sdk} selectedDomain={selectedDomain} />
              )}
            </Modal.Content>
          </>
        )}
      </Modal>
    </EntityProvider>
  )
}

export default ConfigModal
