.configEngineModal {
  width: 100vw !important;
  height: 90vh !important;
  min-height: 90vh !important;
}

.css-wich3m {
  padding: 1rem !important;
}

.configModalContent {
  overflow-y: hidden;
  width: 100% !important;
  height: 100% !important;
}

.css-1ry5edl {
  margin-bottom: 0;
}

.formHeader {
  display: flex;
  justify-content: space-between;
}

.form-dropdown {
  display: flex;
}

// .css-1m52afs {
//   display: none;
// }

.formRoot {
  overflow-y: auto;
}

.modalTitle {
  margin: 0px;
  padding: 0px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial,
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  font-weight: 600;
  color: rgb(17, 27, 43);
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: 0rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.modalSubTitle {
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 4px;
  width: 100%;
}

.subtitleHeading {
  font-weight: 600;
  text-transform: none;
  color: black;
}

.formDropdown {
  display: flex;
  justify-content: flex-start;
  align-items: center !important;
  gap: 15px;
  cursor: pointer;
}

.formLabelPadding {
  padding-left: 16px;
}

.endHorizontalLine {
  margin-top: 0px !important;
}

.slugInputHorizontalLine {
  margin-top: 8px;
}

.infoDiv {
  margin: 0;
  padding: 8px 16px 0px 16px;
}

.summaryRoot {
  display: flex;
  flex-direction: column;
  padding: 16px 16px 16px 24px;

  .summaryTitle {
    font-size: 14.5px;
    padding-bottom: 10px;
  }

  .domainName {
    font-size: 15px;
    font-weight: 500;
    width: 150px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: black;
  }

  .SlugName {
    font-weight: 500;
    width: 105px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    padding-right: 5px;
  }
}

.customiserRoot {
  display: flex;
  height: 100%;

  .customiserInnerRoot {
    width: 100%;
    display: flex;

    .customiserSidebar {
      width: 250px !important;
      background: rgb(247, 249, 250);
      //padding: 16px;

      .resizer {
        border: 3px solid black;
      }

      .asideTopDiv {
        padding: 16px 16px 0 16px;

        .searchRoot {
          margin-bottom: 12px;
        }

        .stylingRoot {
          margin-bottom: 18px;
          display: flex;
          justify-content: center;
        }
      }

      .categoryRoot,
      .subCategoryRoot {
        // max-height: 250px;
        overflow-y: auto;

        .subCategoryInner {
          padding: 16px 0;

          .css-1lxwves:hover {
            background-color: rgba(207, 217, 224, 0.5);
          }

          a {
            text-decoration: none;
            color: black;
            font-size: 14px;
          }
        }

        .categoryHeading {
          margin: 0;
          padding-left: 16px;
          padding-top: 10px;
        }

        ul {
          padding: 0;
        }

        .catList.active {
          background-color: rgba(207, 217, 224, 0.5);
        }

        .catList {
          list-style: none;
          padding: 10px !important;
          padding-left: 16px !important;
          font-weight: 500;

          &:hover {
            background-color: rgba(207, 217, 224, 0.5);
          }
        }
      }

      .tagsRoot {
        padding: 16px;
        gap: 10px;
        display: flex;
        flex-direction: column;
      }
    }

    .mainRoot {
      width: 100%;
      height: 100%;

      .previewRoot {
        height: auto;
        // min-height: 350px;
        width: 100%;
        overflow-y: auto;
        display: flex;
        justify-content: flex-start;
        align-items: start;
        padding-inline: 0px;
      }
    }
  }

  .footerInner {
    display: flex;
    justify-content: center;
  }
}

.tabPanelRoot {
  // height: 50vh;
  overflow-y: auto;
  min-height: auto;
  width: 100%;
}

/* Webkit-based browsers (Chrome, Safari) */
.tabPanelRoot::-webkit-scrollbar {
  width: 6px;
  /* Set the width of the scrollbar */
}

.tabPanelRoot::-webkit-scrollbar-track {
  background: #f1f1f1;
  /* Track color */
}

.tabPanelRoot::-webkit-scrollbar-thumb {
  background: #888;
  /* Thumb color */
  border-radius: 10px;
  /* Round the corners */
}

.tabPanelRoot::-webkit-scrollbar-thumb:hover {
  background: #555;
  /* Thumb color when hovered */
}

/* Firefox */
.tabPanelRoot {
  scrollbar-width: thin;
  /* Set the width to thin */
  scrollbar-color: #888 #f1f1f1;
  /* Thumb and track color */
}

.switchRoot {
  padding: 0px !important;
}

.colorPickerModal {
  width: 450px !important;
}

.upcomingFeatureRoot {
  padding: 16px;
  // height: 60vh;
}

.customSelect select {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding-left: 5px !important;
  padding-right: 30px;
}

.css-1hq6au8 {
  padding-right: 8px;
}

.css-hcyaiw {
  height: 42px;
}
