import { AiOutlineInfoCircle } from 'react-icons/ai'
import {
  BsApple,
  BsArrowClockwise,
  BsArrowCounterclockwise,
  BsArrowDown,
  BsArrowRight,
  BsBadgeCc,
  BsBoxArrowUpRight,
  BsBuilding,
  BsCalendar3,
  BsCardText,
  BsCheck,
  BsCheckLg,
  BsChevronBarUp,
  BsChevronDown,
  BsChevronExpand,
  BsChevronLeft,
  BsChevronRight,
  BsChevronUp,
  BsClock,
  BsDownload,
  BsEnvelope,
  BsFacebook,
  BsFilePdf,
  BsFiletypeJpg,
  BsFiletypePng,
  BsFiletypeSvg,
  BsFillCheckSquareFill,
  BsFillExclamationCircleFill,
  BsFilterCircle,
  BsFullscreen,
  BsFullscreenExit,
  BsGeoAlt,
  BsGlobeAmericas,
  BsLayoutTextSidebarReverse,
  BsLightbulb,
  BsLink,
  BsLinkedin,
  BsList,
  BsMic,
  BsPauseFill,
  BsPersonFill,
  BsPip,
  BsPipFill,
  BsPlayBtn,
  BsPlayCircle,
  BsSearch,
  BsSend,
  BsSendXFill,
  BsShare,
  BsSpotify,
  BsTag,
  BsTelephone,
  BsTrash2,
  BsTwitterX,
  BsUpload,
  BsVolumeDownFill,
  BsVolumeMute,
  BsVolumeUp,
  BsX,
  BsXLg,
  BsYoutube
} from 'react-icons/bs'
import { CiYoutube } from 'react-icons/ci'
import { FaCircleCheck } from 'react-icons/fa6'
import { FiMic } from 'react-icons/fi'
import { IoMdRadioButtonOff } from 'react-icons/io'
import { RiCheckboxBlankFill, RiCheckboxBlankLine, RiEarthLine, RiMapPinLine, RiRadioButtonFill, RiVolumeDownLine } from 'react-icons/ri'
import { RxBox, RxBoxModel, RxSlash, RxTrackNext } from 'react-icons/rx'
import { TfiShareAlt } from 'react-icons/tfi'
export const TheIcons = {
  SendXFill            : BsSendXFill,
  Reset: BsArrowCounterclockwise,
  IoMdRadioButtonOff   : IoMdRadioButtonOff,
  RiCheckboxBlankLine  : RiCheckboxBlankLine,
  FaCircleCheck        : FaCircleCheck,
  AiOutlineInfoCircle  : AiOutlineInfoCircle,
  DownChevron          : BsChevronDown,
  BsEnvelope           : BsEnvelope,
  BsCheckLg            : BsCheckLg,
  Close                : BsX,
  LeftChevron          : BsChevronLeft,
  RightChevron         : BsChevronRight,
  RightArrow           : BsArrowRight,
  BarUp                : BsChevronBarUp,
  BsArrowDown          : BsArrowDown,
  BsFillCheckSquareFill: BsFillCheckSquareFill,
  RiRadioButtonFill    : RiRadioButtonFill,
  Slash                : RxSlash,
  Calendar             : BsCalendar3,
  Clock                : BsClock,
  MapPin               : RiMapPinLine,
  Play                 : CiYoutube,
  //contentful only icons
  CInlineEntry: RxBoxModel,
  CBlockEntry : RxBox,
  Tag: BsTag,
  BsLayoutTextSidebarReverse: BsLayoutTextSidebarReverse,
  BsBoxArrowUpRight: BsBoxArrowUpRight,
  BsCardText: BsCardText,
  Download: BsDownload,
  BsCheck: BsCheck,
  Apple: BsApple,
  Delete: BsTrash2,
  BsYoutube: BsYoutube,
  UpChevron: BsChevronUp,
  Mics: BsMic,
  GeoAlt: BsGeoAlt,
  RiCheckboxBlankFill: RiCheckboxBlankFill,
  BsLinkedin: BsLinkedin,
  Buildings: BsBuilding,
  Spotify: BsSpotify,
  CardText: BsCardText,
  Pdf: BsFilePdf,
  Mic: FiMic,
  Upload: BsUpload,
  Bulb: BsLightbulb,
  Earth: RiEarthLine,
  Exclamation: BsFillExclamationCircleFill,
  CirclePlay: BsPlayCircle,
  Telephone: BsTelephone,
  Share: BsShare,
  LinkedIn: BsLinkedin,
  Facebook: BsFacebook,
  Twitter: BsTwitterX,
  Send: BsSend,
  Link: BsLink,
  Search: BsSearch,
  Profile: BsPersonFill,
  Hamburger: BsList,
  Linkedin: BsLinkedin,
  Email: BsSend,
  Filter: BsFilterCircle,
  VolumeDown: RiVolumeDownLine,
  VolumeUp: BsVolumeUp,
  VolumeMute: BsVolumeMute,
  VolumeFill: BsVolumeDownFill,
  Fullscreen: BsFullscreen,
  FullscreenExit: BsFullscreenExit,
  Pause: BsPauseFill,
  Skip: RxTrackNext,
  Caption: BsBadgeCc,
  Pip: BsPip,
  PipFill: BsPipFill,
  Expand: TfiShareAlt,
  BsPlayBtn: BsPlayBtn,
  BsXLarge: BsXLg,
  Globe: BsGlobeAmericas,
  Svg: BsFiletypeSvg,
  Png: BsFiletypePng,
  Jpg: BsFiletypeJpg,
  Replay: BsArrowClockwise,
  ChevronExpand: BsChevronExpand
}