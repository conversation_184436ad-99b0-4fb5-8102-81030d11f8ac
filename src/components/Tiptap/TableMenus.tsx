import { Menu } from '@contentful/f36-components'
import { useCurrentEditor } from '@tiptap/react'
import React, { useEffect, useRef } from 'react'

type TableMenusProp = {
  contextMenuPosition: {
    x: number
    y: number
  }
  setShowContextMenu: (show: boolean) => void
  showContextMenu: boolean
}
/**
 * TableMenus
 *
 * A component that renders a context menu for tables.
 *
 * The component is a wrapper around the {@link Menu} component from
 * `@contentful/f36-components`. The menu is rendered at the position specified
 * by the `contextMenuPosition` prop.
 *
 * The menu contains a list of actions that can be performed on the table:
 * - Add row above
 * - Add row below
 * - Add column left
 * - Add column right
 * - Toggle header row
 * - Delete row
 * - Delete column
 * - Delete table
 *
 * Each menu item is disabled if the corresponding action is not allowed
 * on the table.
 *
 * The component also handles the case when the user clicks outside of the
 * menu. In this case, the menu is closed.
 *
 * @param {TableMenusProp} props - The props object.
 * @param {Object} props.contextMenuPosition - The position of the menu.
 * @param {number} props.contextMenuPosition.x - The x coordinate.
 * @param {number} props.contextMenuPosition.y - The y coordinate.
 * @param {boolean} props.showContextMenu - Whether the menu should be shown.
 * @param {function} props.setShowContextMenu - The function to call when the menu should be closed.
 */
export const TableMenus = (props: TableMenusProp) => {
  const { editor } = useCurrentEditor()
  const { x, y } = props.contextMenuPosition
  const { showContextMenu, setShowContextMenu } = props

  const menuRef = useRef(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // @ts-ignore
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setShowContextMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  if (!editor) {
    return null
  }

  return (
    <>
      {showContextMenu && editor.can().deleteTable() && (
        <div
          // ref={menuRef}
          style={{
            position: 'absolute',
            top: y,
            left: x,
          }}
        >
          <Menu isOpen={true}>
            <Menu.Trigger>
              <div></div>
            </Menu.Trigger>
            <Menu.List ref={menuRef}>
              <Menu.Item
                onClick={() => editor.chain().focus().addRowBefore().run()}
                isDisabled={!editor.can().addRowBefore()}
              >
                Add row above
              </Menu.Item>
              <Menu.Item
                onClick={() => editor.chain().focus().addRowAfter().run()}
                isDisabled={!editor.can().addRowAfter()}
              >
                Add row below
              </Menu.Item>
              <Menu.Item
                onClick={() => editor.chain().focus().addColumnBefore().run()}
                isDisabled={!editor.can().addColumnBefore()}
              >
                Add column left
              </Menu.Item>
              <Menu.Item
                onClick={() => editor.chain().focus().addColumnAfter().run()}
                isDisabled={!editor.can().addColumnAfter()}
              >
                Add column right
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item
                onClick={() => editor.chain().focus().toggleHeaderRow().run()}
                isDisabled={!editor.can().toggleHeaderRow()}
              >
                Toggle header row
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item
                onClick={() => editor.chain().focus().deleteRow().run()}
                isDisabled={!editor.can().deleteRow()}
              >
                Delete row
              </Menu.Item>
              <Menu.Item
                onClick={() => editor.chain().focus().deleteColumn().run()}
                isDisabled={!editor.can().deleteColumn()}
              >
                Delete column
              </Menu.Item>
              <Menu.Item
                onClick={() => editor.chain().focus().deleteTable().run()}
                isDisabled={!editor.can().deleteTable()}
              >
                Delete table
              </Menu.Item>
            </Menu.List>
          </Menu>

          {/*<button*/}
          {/*  onClick={() => editor.chain().focus().addColumnAfter().run()}*/}
          {/*  disabled={!editor.can().addColumnAfter()}*/}
          {/*>*/}
          {/*  addColumnAfter*/}
          {/*</button>*/}
          {/*<button*/}
          {/*  onClick={() => editor.chain().focus().deleteColumn().run()}*/}
          {/*  disabled={!editor.can().deleteColumn()}*/}
          {/*>*/}
          {/*  deleteColumn*/}
          {/*</button>*/}
          {/*<button*/}
          {/*  onClick={() => editor.chain().focus().addRowBefore().run()}*/}
          {/*  disabled={!editor.can().addRowBefore()}*/}
          {/*>*/}
          {/*  addRowBefore*/}
          {/*</button>*/}
          {/*<button*/}
          {/*  onClick={() => editor.chain().focus().addRowAfter().run()}*/}
          {/*  disabled={!editor.can().addRowAfter()}*/}
          {/*>*/}
          {/*  addRowAfter*/}
          {/*</button>*/}
          {/*<button*/}
          {/*  onClick={() => editor.chain().focus().deleteRow().run()}*/}
          {/*  disabled={!editor.can().deleteRow()}*/}
          {/*>*/}
          {/*  deleteRow*/}
          {/*</button>*/}
          {/*<button*/}
          {/*  onClick={() => editor.chain().focus().deleteTable().run()}*/}
          {/*  disabled={!editor.can().deleteTable()}*/}
          {/*>*/}
          {/*  deleteTable*/}
          {/*</button>*/}
          {/*<button*/}
          {/*  onClick={() => editor.chain().focus().mergeCells().run()}*/}
          {/*  disabled={!editor.can().mergeCells()}*/}
          {/*>*/}
          {/*  mergeCells*/}
          {/*</button>*/}
          {/*<button*/}
          {/*  onClick={() => editor.chain().focus().splitCell().run()}*/}
          {/*  disabled={!editor.can().splitCell()}*/}
          {/*>*/}
          {/*  splitCell*/}
          {/*</button>*/}
          {/*<button*/}
          {/*  onClick={() => editor.chain().focus().toggleHeaderColumn().run()}*/}
          {/*  disabled={!editor.can().toggleHeaderColumn()}*/}
          {/*>*/}
          {/*  toggleHeaderColumn*/}
          {/*</button>*/}
          {/*<button*/}
          {/*  onClick={() => editor.chain().focus().toggleHeaderRow().run()}*/}
          {/*  disabled={!editor.can().toggleHeaderRow()}*/}
          {/*>*/}
          {/*  toggleHeaderRow*/}
          {/*</button>*/}
          {/*<button*/}
          {/*  onClick={() => editor.chain().focus().mergeOrSplit().run()}*/}
          {/*  disabled={!editor.can().mergeOrSplit()}*/}
          {/*>*/}
          {/*  mergeOrSplit*/}
          {/*</button>*/}
        </div>
      )}
    </>
  )
}
