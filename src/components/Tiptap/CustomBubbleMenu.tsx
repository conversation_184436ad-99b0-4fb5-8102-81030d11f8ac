import { BubbleMenu, useCurrentEditor } from '@tiptap/react'
import React from 'react'

/**
 * A custom bubble menu that includes buttons for formatting text as bold, italic, and underlined.
 *
 * The buttons are conditionally rendered based on the current state of the editor. If the current
 * selection is bold, the bold button is rendered with the class 'is-active'. The same applies for the
 * italic and underline buttons.
 *
 * When a button is clicked, the corresponding formatting action is executed on the current selection.
 *
 * The bubble menu is rendered if and only if the current editor is defined.
 */
const CustomBubbleMenu = () => {
  const { editor } = useCurrentEditor()
  return (
    <>
      {editor && (
        <BubbleMenu
          className='bubble-menu'
          tippyOptions={{ duration: 100 }}
          editor={editor}
        >
          <button
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={editor.isActive('bold') ? 'is-active' : ''}
          >
            Bold
          </button>
          <button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={editor.isActive('italic') ? 'is-active' : ''}
          >
            Italic
          </button>
          <button
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            className={editor.isActive('underline') ? 'is-active' : ''}
          >
            Underline
          </button>
        </BubbleMenu>
      )}
    </>
  )
}

export default CustomBubbleMenu
