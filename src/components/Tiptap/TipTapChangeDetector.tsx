import { useCurrentEditor } from '@tiptap/react'
import { EditorAppSDK } from 'contentful-ui-extensions-sdk'
import React, { useEffect } from 'react'
interface TestFuncProps {
  sdk: EditorAppSDK
  fieldId: string
  currentLocale: string
}

/**
 * TipTapChangeDetector is a React functional component that observes changes
 * to a specific Contentful field's value for a given locale and updates the 
 * TipTap editor content accordingly.
 *
 * It sets up a subscription to the field's value changes using the Contentful SDK,
 * and updates the editor's content when a new value is detected. The component
 * ensures the subscription is cleaned up on component unmount to prevent memory leaks.
 *
 * @param {Object} props - The properties object.
 * @param {EditorAppSDK} props.sdk - The Contentful SDK instance for accessing entry fields.
 * @param {string} props.fieldId - The ID of the field to observe for value changes.
 * @param {string} props.currentLocale - The locale of the field to observe.
 */

const TipTapChangeDetector: React.FC<TestFuncProps> = ({
  sdk,
  fieldId,
  currentLocale,
}) => {
  const { editor } = useCurrentEditor()

  useEffect(() => {
    if (currentLocale === 'en-CA' || !sdk || !fieldId) return

    // Function to update editor content when value changes
    const updateEditorContent = (value: any) => {
      if (value) {
        editor?.commands.setContent(value)
      }
    }

    // Subscribe to value changes for the specific locale and field
    const unsubscribe = sdk.entry.fields[fieldId]
      .getForLocale(currentLocale)
      .onValueChanged(updateEditorContent)

    // Cleanup the subscription on unmount
    return () => {
      unsubscribe()
    }
  }, [currentLocale, fieldId, sdk, editor])

  return <></>
}

export default TipTapChangeDetector
