import { EditorAppSDK } from '@contentful/app-sdk'
import {
  Button,
  Form,
  FormControl,
  Modal,
  Textarea,
} from '@contentful/f36-components'
import { useSDK } from '@contentful/react-apps-toolkit'
import { generateJSON, useCurrentEditor } from '@tiptap/react'
import React from 'react'
import { extensions } from '..'

interface HtmlInputModalProps {
  isHTMLModalOpen: boolean
  setIsHTMLModalOpen: React.Dispatch<React.SetStateAction<boolean>>
}

function HtmlInputModal(props: HtmlInputModalProps) {
  const { isHTMLModalOpen, setIsHTMLModalOpen } = props
  const [htmlContent, setHtmlContent] = React.useState('')
  const { editor } = useCurrentEditor()
  const sdk = useSDK<EditorAppSDK>()

  function htmlSubmitHandler() {
    const scribeHowJSON = generateJSON(htmlContent, extensions)
    editor?.chain().focus().setContent(scribeHowJSON).run()
    sdk.entry.fields['content']
      .getForLocale('en-CA')
      .setValue(scribeHowJSON)
      .then(() => {
        sdk.notifier.success('HTML content converted successfully')
      })
    setIsHTMLModalOpen(false)
  }

  return (
    <Modal onClose={() => setIsHTMLModalOpen(false)} isShown={isHTMLModalOpen}>
      {() => (
        <>
          <Modal.Header
            title='Html to Richtext Converter'
            onClose={() => setIsHTMLModalOpen(false)}
          />
          <Modal.Content>
            <Form onSubmit={htmlSubmitHandler}>
              <FormControl>
                <FormControl.Label isRequired>HTML Code</FormControl.Label>
                <Textarea
                  value={htmlContent}
                  placeholder='Enter HTML code here'
                  onChange={(e) => {
                    setHtmlContent(e.target.value)
                  }}
                />
              </FormControl>
            </Form>
          </Modal.Content>
          <Modal.Controls>
            <Button
              size='small'
              variant='transparent'
              onClick={() => setIsHTMLModalOpen(false)}
            >
              Close
            </Button>
            <Button
              size='small'
              variant='positive'
              isDisabled={htmlContent.length === 0}
              onClick={htmlSubmitHandler}
            >
              Convert
            </Button>
          </Modal.Controls>
        </>
      )}
    </Modal>
  )
}

export default HtmlInputModal
