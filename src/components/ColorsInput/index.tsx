import { Icon<PERSON>utton, Tooltip } from '@contentful/f36-components'
import React, { Fragment } from 'react'
//import ToolTip from '../Tooltip'
import Undo from '../../assets/icons/Undo'
import { GlobalContext } from '../../contexts/globalContext'
import '../Tiptap/styles.scss'
import { colorsValues } from './@core/colorsValues'
import SingleColor from './@core/SingleColor'
import styles from './styles/colorInput.module.scss'

const ColorsInput = ({
                       name,
                       onClick,
                       activeColor,
                       showUndo = true,
                     }: {
  name: string
  onClick: ({ name, value }: { name: string; value: string | number }) => void
  activeColor: string
  showUndo?: boolean
}) => {
  const { setActiveColor } = React.useContext(GlobalContext)
  console.warn('activeColoe', activeColor)

  function handleReset() {
    if (name === 'Color') {
      setActiveColor((prev) => {
        return {
          ...prev,
          fontColor: 'black',
        }
      })
    }
    else {
      setActiveColor((prev) => {
        return {
          ...prev,
          highlightColor: null,
        }
      })
    }
  }

  return (
    <div
      style={{
        //display : 'flex',
        flexWrap: 'wrap',
        gap     : '0.5rem',
        width   : 300,
        margin  : '0 20px',
      }}
    >
      {showUndo && (
        <div className={styles.colorSelectionDiv}>
          {/*<ButtonGroup>*/}
          <Tooltip placement='bottom' content={`Reset ${name}`}>
            <IconButton
              size={'medium'}
              variant='transparent'
              aria-label='reset'
              icon={<Undo />}
              onClick={handleReset}
            />
          </Tooltip>
          {/*</ButtonGroup>*/}
        </div>
      )}
      {colorsValues.map((colors) => (
        <Fragment key={colors.name}>
          <div className={styles.colorRoot}>
            <p className={styles.colorTitle}>{colors.name}</p>
            <div className={styles.colorTiles}>
              {colors.colors.map((color) => (
                <Fragment key={color.hex}>
                  <Tooltip
                    placement='top'
                    content={`${color.name} : ${color.codeName}`}
                  >
                    <div className={styles.singleColorRoot}>
                      <SingleColor
                        activeColor={activeColor}
                        name={name}
                        onClick={onClick}
                        value={color.hex}
                      />
                    </div>
                  </Tooltip>
                </Fragment>
              ))}
            </div>
          </div>
        </Fragment>
      ))}
    </div>
  )
}

export default ColorsInput