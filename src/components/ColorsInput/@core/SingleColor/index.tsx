/**
 * SingleColor Component - Individual Color Swatch
 *
 * Represents a single color option in the color picker grid. This component
 * renders an individual color swatch that users can click to select a color.
 * It provides visual feedback for the active state and handles click events.
 *
 * Key Features:
 * - Visual color swatch representation
 * - Active state highlighting
 * - Click event handling with structured data
 * - Accessible color selection interface
 * - CSS module styling for consistent appearance
 */

import React from 'react';
import styles from './styles/singleColor.module.scss';

/**
 * SingleColor Main Component Function
 *
 * Renders an individual color swatch with click handling and active state management.
 * The component displays the color as a background and provides visual feedback
 * when the color is currently selected.
 *
 * @param onClick - Callback function triggered when the color is selected
 * @param name - Color category name passed to the click handler
 * @param value - Color value (hex string or other format)
 * @param activeColor - Currently active color for comparison and highlighting
 * @returns JSX.Element - The color swatch button
 */
const SingleColor = ({
  onClick,
  name,
  value,
  activeColor,
}: {
  onClick: ({ name, value }: { name: string; value: string | number }) => void  // Click handler with structured data
  name: string                                                                    // Color category identifier
  value: string | number                                                          // Color value to display and select
  activeColor: string
}) => {
  return (
    <div
      className={`${styles.color} ${activeColor === value ? styles.colorActive : ''
        }`}
      style={{ backgroundColor: value.toString() }}
      onClick={() => onClick({ name, value })}
    ></div>
  )
}

export default SingleColor
