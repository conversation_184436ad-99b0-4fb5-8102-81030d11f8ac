import React from 'react'
import styles from './styles/singleColor.module.scss'

const SingleColor = ({
  onClick,
  name,
  value,
  activeColor,
}: {
  onClick: ({ name, value }: { name: string; value: string | number }) => void
  name: string
  value: string | number
  activeColor: string
}) => {
  return (
    <div
      className={`${styles.color} ${
        activeColor === value ? styles.colorActive : ''
      }`}
      style={{ backgroundColor: value.toString() }}
      onClick={() => onClick({ name, value })}
    ></div>
  )
}

export default SingleColor
