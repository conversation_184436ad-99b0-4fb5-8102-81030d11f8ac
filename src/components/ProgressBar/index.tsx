import React from 'react'
import styles from './index.module.css'

interface Props {
  progressWidth?: string
  completedSteps?: number
  totalSteps?: number
}

function ProgressBar(props: Props) {
  let className = `${styles.simpleBar}`

  const { completedSteps = 0, progressWidth, totalSteps = 0 } = props

  let progress = (completedSteps / totalSteps) * 100 + '%'

  return (
    <div
      className={className}
      style={{
        width: progress || progressWidth || '0%',
        transition: `width 500ms ease-in-out`,
        // borderRadius: '500px',
        height: '5px',
      }}
    />
  )
}

export default ProgressBar
