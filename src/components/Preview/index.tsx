import { <PERSON>barAppSDK } from '@contentful/app-sdk'
import { <PERSON>, Button } from '@contentful/f36-components'
import React, { useEffect } from 'react'
import { ENV_VARIABLES } from '../../constant/variables'
import { getUpdatedBranchFrontName } from '../PageSettings/utils'

/**
 * PreviewUrls Component - Provides quick access to preview page content on different deployment branches
 *
 * This component generates and displays preview URLs for Contentful page entries, allowing content
 * editors to quickly view their content on staging and production environments. It automatically
 * detects the domain configuration and monitors entry publication status.
 *
 * Key Features:
 * - Generates preview URLs for both staging and production environments
 * - Automatically detects domain from entry metadata tags or configurations field
 * - Monitors entry publication status to enable/disable production preview
 * - Listens for real-time changes to entry fields and metadata
 * - Opens preview URLs in new browser tabs
 * - Shows appropriate error messages when domain is not configured
 * - Supports multiple deployment branches and domain-specific URL generation
 *
 * @param sdk - Contentful Sidebar App SDK instance for accessing entry data and events
 */
function PreviewUrls({ sdk }: { sdk: SidebarAppSDK }) {
  const [isPagePublished, setIsPagePublished] = React.useState(false)
  const [pageDomain, setPageDomain] = React.useState('')

  // Get current entry slug for URL construction
  let slug = sdk.entry.fields['slug'].getValue()

  // Environment configuration for different deployment branches
  const MainPreviewBranch = ENV_VARIABLES.MainPreviewBranch
  const mainBranch = ENV_VARIABLES.mainBranch

  /**
   * Monitor entry publication status changes
   *
   * Listens for system changes to determine if the entry is published or in draft state.
   * This affects whether the production preview button is enabled.
   * Only published entries can be previewed on the main/production branch.
   */
  useEffect(() => {
    sdk.entry.onSysChanged(() => {
      const isDraft = sdk.entry.getSys().fieldStatus['*']['en-CA'] === 'draft'

      setIsPagePublished(!isDraft)
    })
  }, [sdk])

  /**
   * Monitor domain configuration changes
   *
   * Sets up event listeners to detect domain changes from two sources:
   * 1. Configurations field changes (domain field within configurations)
   * 2. Metadata tag changes (domain tags applied to the entry)
   *
   * Domain detection priority:
   * 1. First checks configurations.domain field
   * 2. Falls back to domain tags in entry metadata
   * 3. Converts full domain names to short codes (e.g., 'domainAltusGroupCom' -> 'agl')
   *
   * This ensures preview URLs are always generated for the correct domain/brand.
   */
  useEffect(() => {
    const handleFieldChange = (value: any) => {
      let domain = value?.domain // Get the value of the "domain" field

      if (!domain) {
        const pageTags = sdk.entry.getMetadata()?.tags.map((tag) => tag.sys.id)

        domain = pageTags?.find((tag) => tag.startsWith('domain'))

        if (domain) domain = getDomainShortName(domain)
      }

      setPageDomain(domain)
    }

    const handleTagsChange = () => {
      const pageTags = sdk.entry.getMetadata()?.tags.map((tag) => tag.sys.id)

      let domain = pageTags?.find((tag) => tag.startsWith('domain'))

      if (domain) domain = getDomainShortName(domain)
      if (domain) setPageDomain(domain)
    }

    // Listen for field value changes
    const fieldUnsubscribe =
      sdk.entry.fields['configurations'].onValueChanged(handleFieldChange)

    // Listen for tag changes
    const tagsUnsubscribe = sdk.entry.onMetadataChanged(handleTagsChange)

    // Cleanup on unmount
    return () => {
      fieldUnsubscribe()
      tagsUnsubscribe()
    }
  }, [sdk])

  /**
   * Opens preview URL for the specified deployment branch
   *
   * @param branch - Deployment branch name (e.g., 'main', 'staging', 'dev')
   *
   * Constructs the appropriate preview URL based on:
   * - Branch name (determines if production or staging URL)
   * - Domain configuration (determines which brand/website)
   * - Entry slug (determines specific page path)
   *
   * Opens the constructed URL in a new browser tab for content preview.
   */
  const handlePreviewUrlByBranch = (branch: string) => {
    const previewUrl = getPreviewUrlByBranch(branch, pageDomain || 'agl')
    window.open(previewUrl + slug, '_blank')
  }

  return (
    <Box
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'start',
        alignItems: 'start',
        gap: '0.5rem',
        width: '100%',
      }}
    >
      <h2
        style={{
          width: '100%',
          marginBottom: '0.5rem',
          fontSize: '0.75rem',
          display: 'flex',
          justifyContent: 'space-between',
          borderBottom: '1px solid rgb(174, 193, 204)',
          alignItems: 'center',
          fontWeight: 600,
          color: '#67728a',
          lineHeight: '1.5rem',
        }}
      >
        Preview Urls
      </h2>
      <Box className='flex flex-col gap-3 w-full'>
        <Button
          variant='secondary'
          className='w-full'
          isDisabled={!pageDomain}
          onClick={() => handlePreviewUrlByBranch(MainPreviewBranch)}
        >
          Open {getUpdatedBranchFrontName(MainPreviewBranch)}
        </Button>
        <Button
          variant='secondary'
          className='w-full'
          isDisabled={!isPagePublished || !pageDomain}
          onClick={() => handlePreviewUrlByBranch(mainBranch)}
        >
          Open {mainBranch}
        </Button>
      </Box>
      {!pageDomain && (
        <p
          style={{
            fontSize: '0.75rem',
            color: '#ff1313',
          }}
        >
          Please select a domain tag
        </p>
      )}
    </Box>
  )
}

export default PreviewUrls

function getDomainShortName(domain: string) {
  switch (domain) {
    case 'domainAltusGroupCom':
      return 'agl'
    case 'domainFinanceActiveCom':
      return 'fia'
    case 'domainReonomyCom':
      return 'reo'
    case 'domainVerifinoCom':
      return 'ver'
    case 'domainOne11Com':
      return 'o11'
    default:
      return ''
  }
}

export const getPreviewUrlByBranch = (branch: string, domain: string) => {
  if (branch === 'main') {
    switch (domain) {
      case 'agl':
        return 'https://www.altusgroup.com/'
      case 'fia':
        return 'https://financeactive.com/'
      case 'reo':
        return 'https://reonomy.com/'
      case 'ver':
        return 'https://verifino.com/'
      case 'o11':
        return 'https://msa-o11-v3w-git-main-altus.vercel.app'
      default:
        return 'https://www.altusgroup.com/'
    }
  } else {
    return `https://msa-${domain}-v3w-git-${branch}-altus.vercel.app/`
  }
}
