export const getConfigurationsCollectionQuery = () => {
  return `{
	configurationsCollection(preview: true) {
    items {
      sys {
        id
      }
      internalName
      type
      scope
      data {
        json
      }
    }
  }
	}`
}

export const getComponentNotificationCollectionQuery = () => {
  return `{
  componentNotificationCollection(preview:true){
    items{
      internalName
      sys{
        id
      }
        template
    }
  }}`
}
