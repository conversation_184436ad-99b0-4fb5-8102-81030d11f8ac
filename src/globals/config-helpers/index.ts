import * as contentful from 'contentful-management'
import { ENV_VARIABLES } from '../../constant/variables'
import { getEntryDataById } from '../utils'

export type SCOPE = 'MSA' | 'AGL' | 'FIA' | 'O11' | 'REO' | 'VER'

export type EntryType =
  | 'Crosspost'
  | 'Experimentation'
  | 'Locale'
  | 'DV'
  | 'DTS CTA Mapping'

  /**
   * Creates a new configuration entry.
   * @param {{ data: any; internalName: string; type: EntryType; scope: SCOPE; envId: string; spaceId: string; }} param0 - An object containing the data, internalName, type, scope, envId and spaceId.
   * @returns {Promise<string>} - A promise that resolves to the id of the newly created entry.
   */
export const CreateConfigurationEntry = async ({
  data,
  internalName,
  type,
  scope,
  envId,
  spaceId,
}: {
  data: any
  type: EntryType
  internalName: string
  scope: SCOPE
  envId: string
  spaceId: string
}) => {
  const fields = {
    internalName: {
      'en-CA': internalName,
    },
    type: {
      'en-CA': type,
    },
    scope: {
      'en-CA': scope,
    },
    data: {
      'en-CA': {
        content: [
          {
            content: [
              {
                data: {},
                marks: [],
                nodeType: 'text',
                value: JSON.stringify(data),
              },
            ],
            data: {},
            nodeType: 'paragraph',
          },
        ],
        data: {},
        nodeType: 'document',
      },
    },
  }

  const client = contentful.createClient({
    space: ENV_VARIABLES.contentfulSpaceID,
    accessToken: ENV_VARIABLES.contentfulToken,
  })

  let payload: any = {
    fields: fields,
  }

  const res = await client
    .getSpace(ENV_VARIABLES.contentfulSpaceID)
    .then((space) => {
      return space.getEnvironment(ENV_VARIABLES.contentfulEnvironment).then(async (environment) => {
        const res = await environment.createEntry('configurations', payload)

        return res
      })
    })
    .catch((error) => {
      return error
    })

  const newContentId = res?.sys?.id || ''

  return newContentId
}

/**
 * Updates a Contentful configuration entry with new data.
 *
 * @param {string} contentId - The ID of the entry to update.
 * @param {any} data - The new data to write to the entry.
 * @param {string} envId - The ID of the Contentful environment.
 * @param {string} spaceId - The ID of the Contentful space.
 * @returns {Promise<Object>} - A promise that resolves with the updated entry.
 */
export const UpdateConfigurationEntry = async ({
  contentId,
  data,
  envId,
  spaceId,
}: {
  contentId: string
  data: any
  envId: string
  spaceId: string
}) => {
  if (contentId) {
    const response = await getEntryDataById(contentId).then((res: any) => {
      return res.fields
    })

    let updatedPayload = {
      ...response,
      data: {
        'en-CA': {
          content: [
            {
              content: [
                {
                  data: {},
                  marks: [],
                  nodeType: 'text',
                  value: JSON.stringify(data),
                },
              ],
              data: {},
              nodeType: 'paragraph',
            },
          ],
          data: {},
          nodeType: 'document',
        },
      },
    }

    const client = contentful.createClient({
      space: ENV_VARIABLES.contentfulSpaceID,
      accessToken: ENV_VARIABLES.contentfulToken,
    })

    const res = await client
      .getSpace(ENV_VARIABLES.contentfulSpaceID)
      .then((space) => {
        return space.getEnvironment(ENV_VARIABLES.contentfulEnvironment).then((environment) => {
          return environment.getEntry(contentId).then(async (entry) => {
            entry.fields = { ...entry.fields, ...updatedPayload }

            const res = await entry.update()

            return res
          })
        })
      })
      .catch((error) => console.error(error))
    return res
  }
}
