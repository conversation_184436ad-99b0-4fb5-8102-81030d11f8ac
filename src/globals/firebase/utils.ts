import { ref, set } from 'firebase/database'
import { domainsConfig } from '../../components/Crosspost/utils'
import { PreviewBranchUrl } from '../../components/Dashboard/Notifications/utils'
import { database } from '../firebase/firebase-init'
import { CreateNotificationPayload } from './interface'

// const URL = 'http://localhost:3000/'

export const generateRandomId = () => {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  )
}

export const CreatePushNotification = async (
  notificationPayload: CreateNotificationPayload
) => {
  try {
    const res = await set(
      ref(database, 'notifications/' + generateRandomId()),
      notificationPayload
    ).then(() => true)
    return res
  } catch (error) {
    return false
  }
}

export const sendMessage = async (
  notificationPayload: CreateNotificationPayload,
  domain?: string
) => {
  const environment = process.env.REACT_APP_CONTENTFUL_ENVIRONMENT

  const domainCode =
    domainsConfig.find((d) => d.domainKey === domain)?.key || 'agl'

  const URL = PreviewBranchUrl(domainCode)

  // const URL = 'http://localhost:3000/'

  const message1 = {
    data: notificationPayload,
    topic: `common-notification-${domain}-${environment}-development`,
  }

  try {
    const res = await fetch(`${URL}api/send-message/`, {
      body: JSON.stringify(message1),
      method: 'POST',
    })
      .then((res) => res.json())
      .then((x) => {
        return x
      })
    return res
  } catch (err) {
    return false
  }
}
