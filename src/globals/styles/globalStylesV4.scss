/**
This file is made for v4, it uses v4 naming convention for future.
There are instances where variable and classNames are used from "_vars.scss" but in future when we revisit,
we will consolidate both vars and vars2 into one and "_vars2.scss" will replace old "_vars.scss"
*/


/**
Primary colour classes
*/

.cp1 {
  color: $cp1;
  }

.cp1-50 {
  color: $cp1-50 !important;
  }

.cp1-100 {
  color: $cp1-100 !important;
  }

.cp1-200 {
  color: $cp1-200 !important;
  }

.cp1-300 {
  color: $cp1-300 !important;
  }

.cp1-400 {
  color: $cp1-400 !important;
  }

.cp1-600 {
  color: $cp1-600 !important;
  }

.cp1-700 {
  color: $cp1-700 !important;
  }

.cp1-800 {
  color: $cp1-800 !important;
  }

.cp1-900 {
  color: $cp1-900 !important;
  }

.cp2 {
  color: $cp2;
  }

.cp2-50 {
  color: $cp2-50 !important;
  }

.cp2-100 {
  color: $cp2-100 !important;
  }

.cp2-200 {
  color: $cp2-200 !important;
  }

.cp2-300 {
  color: $cp2-300 !important;
  }

.cp2-400 {
  color: $cp2-400 !important;
  }

.cp2-600 {
  color: $cp2-600 !important;
  }

.cp2-700 {
  color: $cp2-700 !important;
  }

.cp2-800 {
  color: $cp2-800 !important;
  }

.cp2-900 {
  color: $cp2-900 !important;
  }

/**
Secondary colour classes
*/

.cs1 {
  color: $cs1;
  }

.cs1-50 {
  color: $cs1-50 !important;
  }

.cs1-100 {
  color: $cs1-100 !important;
  }

.cs1-200 {
  color: $cs1-200 !important;
  }

.cs1-300 {
  color: $cs1-300 !important;
  }

.cs1-400 {
  color: $cs1-400 !important;
  }

.cs1-600 {
  color: $cs1-600 !important;
  }

.cs1-700 {
  color: $cs1-700 !important;
  }

.cs1-800 {
  color: $cs1-800 !important;
  }

.cs1-900 {
  color: $cs1-900 !important;
  }

.cs2 {
  color: $cs2;
  }

.cs2-50 {
  color: $cs2-50 !important;
  }

.cs2-100 {
  color: $cs2-100 !important;
  }

.cs2-200 {
  color: $cs2-200 !important;
  }

.cs2-300 {
  color: $cs2-300 !important;
  }

.cs2-400 {
  color: $cs2-400 !important;
  }

.cs2-600 {
  color: $cs2-600 !important;
  }

.cs2-700 {
  color: $cs2-700 !important;
  }

.cs2-800 {
  color: $cs2-800 !important;
  }

.cs2-900 {
  color: $cs2-900 !important;
  }

/**
Neutral colour classes
*/
.cn1 {
  color: $cn1 !important;
  }

.cn2 {
  color: $cn2 !important;
  }

.cn3 {
  color: $cn3 !important;
  }

.cn4 {
  color: $cn4 !important;
  }

.cn5 {
  color: $cn5 !important;
  }

.cn6 {
  color: $cn6 !important;
  }

.cn7 {
  color: $cn7 !important;
  }

.cn8 {
  color: $cn8 !important;
  }

/**
Accent colour classes
*/
.ca1-50 {
  color: $ca1-50 !important;
  }

.ca1-100 {
  color: $ca1-100 !important;
  }

.ca1-200 {
  color: $ca1-200 !important;
  }

.ca1-300 {
  color: $ca1-300 !important;
  }

.ca1-400 {
  color: $ca1-400 !important;
  }

.ca1-600 {
  color: $ca1-600 !important;
  }

.ca1-700 {
  color: $ca1-700 !important;
  }

.ca1-800 {
  color: $ca1-800 !important;
  }

.ca1-900 {
  color: $ca1-900 !important;
  }

/**
Background color classes for Primary colors
*/

.bcp1 {
  background-color: $cp1 !important;
  }

.bcp1-50 {
  background-color: $cp1-50 !important;
  }

.bcp1-100 {
  background-color: $cp1-100 !important;
  }

.bcp1-200 {
  background-color: $cp1-200 !important;
  }

.bcp1-300 {
  background-color: $cp1-300 !important;
  }

.bcp1-400 {
  background-color: $cp1-400 !important;
  }

.bcp1-600 {
  background-color: $cp1-600 !important;
  }

.bcp1-700 {
  background-color: $cp1-700 !important;
  }

.bcp1-800 {
  background-color: $cp1-800 !important;
  }

.bcp1-900 {
  background-color: $cp1-900 !important;
  }

.bcp2 {
  background-color: $cp2 !important;
  }

.bcp2-50 {
  background-color: $cp2-50 !important;
  }

.bcp2-100 {
  background-color: $cp2-100 !important;
  }

.bcp2-200 {
  background-color: $cp2-200 !important;
  }

.bcp2-300 {
  background-color: $cp2-300 !important;
  }

.bcp2-400 {
  background-color: $cp2-400 !important;
  }

.bcp2-600 {
  background-color: $cp2-600 !important;
  }

.bcp2-700 {
  background-color: $cp2-700 !important;
  }

.bcp2-800 {
  background-color: $cp2-800 !important;
  }

.bcp2-900 {
  background-color: $cp2-900 !important;
  }

/**
Background color classes for Secondary colors
*/

.bcs1 {
  background-color: $cs1 !important;
  }

.bcs1-50 {
  background-color: $cs1-50 !important;
  }

.bcs1-100 {
  background-color: $cs1-100 !important;
  }

.bcs1-200 {
  background-color: $cs1-200 !important;
  }

.bcs1-300 {
  background-color: $cs1-300 !important;
  }

.bcs1-400 {
  background-color: $cs1-400 !important;
  }

.bcs1-600 {
  background-color: $cs1-600 !important;
  }

.bcs1-700 {
  background-color: $cs1-700 !important;
  }

.bcs1-800 {
  background-color: $cs1-800 !important;
  }

.bcs1-900 {
  background-color: $cs1-900 !important;
  }

.bcs2 {
  background-color: $cs3 !important;
  }

.bcs2-50 {
  background-color: $cs2-50 !important;
  }

.bcs2-100 {
  background-color: $cs2-100 !important;
  }

.bcs2-200 {
  background-color: $cs2-200 !important;
  }

.bcs2-300 {
  background-color: $cs2-300 !important;
  }

.bcs2-400 {
  background-color: $cs2-400 !important;
  }

.bcs2-600 {
  background-color: $cs2-600 !important;
  }

.bcs2-700 {
  background-color: $cs2-700 !important;
  }

.bcs2-800 {
  background-color: $cs2-800 !important;
  }

.bcs2-900 {
  background-color: $cs2-900 !important;
  }

/**
Background color classes for Neutral colors
*/
.bcn1 {
  background-color: $cn1 !important;
  }

.bcn2 {
  background-color: $cn2 !important;
  }

.bcn3 {
  background-color: $cn3 !important;
  }

.bcn4 {
  background-color: $cn4 !important;
  }

.bcn5 {
  background-color: $cn5 !important;
  }

.bcn6 {
  background-color: $cn6 !important;
  }

.bcn7 {
  background-color: $cn7 !important;
  }

.bcn8 {
  background-color: $cn8 !important;
  }


/**
Background color classes for Accent colors
*/

@for $i from 1 through 10 {
  .ca#{$i} {
    color: map-get($accent-colors-map, $i);
    }

  .bca#{$i} {
    background-color: map-get($accent-colors-map, $i) !important;
    }
  }


@for $i from 1 through 10 {
  .bca#{$i}-50 {
    background-color: map-get(map-get($accent-colors, $i), "50") !important;
    }
  .bca#{$i}-100 {
    background-color: map-get(map-get($accent-colors, $i), "100") !important;
    }
  .bca#{$i}-200 {
    background-color: map-get(map-get($accent-colors, $i), "200") !important;
    }
  .bca#{$i}-300 {
    background-color: map-get(map-get($accent-colors, $i), "300") !important;
    }
  .bca#{$i}-400 {
    background-color: map-get(map-get($accent-colors, $i), "400") !important;
    }
  .bca#{$i}-600 {
    background-color: map-get(map-get($accent-colors, $i), "600") !important;
    }
  .bca#{$i}-700 {
    background-color: map-get(map-get($accent-colors, $i), "700") !important;
    }
  .bca#{$i}-800 {
    background-color: map-get(map-get($accent-colors, $i), "800") !important;
    }
  .bca#{$i}-900 {
    background-color: map-get(map-get($accent-colors, $i), "900") !important;
    }
  }