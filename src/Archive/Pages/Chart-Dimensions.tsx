import {
  Box,
  FormControl,
  Select,
  Table,
  Tooltip,
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import Info from '../../../../assets/icons/Info'
import { fetchGlobalConfigurationData } from '../../../../globals/utils'
import {
  createNewGlobalConfig,
  updateGlobalConfigData,
} from '../../../../redux/slices/dashboard/dvSlices'
import { RootState, useAppDispatch } from '../../../../redux/store'
import RevertButton from '../../../Buttons/RevertButton'
import SaveButton from '../../../Buttons/SaveButton'
import ColorPickerPopup from '../../../ConfigurationEngine/Components/ColorPickerPopup'

function ChartAndDimensions(props: any) {
  const dispatch = useAppDispatch()

  const [currentDimensionId, setCurrentDimensionId] = useState('')

  const [showColorPicker, setShowColorPicker] = useState(false)

  const [data, setData] = useState<any>({})

  const [loading, setLoading] = useState(false)

  const [unChangedData, setUnChangedData] = useState<any>({})

  const [isRevertible, setIsRevertible] = useState(false)

  const dvGlobalData = useSelector(
    (state: RootState) => state.dvDashboard.globalConfigData
  )

  const dvGlobalContentId = useSelector(
    (state: RootState) => state.dvDashboard.contentId
  )

  const handleChartDataChange = (key: string, value: string | boolean) => {
    const dataToUpdate = {
      ...data,
      chart: {
        ...data.chart,
        [key]: value,
      },
    }

    setData(dataToUpdate)
  }

  const handleSave = async () => {
    setLoading(true)
    const response: any = await fetchGlobalConfigurationData()
    let payload = {
      ...response?.data,
      DataVisualization: data,
    }

    if (dvGlobalContentId) {
      dispatch(
        updateGlobalConfigData({ contentId: dvGlobalContentId, payload })
      )
    } else {
      dispatch(createNewGlobalConfig(payload))
    }

    setLoading(false)
    setUnChangedData(data)
  }

  const revertChanges = () => {
    setData(unChangedData)
  }

  const handleDimensionColorChange = (value: string) => {
    const dataToUpdate = {
      ...data,
      chart: {
        ...data?.chart,
        dimensionColors: data?.chart?.dimensionColors
          .map((dc: { id: string }) =>
            dc.id === currentDimensionId ? { ...dc, color: value } : dc
          )
          .concat(
            data?.chart?.dimensionColors.find(
              (dc: { id: string }) => dc.id === currentDimensionId
            )
              ? []
              : [{ id: currentDimensionId, color: value }]
          ),
      },
    }

    setData(dataToUpdate)
  }

  const getDimensionColorById = (id: string) => {
    return (
      data?.chart?.dimensionColors.find((dimension: any) => dimension.id === id)
        ?.color || ''
    )
  }

  useEffect(() => {
    setData(dvGlobalData?.DataVisualization)
    setUnChangedData(dvGlobalData?.DataVisualization)
  }, [dvGlobalData, dvGlobalContentId])

  useEffect(() => {
    setIsRevertible(JSON.stringify(data) !== JSON.stringify(unChangedData))
  }, [data, unChangedData])

  return (
    <Box className='dashboardItemContainer'>
      <Box
        style={{
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          width: '100%',
          height: '100%',
          flexDirection: 'column',
          gap: '5px',
        }}
      >
        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
          }}
        >
          <FormControl
            id='original-domain'
            style={{
              width: '70%',
            }}
          >
            <div className='formLabelWithIcon'>
              <FormControl.Label>Default Chart</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Set this chart as the default view when the data visualization is loaded.'
              >
                <Info />
              </Tooltip>
            </div>
            <Select
              id='optionSelect-controlled'
              name='optionSelect-controlled'
              value={data?.chart?.defaultChart}
              onChange={(e) =>
                handleChartDataChange('defaultChart', e.target.value)
              }
              placeholder='Select Title Alignment'
              style={{
                width: '100%',
              }}
              isDisabled={loading}
            >
              <Select.Option value='Line-chart'>Line-chart</Select.Option>
              <Select.Option value='Bar-chart'>Bar-chart</Select.Option>
              <Select.Option value='Area-chart'>Area-chart</Select.Option>
              <Select.Option value='Area-chart'>Combo-chart</Select.Option>
              <Select.Option value='Area-chart'>Pie-chart</Select.Option>
              <Select.Option value='Area-chart'>Doughnut-chart</Select.Option>
            </Select>
          </FormControl>
        </Box>

        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
          }}
        >
          <FormControl
            id='original-domain'
            style={{
              width: '70%',
            }}
          >
            <div className='formLabelWithIcon'>
              <FormControl.Label>Dimensions Default Color</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Specify the default colors for each dimension in the chart.'
              >
                <Info />
              </Tooltip>
            </div>
            <div className='row'>
              <Table>
                <Table.Head>
                  <Table.Row>
                    <Table.Cell
                      style={{
                        padding: '10px !important',
                      }}
                    >
                      Dimension
                    </Table.Cell>
                    <Table.Cell
                      style={{
                        padding: '10px !important',
                      }}
                    >
                      Color
                    </Table.Cell>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {dimensions.map((name, i) => {
                    return (
                      <Table.Row key={name.id}>
                        <Table.Cell
                          style={{
                            padding: '10px !important',
                          }}
                        >
                          {name.title}
                        </Table.Cell>
                        <Table.Cell
                          style={{
                            padding: '10px !important',
                          }}
                        >
                          <div
                            className={`${
                              currentDimensionId === name.id && 'bg'
                            } edit`}
                            onClick={() => {
                              setShowColorPicker(true)
                              setCurrentDimensionId(name.id)
                            }}
                          >
                            {getDimensionColorById(name.id) ? (
                              <div
                                className='color'
                                style={{
                                  backgroundColor: getDimensionColorById(
                                    name.id
                                  ),
                                }}
                              ></div>
                            ) : (
                              <span>Pick</span>
                            )}
                          </div>
                        </Table.Cell>
                      </Table.Row>
                    )
                  })}
                </Table.Body>
              </Table>
            </div>
          </FormControl>
          <ColorPickerPopup
            isOpen={showColorPicker}
            handleClose={() => {
              setShowColorPicker(false)
            }}
            selectedColors={data?.chart?.dimensionColors.find(
              (color: any) => color.id === currentDimensionId
            )}
            onColorPick={(color: any) => {
              handleDimensionColorChange(color.value)
              setShowColorPicker(false)
            }}
          />
        </Box>
      </Box>
      <Box
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <RevertButton
          isDisabled={!isRevertible || loading}
          onClick={revertChanges}
          tooltipPlacement='right'
        ></RevertButton>
        <SaveButton
          onClick={handleSave}
          isDisabled={!isRevertible}
          isLoading={loading}
        ></SaveButton>
      </Box>
    </Box>
  )
}

export default ChartAndDimensions

const dimensions = [
  {
    id: '1',
    title: 'Dimension 1',
  },
  {
    id: '2',
    title: 'Dimension 2',
  },
  {
    id: '3',
    title: 'Dimension 3',
  },
  {
    id: '4',
    title: 'Dimension 4',
  },
  {
    id: '5',
    title: 'Dimension 5',
  },
  {
    id: '6',
    title: 'Dimension 6',
  },
  {
    id: '7',
    title: 'Dimension 7',
  },
  {
    id: '8',
    title: 'Dimension 8',
  },
  {
    id: '9',
    title: 'Dimension 9',
  },
  {
    id: '10',
    title: 'Dimension 10',
  },
]
