import { Box, FormControl, Switch, Tooltip } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import Info from '../../../../assets/icons/Info'
import { fetchGlobalConfigurationData } from '../../../../globals/utils'
import {
  createNewGlobalConfig,
  updateGlobalConfigData,
} from '../../../../redux/slices/dashboard/dvSlices'
import { RootState, useAppDispatch } from '../../../../redux/store'
import RevertButton from '../../../Buttons/RevertButton'
import SaveButton from '../../../Buttons/SaveButton'
import ColorPickerPopup from '../../../ConfigurationEngine/Components/ColorPickerPopup'

function DvAxes() {
  const dispatch = useAppDispatch()

  const [idForColorPicker, setIdForColorPicker] = useState('')

  const [showColorPicker, setShowColorPicker] = useState(false)

  const [data, setData] = useState<any>({})

  const [loading, setLoading] = useState(false)

  const [unChangedData, setUnChangedData] = useState<any>({})

  const [isRevertible, setIsRevertible] = useState(false)

  const dvGlobalData = useSelector(
    (state: RootState) => state.dvDashboard.globalConfigData
  )

  const dvGlobalContentId = useSelector(
    (state: RootState) => state.dvDashboard.contentId
  )

  const handleAxesDataChange = (key: string, value: string | boolean) => {
    const dataToUpdate = {
      ...data,
      axes: {
        ...data.axes,
        [key]: value,
      },
    }

    setData(dataToUpdate)
  }

  const handleSave = async () => {
    setLoading(true)
    const response: any = await fetchGlobalConfigurationData()
    let payload = {
      ...response?.data,
      DataVisualization: data,
    }

    if (dvGlobalContentId) {
      dispatch(
        updateGlobalConfigData({ contentId: dvGlobalContentId, payload })
      )
    } else {
      dispatch(createNewGlobalConfig(payload))
    }

    setLoading(false)
    setUnChangedData(data)
  }

  const revertChanges = () => {
    setData(unChangedData)
  }

  useEffect(() => {
    setData(dvGlobalData?.DataVisualization)
    setUnChangedData(dvGlobalData?.DataVisualization)
  }, [dvGlobalData, dvGlobalContentId])

  useEffect(() => {
    setIsRevertible(JSON.stringify(data) !== JSON.stringify(unChangedData))
  }, [data, unChangedData])

  return (
    <Box className='dashboardItemContainer'>
      <Box
        style={{
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          width: '100%',
          height: '100%',
          flexDirection: 'column',
          gap: '10px',
        }}
      >
        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
            paddingTop: '0.3rem',
          }}
        >
          <Box
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'start',
            }}
          >
            <Box
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'start',
              }}
            >
              <FormControl
                id='pri-dv-axes'
                style={{
                  paddingLeft: '0rem !import',
                }}
              >
                <div className='formLabelWithIcon'>
                  <FormControl.Label>Show Axes</FormControl.Label>
                  <Tooltip
                    placement='top'
                    id='dv-pri-axes-tooltip'
                    content='Toggle this option to display or hide both X and Y axes on the chart.'
                  >
                    <Info />
                  </Tooltip>
                </div>
                <Switch
                  name='showAxes dv axes'
                  id='showAxes-dv-pri-axes'
                  onChange={() =>
                    handleAxesDataChange('showAxes', !data?.axes?.showAxes)
                  }
                  className='switchRoot'
                  isChecked={data?.axes?.showAxes}
                  isDisabled={loading}
                >
                  {data?.axes?.showAxes ? 'Show ' : 'Hide '}
                  Axes
                </Switch>
              </FormControl>
            </Box>
            {data?.axes?.showAxes && (
              <Box
                style={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'start',
                  alignItems: 'start',
                }}
              >
                <FormControl
                  id='pri-dv-reverse-axis-type'
                  style={{
                    paddingLeft: '0rem !import',
                  }}
                >
                  <div className='formLabelWithIcon'>
                    <FormControl.Label>Reverse Axis Type</FormControl.Label>
                    <Tooltip
                      placement='top'
                      id='dv-pri-reverse-axis-type-tooltip'
                      content='Select the type of axis inversion for the chart: category or value.'
                    >
                      <Info />
                    </Tooltip>
                  </div>
                  <Switch
                    name='showAxes dv reverse x-axis direction'
                    id='showAxes-dv-pri-reverse-x-axis-direction'
                    onChange={() =>
                      handleAxesDataChange(
                        'reverseAxisType',
                        !data?.axes?.reverseAxisType
                      )
                    }
                    className='switchRoot'
                    isChecked={data?.axes?.reverseAxisType}
                    isDisabled={loading}
                  >
                    {data?.axes?.reverseAxisType
                      ? 'Y-axis type-'
                      : 'X-axis type-'}
                    category
                  </Switch>
                </FormControl>
              </Box>
            )}
          </Box>
        </Box>
        {data?.axes?.showAxes && (
          <>
            <Box
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'start',
              }}
            >
              <Box
                className='w-50'
                style={{
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'start',
                  alignItems: 'start',
                  paddingTop: '0.3rem',
                }}
              >
                <FormControl
                  id='pri-dv-x-axis'
                  style={{
                    paddingLeft: '0rem !import',
                  }}
                >
                  <div className='formLabelWithIcon'>
                    <FormControl.Label>Show X-axis</FormControl.Label>
                    <Tooltip
                      placement='top'
                      id='dv-pri-x-axis-tooltip'
                      content='Toggle this option to display or hide the X-axis on the chart.'
                    >
                      <Info />
                    </Tooltip>
                  </div>
                  <Switch
                    name='showAxes dv x-axis'
                    id='showAxes-dv-pri-x-axis'
                    onChange={() =>
                      handleAxesDataChange('xAxisShow', !data?.axes?.xAxisShow)
                    }
                    className='switchRoot'
                    isChecked={data?.axes?.xAxisShow}
                    isDisabled={loading}
                  >
                    {data?.axes?.xAxisShow ? 'Show ' : 'Hide '}
                    X-Axis
                  </Switch>
                </FormControl>
                {data?.axes?.xAxisShow && (
                  <>
                    <FormControl
                      id='pri-dv-x-axis-line'
                      style={{
                        paddingLeft: '0rem !import',
                      }}
                    >
                      <div className='formLabelWithIcon'>
                        <FormControl.Label>Show X-axis Line</FormControl.Label>
                        <Tooltip
                          placement='top'
                          id='dv-pri-x-axis-line-tooltip'
                          content='Toggle this option to display or hide the line representing the X-axis.'
                        >
                          <Info />
                        </Tooltip>
                      </div>
                      <Switch
                        name='showAxes dv y-axis-line'
                        id='showAxes-dv-pri-y-axis-line'
                        onChange={() =>
                          handleAxesDataChange(
                            'showXAxisLine',
                            !data?.axes?.showXAxisLine
                          )
                        }
                        className='switchRoot'
                        isChecked={data?.axes?.showXAxisLine}
                        isDisabled={loading}
                      >
                        {data?.axes?.showXAxisLine ? 'Show ' : 'Hide '}
                        X-Axis Line
                      </Switch>
                    </FormControl>
                    <FormControl id='dv-pri-x-axis-lineColor'>
                      <div className='formLabelWithIcon'>
                        <FormControl.Label>X-axis Line Color</FormControl.Label>
                        <Tooltip
                          placement='top'
                          id='dv-pri-x-axis-lineColor-tooltip'
                          content='Select the color for the X-axis line.'
                        >
                          <Info />
                        </Tooltip>
                      </div>
                      <div
                        className={`bg edit`}
                        onClick={() => {
                          setShowColorPicker(!showColorPicker)
                          setIdForColorPicker('xAxisLineColor')
                        }}
                      >
                        {data?.axes?.xAxisLineColor ? (
                          <div
                            className='color'
                            style={{
                              backgroundColor: data?.axes?.xAxisLineColor,
                            }}
                          ></div>
                        ) : (
                          <span>Pick</span>
                        )}
                      </div>
                    </FormControl>

                    <FormControl
                      id='pri-dv-reverse-x-axis-direction+'
                      style={{
                        paddingLeft: '0rem !import',
                      }}
                    >
                      <div className='formLabelWithIcon'>
                        <FormControl.Label>Reverse X-axis</FormControl.Label>
                        <Tooltip
                          placement='top'
                          id='dv-pri-reverse-x-axis-direction-tooltip'
                          content='Enable this option to invert the direction of the X-axis.'
                        >
                          <Info />
                        </Tooltip>
                      </div>
                      <Switch
                        name='showAxes dv reverse x-axis direction'
                        id='showAxes-dv-pri-reverse-x-axis-direction'
                        onChange={() =>
                          handleAxesDataChange(
                            'reverseXAxis',
                            !data?.axes?.reverseXAxis
                          )
                        }
                        className='switchRoot'
                        isChecked={data?.axes?.reverseXAxis}
                        isDisabled={loading}
                      >
                        {data?.axes?.reverseXAxis ? 'Inverse ' : 'LeftToRight '}
                      </Switch>
                    </FormControl>
                  </>
                )}
              </Box>
              <Box
                className='w-50'
                style={{
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'start',
                  alignItems: 'start',
                  paddingTop: '0.3rem',
                }}
              >
                <FormControl
                  id='pri-dv-y-axis'
                  style={{
                    paddingLeft: '0rem !import',
                  }}
                >
                  <div className='formLabelWithIcon'>
                    <FormControl.Label>Show Y-axis</FormControl.Label>
                    <Tooltip
                      placement='top'
                      id='dv-pri-y-axis-tooltip'
                      content='Toggle this option to display or hide the Y-axis on the chart.'
                    >
                      <Info />
                    </Tooltip>
                  </div>
                  <Switch
                    name='showAxes dv y-axis'
                    id='showAxes-dv-pri-y-axis'
                    onChange={() =>
                      handleAxesDataChange('yAxisShow', !data?.axes?.yAxisShow)
                    }
                    className='switchRoot'
                    isChecked={data?.axes?.yAxisShow}
                    isDisabled={loading}
                  >
                    {data?.axes?.yAxisShow ? 'Show ' : 'Hide '}
                    Y-Axis
                  </Switch>
                </FormControl>
                {data?.axes?.yAxisShow && (
                  <>
                    <FormControl
                      id='pri-dv-y-axis-line'
                      style={{
                        paddingLeft: '0rem !import',
                      }}
                    >
                      <div className='formLabelWithIcon'>
                        <FormControl.Label>Show Y-axis Line</FormControl.Label>
                        <Tooltip
                          placement='top'
                          id='dv-pri-y-axis-line-tooltip'
                          content='Toggle this option to display or hide the line representing the Y-axis.'
                        >
                          <Info />
                        </Tooltip>
                      </div>
                      <Switch
                        name='showAxes dv y-axis-line'
                        id='showAxes-dv-pri-y-axis-line'
                        onChange={() =>
                          handleAxesDataChange(
                            'showYAxisLine',
                            !data?.axes?.showYAxisLine
                          )
                        }
                        className='switchRoot'
                        isChecked={data?.axes?.showYAxisLine}
                        isDisabled={loading}
                      >
                        {data?.axes?.showYAxisLine ? 'Show ' : 'Hide '}
                        Y-Axis Line
                      </Switch>
                    </FormControl>
                    <FormControl id='dv-pri-x-axis-lineColor'>
                      <div className='formLabelWithIcon'>
                        <FormControl.Label>Y-axis Line Color</FormControl.Label>
                        <Tooltip
                          placement='top'
                          id='dv-pri-y-axis-lineColor-tooltip'
                          content='Select the color for the Y-axis line.'
                        >
                          <Info />
                        </Tooltip>
                      </div>
                      <div
                        className={`bg edit`}
                        onClick={() => {
                          setShowColorPicker(!showColorPicker)
                          setIdForColorPicker('yAxisLineColor')
                        }}
                      >
                        {data?.axes?.yAxisLineColor ? (
                          <div
                            className='color'
                            style={{
                              backgroundColor: data?.axes?.yAxisLineColor,
                            }}
                          ></div>
                        ) : (
                          <span>Pick</span>
                        )}
                      </div>
                    </FormControl>

                    <FormControl
                      id='pri-dv-reverse-y-axis-direction+'
                      style={{
                        paddingLeft: '0rem !import',
                      }}
                    >
                      <div className='formLabelWithIcon'>
                        <FormControl.Label>Reverse Y-axis</FormControl.Label>
                        <Tooltip
                          placement='top'
                          id='dv-pri-reverse-y-axis-direction-tooltip'
                          content='Enable this option to invert the direction of the Y-axis.'
                        >
                          <Info />
                        </Tooltip>
                      </div>
                      <Switch
                        name='showAxes dv reverse x-axis direction'
                        id='showAxes-dv-pri-reverse-x-axis-direction'
                        onChange={() =>
                          handleAxesDataChange(
                            'reverseYAxis',
                            !data?.axes?.reverseYAxis
                          )
                        }
                        className='switchRoot'
                        isChecked={data?.axes?.reverseYAxis}
                        isDisabled={loading}
                      >
                        {data?.axes?.reverseYAxis ? 'Inverse' : 'BottomToTop'}
                      </Switch>
                    </FormControl>
                  </>
                )}
              </Box>
            </Box>
          </>
        )}
      </Box>
      <Box
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <RevertButton
          isDisabled={!isRevertible || loading}
          onClick={revertChanges}
          tooltipPlacement='right'
        ></RevertButton>
        <SaveButton
          onClick={handleSave}
          isDisabled={!isRevertible}
          isLoading={loading}
        ></SaveButton>
      </Box>
      <ColorPickerPopup
        isOpen={showColorPicker}
        handleClose={() => {
          setShowColorPicker(false)
        }}
        selectedColors={data?.axes?.[(idForColorPicker as any) || '']}
        onColorPick={(color: any) => {
          handleAxesDataChange(idForColorPicker, color.value)
          setShowColorPicker(false)
        }}
      />
    </Box>
  )
}

export default DvAxes
