import {
  Box,
  FormControl,
  Select,
  Switch,
  Tooltip,
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import Info from '../../../../assets/icons/Info'
import { fetchGlobalConfigurationData } from '../../../../globals/utils'
import {
  createNewGlobalConfig,
  updateGlobalConfigData,
} from '../../../../redux/slices/dashboard/dvSlices'
import { RootState, useAppDispatch } from '../../../../redux/store'
import RevertButton from '../../../Buttons/RevertButton'
import SaveButton from '../../../Buttons/SaveButton'

function DvGeneral() {
  const dispatch = useAppDispatch()

  const [data, setData] = useState<any>({})

  const [loading, setLoading] = useState(false)

  const [unChangedData, setUnChangedData] = useState<any>({})

  const [isRevertible, setIsRevertible] = useState(false)

  const dvGlobalData: any = useSelector(
    (state: RootState) => state.dvDashboard.globalConfigData
  )

  const dvGlobalContentId = useSelector(
    (state: RootState) => state.dvDashboard.contentId
  )

  const handleGeneralDataChange = (key: string, value: string | boolean) => {
    const dataToUpdate = {
      ...data,
      general: {
        ...data.general,
        [key]: value,
      },
    }

    setData(dataToUpdate)
  }

  const handleSave = async () => {
    setLoading(true)
    const response: any = await fetchGlobalConfigurationData()
    let payload = {
      ...response?.data,
      DataVisualization: data,
    }

    if (dvGlobalContentId) {
      dispatch(
        updateGlobalConfigData({ contentId: dvGlobalContentId, payload })
      )
    } else {
      dispatch(createNewGlobalConfig(payload))
    }

    setLoading(false)
    setUnChangedData(data)
  }

  const revertChanges = () => {
    setData(unChangedData)
  }

  useEffect(() => {
    setData(dvGlobalData?.DataVisualization)
    setUnChangedData(dvGlobalData?.DataVisualization)
  }, [dvGlobalData, dvGlobalContentId])

  useEffect(() => {
    setIsRevertible(JSON.stringify(data) !== JSON.stringify(unChangedData))
  }, [data, unChangedData])

  return (
    <Box className='dashboardItemContainer'>
      <Box
        style={{
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          width: '100%',
          height: '100%',
          flexDirection: 'column',
          gap: '10px',
        }}
      >
        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
            paddingTop: '0.5rem',
          }}
        >
          <FormControl
            id='original-domain'
            style={{
              paddingLeft: '0rem !import',
              width: '30%',
            }}
          >
            <div className='formLabelWithIcon'>
              <FormControl.Label>Show Title</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Toggle this option to display or hide the title of the chart.'
              >
                <Info />
              </Tooltip>
            </div>
            <Switch
              name='allow-cookies-uncontrolled'
              id='allow-cookies-uncontrolled'
              onChange={() =>
                handleGeneralDataChange('showTitle', !data?.general?.showTitle)
              }
              className='switchRoot'
              isChecked={data?.general?.showTitle}
              style={{
                paddingTop: '0.5rem',
              }}
              // isDisabled={loading}
            >
              {data?.general?.showTitle ? 'Show ' : 'Hide '}
              title
            </Switch>
          </FormControl>
          {data?.general?.showTitle && (
            <FormControl
              id='original-domain'
              style={{
                width: '70%',
              }}
            >
              <div className='formLabelWithIcon'>
                <FormControl.Label>Title Alignment</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='tooltip-1'
                  content='Choose the Title Alignment for the chart title: left, center, or right.'
                >
                  <Info />
                </Tooltip>
              </div>
              <Select
                id='optionSelect-controlled'
                name='optionSelect-controlled'
                value={data?.general?.titleAlignment}
                onChange={(e) =>
                  handleGeneralDataChange('titleAlignment', e.target.value)
                }
                placeholder='Select Title Alignment'
                style={{
                  width: '100%',
                }}
                // isDisabled={loading}
              >
                <Select.Option value='left'>Left</Select.Option>
                <Select.Option value='center'>Center</Select.Option>
                <Select.Option value='right'>Right</Select.Option>
              </Select>
            </FormControl>
          )}
        </Box>
        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
            paddingTop: '0.5rem',
          }}
        >
          <FormControl
            id='original-domain'
            style={{
              paddingLeft: '0rem !import',
            }}
          >
            <div className='formLabelWithIcon'>
              <FormControl.Label>Is Graph Zoomable</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Enable or disable zooming functionality for the graph, allowing users to focus on specific data points.'
              >
                <Info />
              </Tooltip>
            </div>
            <Switch
              name='allow-cookies-uncontrolled'
              id='allow-cookies-uncontrolled'
              onChange={() =>
                handleGeneralDataChange(
                  'isZoomable',
                  !data?.general?.isZoomable
                )
              }
              isChecked={data?.general?.isZoomable}
              className='switchRoot'
              // isDisabled={loading}
            >
              {data?.general?.isZoomable ? 'Enable ' : 'Disable '}
              graph zoom
            </Switch>
          </FormControl>
        </Box>

        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
            paddingTop: '0.5rem',
          }}
        >
          <FormControl
            id='original-domain'
            style={{
              paddingLeft: '0rem !import',
            }}
          >
            <div className='formLabelWithIcon'>
              <FormControl.Label>Show Legend</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Toggle this option to display or hide the legend on the chart.'
              >
                <Info />
              </Tooltip>
            </div>
            <Switch
              name='allow-cookies-uncontrolled'
              id='allow-cookies-uncontrolled'
              onChange={() =>
                handleGeneralDataChange(
                  'showLegend',
                  !data?.general?.showLegend
                )
              }
              isChecked={data?.general?.showLegend}
              className='switchRoot'
              // isDisabled={loading}
            >
              {data?.general?.showLegend ? 'Show ' : 'Hide '}
              Legend
            </Switch>
          </FormControl>
        </Box>
        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
            paddingTop: '0.5rem',
            gap: '1rem',
          }}
        >
          {data?.general?.showLegend && (
            <FormControl
              id='original-domain'
              style={{
                width: '50%',
              }}
            >
              <div className='formLabelWithIcon'>
                <FormControl.Label>Legend Alignment</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='tooltip-1'
                  content='Choose the legendAlignment for the legend: left, center, or right.'
                >
                  <Info />
                </Tooltip>
              </div>
              <Select
                id='optionSelect-controlled'
                name='optionSelect-controlled'
                value={data?.general?.legendAlignment}
                onChange={(e) =>
                  handleGeneralDataChange('legendAlignment', e.target.value)
                }
                placeholder='Select Title Alignment'
                // isDisabled={loading}
              >
                <Select.Option value='left'>Left</Select.Option>
                <Select.Option value='center'>Center</Select.Option>
                <Select.Option value='right'>Right</Select.Option>
              </Select>
            </FormControl>
          )}
          {data?.general?.showLegend && (
            <FormControl
              id='original-domain'
              style={{
                width: '50%',
              }}
            >
              <div className='formLabelWithIcon'>
                <FormControl.Label>Legend Orientation</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='tooltip-1'
                  content='Select the legendOrientation for the legend: horizontal or vertical.'
                >
                  <Info />
                </Tooltip>
              </div>
              <Select
                id='optionSelect-controlled'
                name='optionSelect-controlled'
                value={data?.general?.legendOrientation}
                onChange={(e) =>
                  handleGeneralDataChange('legendOrientation', e.target.value)
                }
                placeholder='Select Legend Orientation'
                // isDisabled={loading}
              >
                <Select.Option value='horizontal'>Horizontal</Select.Option>
                <Select.Option value='vertical'>Vertical</Select.Option>
              </Select>
            </FormControl>
          )}
        </Box>
      </Box>
      <Box
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <RevertButton
          isDisabled={!isRevertible || loading}
          onClick={revertChanges}
          tooltipPlacement='right'
        ></RevertButton>
        <SaveButton
          onClick={handleSave}
          isDisabled={!isRevertible}
          isLoading={loading}
        ></SaveButton>
      </Box>
    </Box>
  )
}

export default DvGeneral
