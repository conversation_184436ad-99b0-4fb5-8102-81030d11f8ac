import { EditorAppSDK } from '@contentful/app-sdk'
import { <PERSON><PERSON>, <PERSON>lex, Spinner, Text } from '@contentful/f36-components'
import { PlusIcon } from '@contentful/f36-icons'
import React, { useContext, useState } from 'react'
import { GlobalContext } from '../../../contexts/globalContext'
import { createOrUpdateAsset } from '../../../globals/utils'
import Asset from './Assets'
import './index.scss'

interface FileUploadPropsI {
  sdk: EditorAppSDK
  asset: any
  onAction: (asset: any) => void
}

const FileUpload = (props: FileUploadPropsI) => {
  const { sdk, asset, onAction } = props

  const [isLoading, setIsLoading] = useState(false)

  const { currentLocale } = useContext(GlobalContext)

  const [selectedAsset, setSelectedAsset] = useState(asset)

  const handleFileChange = async (event: any) => {
    setIsLoading(true)
    const file = event.target.files[0]
    if (!file) {
      return
    }
    //Create or update asset
    const asset = await createOrUpdateAsset(file, currentLocale)
    setSelectedAsset(asset)
    onAction(asset)
    setIsLoading(false)
  }

  const openAssetSelector = async () => {
    setIsLoading(true)
    if (sdk) {
      const asset = await sdk.dialogs.selectSingleAsset()
      if (asset) {
        setSelectedAsset(asset)
        onAction(asset)
      }
      setIsLoading(false)
    }
  }

  const handleFileEditOrDel = async (asset: any) => {
    setSelectedAsset(asset)
    onAction(asset)
  }

  return (
    <>
      {' '}
      {isLoading ? (
        <Flex>
          <Text marginRight='spacingXs'>Loading</Text>
          <Spinner />
        </Flex>
      ) : selectedAsset ? (
        <Asset
          asset={selectedAsset}
          currentLocale={currentLocale}
          sdk={sdk}
          onFileUpdate={handleFileEditOrDel}
        />
      ) : (
        <div className='dv-inputDiv'>
          <div className='css-cjqc4f dv-newFile'>
            <input
              type='file'
              id='dv-fileInput'
              className='dv-input'
              onChange={handleFileChange}
              disabled={isLoading} // Disable input during loading
            />
            <label
              htmlFor='dv-fileInput'
              id='dv-fileInputLabel'
              className='css-40g50j dv-lable'
            >
              <PlusIcon className='css-3u52eb' /> Add new Asset
            </label>
          </div>
          <Button
            variant='secondary'
            startIcon={<PlusIcon />}
            size='small'
            onClick={openAssetSelector}
            isLoading={isLoading}
          >
            Add existing Asset
          </Button>
        </div>
      )}
    </>
  )
}

export default FileUpload
