import { IconButton } from '@contentful/f36-components'
import { useCurrentEditor } from '@tiptap/react'
import React, { useEffect } from 'react'
import { RiCloseFill } from 'react-icons/ri'
import { GlobalContext } from '../../../../contexts/globalContext'
import ColorsInput from '../../../ColorsInput'
import './index.scss'

const ColorModal = ({ onColorSelect, color, id }: any) => {
  const { editor } = useCurrentEditor()
  const { activeColor, setActiveColor, setIsColorPickerActive } =
    React.useContext(GlobalContext)

  function handleColorClick({
    name,
    value,
  }: {
    name: string
    value: string | number
  }) {
    onColorSelect({ selectedColor: value.toString(), id })
    if (activeColor.fontColor !== 'black') {
      editor?.commands.setColor(value.toString())
    } else {
      editor?.commands.unsetColor()
    }
    setActiveColor((prev) => {
      return {
        ...prev,
        fontColor: value.toString(),
      }
    })
  }

  useEffect(() => {
    setActiveColor((prev) => {
      return {
        ...prev,
        fontColor: color,
      }
    })
  }, [color])

  return (
    <div className={'customiserRoot'}>
      <IconButton
        className='close'
        size='small'
        variant='transparent'
        aria-label='Select the date'
        icon={
          <RiCloseFill
            size='tiny'
            onClick={() => {
              setIsColorPickerActive(false)
            }}
            color='#333'
          />
        }
      />
      <ColorsInput
        name={'Font Color'}
        onClick={handleColorClick}
        activeColor={activeColor?.fontColor}
        showUndo={false}
      />
    </div>
  )
}

export default ColorModal
