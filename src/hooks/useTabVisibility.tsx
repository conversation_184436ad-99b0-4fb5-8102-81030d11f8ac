import { useEffect, useState } from 'react'

export const useTabVisibility = () => {
  const [isTabFocused, setIsTabFocused] = useState(true)

  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsTabFocused(!document.hidden)
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  return isTabFocused
}
