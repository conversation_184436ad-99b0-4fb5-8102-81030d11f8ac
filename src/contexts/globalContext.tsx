import React, { createContext } from 'react'

interface GlobalStateI {
  currentLocale: string
  setCurrentLocale: React.Dispatch<React.SetStateAction<string>>
  isHyperLinkModalOpen: boolean
  setIsHyperLinkModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  isStylingModalOpen: boolean
  isCrossPostingModalOpen: boolean
  setIsCrossPostingModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  isConfigSettingsModalOpen: boolean
  setIsConfigSettingsModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  setIsStylingModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  chartFileModal: boolean
  isColorPickerActive: boolean
  setIsColorPickerActive: React.Dispatch<React.SetStateAction<boolean>>
  setCharFileModal: React.Dispatch<React.SetStateAction<boolean>>
  activeColor: {
    fontColor: string
    highlightColor: string | null
  }
  setActiveColor: React.Dispatch<
    React.SetStateAction<{
      fontColor: string
      highlightColor: string | null
    }>
  >
  isPageSettingModalOpen: boolean
  setIsPageSettingModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  isTranslationModalOpen: boolean
  setIsTranslationModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  isDataTableModalOpen: boolean
  setIsDataTableModalOpen: React.Dispatch<React.SetStateAction<boolean>>
}

export const initialGlobalState: GlobalStateI = {
  currentLocale: 'en-CA',
  setCurrentLocale: () => {},
  chartFileModal: false,
  setCharFileModal: () => {},
  isColorPickerActive: false,
  setIsColorPickerActive: () => {},
  isHyperLinkModalOpen: false,
  setIsHyperLinkModalOpen: () => {},
  isStylingModalOpen: false,
  setIsStylingModalOpen: () => {},
  isCrossPostingModalOpen: false,
  setIsCrossPostingModalOpen: () => {},
  activeColor: {
    fontColor: 'black',
    highlightColor: null,
  },
  setActiveColor: () => {},
  isConfigSettingsModalOpen: false,
  setIsConfigSettingsModalOpen: () => {},
  isPageSettingModalOpen: false,
  setIsPageSettingModalOpen: () => {},
  isTranslationModalOpen: false,
  setIsTranslationModalOpen: () => {},
  isDataTableModalOpen: false,
  setIsDataTableModalOpen: () => { }
}
export const GlobalContext = createContext(initialGlobalState)
