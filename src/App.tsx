import { locations } from '@contentful/app-sdk'
import { useSDK } from '@contentful/react-apps-toolkit'
import { ConfigProvider } from 'antd'
import React, { useMemo } from 'react'
import { Provider } from 'react-redux'
import GlobalProvider from './contexts/GlobalProvider'
import './index.css'
import ConfigScreen from './locations/ConfigScreen'
import Dialog from './locations/Dialog'
import EntryEditor from './locations/EntryEditor'
import Field from './locations/Field'
import Home from './locations/Home'
import Page from './locations/Page'
import Sidebar from './locations/Sidebar'
import store from './redux/store'

const ComponentLocationSettings = {
  [locations.LOCATION_APP_CONFIG]: ConfigScreen,
  [locations.LOCATION_ENTRY_FIELD]: Field,
  [locations.LOCATION_ENTRY_EDITOR]: EntryEditor,
  [locations.LOCATION_DIALOG]: Dialog,
  [locations.LOCATION_ENTRY_SIDEBAR]: Sidebar,
  [locations.LOCATION_PAGE]: Page,
  [locations.LOCATION_HOME]: Home,
}

const App = () => {
  const sdk = useSDK()

  const Component = useMemo(() => {
    for (const [location, component] of Object.entries(
      ComponentLocationSettings
    )) {
      if (sdk.location.is(location)) {
        return component
      }
    }
  }, [sdk.location])
 
  return Component ? (
    <ConfigProvider
      theme={{
        token: {
          fontFamily: 'Aeonik, sans-serif',
          colorPrimary: '#0028d7',
          colorInfo: '#0028d7',
          borderRadius: 1,
          colorError: '#e2222b',
        },
        components: {
          Table: {
            colorPrimary: 'rgb(0,0,0)',
            controlItemBgActive: 'rgb(35,39,41)',
          },
          Checkbox: {
            colorPrimary: 'rgb(0,0,0)',
            colorPrimaryHover: 'rgb(68,76,86)',
          },
          Form: {
            itemMarginBottom: 35,
            //"lineHeight": 1
          },
          Alert: {
            colorInfo: 'rgb(0,0,0)',
            colorInfoBg: 'rgb(241,242,244)',
            colorInfoBorder: 'rgb(44,44,45)',
            padding: 0,
          },
        },
      }}
    >
    <Provider store={store}>
      <GlobalProvider>
   
        <Component />
  
      </GlobalProvider>
    </Provider>
    </ConfigProvider>
  ) : null
}

export default App
