import { Icon } from '@contentful/f36-components'
import React from 'react'

function <PERSON><PERSON>rrow(props: { colour?: string }) {
  const { colour = '#000' } = props
  return (
    <Icon {...props} size={'tiny'}>
      <svg
        width='24px'
        height='24px'
        viewBox='0 0 24 24'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          fill-rule='evenodd'
          clip-rule='evenodd'
          d='M11.7071 4.29289C11.3166 3.90237 10.6834 3.90237 10.2929 4.29289L3.29289 11.2929C2.90237 11.6834 2.90237 12.3166 3.29289 12.7071L10.2929 19.7071C10.6834 20.0976 11.3166 20.0976 11.7071 19.7071C12.0976 19.3166 12.0976 18.6834 11.7071 18.2929L6.41421 13H20C20.5523 13 21 12.5523 21 12C21 11.4477 20.5523 11 20 11H6.41421L11.7071 5.70711C12.0976 5.31658 12.0976 4.68342 11.7071 4.29289Z'
          fill={colour}
        />
      </svg>
    </Icon>
  )
}

export default LeftArrow
