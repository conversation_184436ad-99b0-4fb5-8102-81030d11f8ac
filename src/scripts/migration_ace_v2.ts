import * as contentful from 'contentful-management'
import { ENV_VARIABLES } from '../constant/variables'

// Generic function to copy values from one field to another across all locales
async function copyFieldValueForAllLocales(
  entryFields: { [x: string]: { [x: string]: any } },
  fromField: string,
  toField: string,
  localeCodes: any[]
) {
  let fields = { ...entryFields }

  localeCodes.forEach((locale: string | number) => {
    if (fields[fromField]?.[locale]) {
      fields[toField] = fields[toField] || {}
      fields[toField][locale] = fields[fromField][locale]
    }
  })

  return fields
}

export async function MigrateDataToAceV2() {
  try {
    const client = contentful.createClient({
      space: ENV_VARIABLES.contentfulSpaceID,
      accessToken: ENV_VARIABLES.contentfulToken,
    })

    const space = await client.getSpace(ENV_VARIABLES.contentfulSpaceID)
    const environment = await space.getEnvironment(ENV_VARIABLES.contentfulEnvironment)

    let allPageEntries: any[] = []
    let skip = 0
    const limit = 100
    let totalEntries = 0

    // Step 1: Get all locales in the environment
    const locales = await environment.getLocales()
    const localeCodes = locales.items.map((locale) => locale.code) // Get list of locale codes

    do {
      const response = await environment.getEntries({
        content_type: 'page',
        skip,
        limit,
      })

      allPageEntries = [...allPageEntries, ...response.items]
      skip += limit
      totalEntries = response.total
    } while (skip < totalEntries)

    // allPageEntries = allPageEntries.filter(
    //   (entry) => entry.sys.id === '62NL5DNGUnnPkpKHYAfCAj'
    // )

    for (let entry of allPageEntries) {
      let fields = entry.fields

      const template = fields.template?.['en-CA']

      const isPageTitleToAfsTitle =
        template === 'Insight Article' || template === 'Press Release'

      if (entry.isArchived()) continue

      const isEntryPublished =
        !entry.isDraft() && !entry.isUpdated() && entry.isPublished()

      // if (!isEntryPublished) continue

      // Step 2: Use the generic function to copy 'title' to 'afsCardTitle' across all locales
      let updatedFields = { ...fields }
      if (isPageTitleToAfsTitle) {
        updatedFields = await copyFieldValueForAllLocales(
          fields,
          'title',
          'afsCardTitle',
          localeCodes
        )
      }

      // Step 3: Use the generic function to copy 'seoTitle' to 'title' across all locales
      const updatedFields2 = await copyFieldValueForAllLocales(
        updatedFields,
        'seoTitle',
        'title',
        localeCodes
      )

      // Step 4: Use the generic function to copy 'seoDescription' to 'afsDescription' across all locales
      const updatedFields3 = await copyFieldValueForAllLocales(
        updatedFields2,
        'seoDescription',
        'afsDescription',
        localeCodes
      )

      // Step 5: Retrieve the entry again to ensure we have the latest version
      let updatedEntry = await environment.getEntry(entry.sys.id)

      // Update the entry fields
      updatedEntry.fields = { ...updatedEntry.fields, ...updatedFields3 }

      // Step 6: Publish the entry if necessary
      updatedEntry.update()
      if (isEntryPublished) {
        await retryPublish(updatedEntry, environment)
      } else {
        console.log(
          `Updated entry ${entry.sys.id}, but keeping it unpublished as per its original status.`
        )
      }
    }
  } catch (error) {
    console.log('error func: ', error)
  }
}

// Retry logic to publish the entry in case of VersionMismatch
async function retryPublish(entry: any, environment: any, retries = 3) {
  try {
    // If the entry was published before the changes and had no unpublished changes, publish it

    const latestEntry = await environment.getEntry(entry.sys.id)
    await latestEntry.publish()

    console.log(`Published entry: ${entry.sys.id}`)
  } catch (error: any) {
    const errorCode = JSON.parse(error.message)?.status
    if (errorCode === 409 && retries > 0) {
      console.log(
        'Version mismatch detected, refetching entry and retrying update...'
      )
      // try updating again
      await retryPublish(entry, environment, retries - 1)
    } else {
      console.log('Error in update: ', error)
    }
  }
}
