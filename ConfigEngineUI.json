{"DataVisualization": {"isEnabled": true, "tabs": {"general": {"id": 1, "title": "General", "key": "dv-general", "isEnabled": true, "fields": [{"id": 1, "name": "Show Title", "key": "showTitle", "type": "switch", "helpText": "Toggle this option to display or hide the title of the chart."}, {"id": 2, "name": "Title Alignment", "key": "titleAlignment", "dependentField": "showTitle", "dependentValue": true, "type": "select", "helpText": "Toggle this option to display or hide the title of the chart.", "options": [{"id": 1, "name": "Left", "value": "left"}, {"id": 2, "name": "Center", "value": "center"}, {"id": 3, "name": "Right", "value": "right"}]}, {"id": 3, "name": "Is Graph Zoomable", "key": "isZoomable", "type": "switch", "helpText": "Toggle this option to display or hide the title of the chart."}, {"id": 4, "name": "Show Legend", "key": "isZoomable", "type": "switch", "helpText": "Toggle this option to display or hide the legend on the chart."}, {"id": 5, "name": "Legend Alignment", "key": "legendAlignment", "type": "select", "dependentField": "legendAlignment", "dependentValue": true, "helpText": "Choose the legendAlignment for the legend: left, center, or right.", "options": [{"id": 1, "name": "Left", "value": "left"}, {"id": 2, "name": "Center", "value": "center"}, {"id": 3, "name": "Right", "value": "right"}]}, {"id": 6, "name": "Legend Orientation", "key": "legendOrientation", "type": "select", "dependentField": "legendAlignment", "dependentValue": true, "helpText": "Select the legendOrientation for the legend: horizontal or vertical.", "options": [{"id": 1, "name": "Horizontal", "value": "horizontal"}, {"id": 2, "name": "Vertical", "value": "vertical"}]}]}, "chart": {"id": 1, "title": "Chart & Dimensions", "key": "dv-chart", "isEnabled": true, "fields": [{"id": 1, "name": "Default Chart", "key": "defaultChart", "type": "select", "helpText": "Set this chart as the default view when the data visualization is loaded.", "options": [{"id": 1, "name": "Line-chart", "value": "Line-chart"}, {"id": 2, "name": "Bar-chart", "value": "Bar-chart"}, {"id": 3, "name": "Area-chart", "value": "Area-chart"}, {"id": 4, "name": "Combo-chart", "value": "Combo-chart"}, {"id": 5, "name": "Pie-chart", "value": "Pie-chart"}, {"id": 6, "name": "Doughnut-chart", "value": "Doughnut-chart"}]}, {"id": 3, "name": "Dimensions Default Color", "key": "dimensions", "type": "customDimensionsTable", "helpText": "Specify the default colors for each dimension in the chart."}]}, "styles": {"id": 3, "title": "Styles", "key": "dv-styles", "isEnabled": true, "fields": [{"id": 1, "name": "Show Grid", "key": "showGrid", "type": "switch", "helpText": "Toggle this option to display or hide the grid lines on the chart background."}, {"id": 2, "name": "Chart Bg Color", "key": "graphBgColor", "dependentField": "showGrid", "dependentValue": true, "type": "colorPicker", "helpText": "Toggle this option to display or hide the title of the chart."}, {"id": 3, "name": "Grid Bg Color", "key": "gridBgColor", "dependentField": "showGrid", "dependentValue": true, "type": "colorPicker", "helpText": "Toggle this option to display or hide the title of the chart."}, {"id": 4, "name": "Grid Line Color", "key": "gridLineColor", "dependentField": "showGrid", "dependentValue": true, "type": "colorPicker", "helpText": "Choose the background color for the grid area within the chart."}, {"id": 5, "name": "Grid Format", "key": "gridFormat", "type": "select", "dependentField": "showGrid", "dependentValue": true, "helpText": "Choose the format for the grid lines: horizontal, vertical, or both.", "options": [{"id": 1, "name": "Split", "value": "split"}, {"id": 2, "name": "Horizontal", "value": "horizontal"}, {"id": 3, "name": "Vertical", "value": "vertical"}]}, {"id": 6, "name": "Grid Line Style", "key": "gridLineStyle", "type": "select", "helpText": "Choose the type of grid lines for the chart: solid, dashed, or dotted.", "options": [{"id": 1, "name": "Solid", "value": "solid"}, {"id": 2, "name": "Dashed", "value": "dashed"}, {"id": 2, "name": "Dotted", "value": "dotted"}]}, {"id": 7, "name": "Border Width", "key": "gridLineColor", "type": "number", "helpText": "Set the width of the border around the chart.", "suffix": "px"}, {"id": 8, "name": "Border Style", "key": "borderType", "type": "select", "helpText": "Choose the type of border for the chart: solid, dashed, or dotted.", "options": [{"id": 1, "name": "Solid", "value": "solid"}, {"id": 2, "name": "Dashed", "value": "dashed"}, {"id": 2, "name": "Dotted", "value": "dotted"}]}, {"id": 9, "name": "Border Shadow", "key": "isBorderShadow", "type": "switch", "helpText": "Enable or disable shadow for the chart border to create a depth effect."}, {"id": 10, "name": "Border Color", "key": "borderColor", "type": "colorPicker", "helpText": "Select the color for the chart's border."}]}, "axes": {"id": 3, "title": "Axes", "key": "dv-axes", "isEnabled": true, "fields": [{"id": 1, "name": "Show Axes", "key": "showAxes", "type": "switch", "helpText": "Toggle this option to display or hide both X and Y axes on the chart."}, {"id": 2, "name": "Reverse Axis Type", "key": "reverseAxisType", "dependentField": "showAxes", "dependentValue": true, "type": "switch", "helpText": "Select the type of axis inversion for the chart: category or value."}, {"id": 3, "name": "Show X-axis", "key": "xAxisShow", "dependentField": "showAxes", "dependentValue": true, "type": "switch", "helpText": "Toggle this option to display or hide the X-axis on the chart."}, {"id": 4, "name": "Show X-axis Line", "key": "showXAxisLine", "dependentField": "xAxisShow", "dependentValue": true, "type": "switch", "helpText": "Toggle this option to display or hide the X-axis on the chart."}, {"id": 5, "name": "X-axis Line Color", "key": "xAxisLineColor", "dependentField": "xAxisShow", "dependentValue": true, "type": "colorPicker", "helpText": "Select the color for the X-axis line."}, {"id": 6, "name": "Reverse X-axis", "key": "reverseXAxis", "dependentField": "xAxisShow", "dependentValue": true, "type": "switch", "helpText": "Enable this option to invert the direction of the X-axis."}, {"id": 7, "name": "Show Y-axis", "key": "yAxisShow", "dependentField": "showAxes", "dependentValue": true, "type": "switch", "helpText": "Toggle this option to display or hide the Y-axis on the chart."}, {"id": 8, "name": "Show Y-axis Line", "key": "showYAxisLine", "dependentField": "yAxisShow", "dependentValue": true, "type": "switch", "helpText": "Toggle this option to display or hide the Y-axis on the chart."}, {"id": 9, "name": "Y-axis Line Color", "key": "yAxisLineColor", "dependentField": "yAxisShow", "dependentValue": true, "type": "colorPicker", "helpText": "Select the color for the Y-axis line."}, {"id": 10, "name": "Reverse Y-axis", "key": "reverseYAxis", "dependentField": "yAxisShow", "dependentValue": true, "type": "switch", "helpText": "Enable this option to invert the direction of the Y-axis."}]}}}, "PageSetting": {"isEnabled": true, "tabs": {"general": {"id": 1, "title": "General", "key": "ps-general", "isEnabled": true, "fields": [{"id": 1, "name": "Page Template", "key": "template", "type": "select", "helpText": "Toggle this option to display or hide the title of the chart.", "options": [{"id": 1, "name": "Generic", "value": "Generic"}, {"id": 2, "name": "Insight Article", "value": "Insight Article"}, {"id": 3, "name": "Press Release", "value": "Press Release"}, {"id": 4, "name": "Object", "value": "Object"}, {"id": 5, "name": "Home", "value": "Home"}]}]}, "discovery": {"id": 1, "title": "Discovery", "key": "ps-discovery", "isEnabled": true, "fields": [{"id": 1, "name": "Hide From Algolia", "key": "hideFromAlgolia", "type": "radio", "helpText": "Toggle this option to display or hide the title of the chart.", "options": [{"id": 1, "name": "Yes", "value": "yes"}, {"id": 2, "name": "No", "value": "no"}]}]}, "navigation": {"id": 1, "title": "Navigation", "key": "ps-navigation", "isEnabled": true, "fields": [{"id": 1, "name": "Hide Header Navigation", "key": "hideHeaderNavigation", "type": "radio", "helpText": "Toggle this option to display or hide the title of the chart.", "options": [{"id": 1, "name": "Yes", "value": "yes"}, {"id": 2, "name": "No", "value": "no"}]}, {"id": 1, "name": "Hide Footer Navigation", "key": "hideFooterNavigation", "type": "radio", "helpText": "Toggle this option to display or hide the title of the chart.", "options": [{"id": 1, "name": "Yes", "value": "yes"}, {"id": 2, "name": "No", "value": "no"}]}]}}}}