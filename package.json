{"name": "transformer-app", "version": "0.1.0", "private": true, "dependencies": {"@contentful/app-sdk": "^4.22.1", "@contentful/f36-components": "4.48.0", "@contentful/f36-tokens": "4.0.2", "@contentful/field-editor-boolean": "^1.3.1", "@contentful/field-editor-date": "^1.5.1", "@contentful/field-editor-reference": "^5.15.0", "@contentful/field-editor-rich-text": "^3.9.5", "@contentful/react-apps-toolkit": "1.2.16", "@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "constate": "^3.3.2", "contentful-management": "10.39.2", "emotion": "10.0.27", "react": "^18.2.0", "react-dom": "18.2.0", "react-scripts": "5.0.1"}, "scripts": {"start": "cross-env BROWSER=none PORT=3001 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "create-app-definition": "contentful-app-scripts create-app-definition", "upload": "contentful-app-scripts upload --bundle-dir ./build", "upload-ci": "contentful-app-scripts upload --ci --bundle-dir ./build --organization-id $CONTENTFUL_ORG_ID --definition-id $CONTENTFUL_APP_DEF_ID --token $CONTENTFUL_ACCESS_TOKEN"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@contentful/app-scripts": "1.10.2", "@testing-library/jest-dom": "5.17.0", "@testing-library/react": "14.0.0", "@tsconfig/create-react-app": "2.0.1", "@types/aria-query": "^5.0.1", "@types/node": "16.18.39", "@types/react": "18.2.18", "@types/react-dom": "18.2.7", "cross-env": "7.0.3", "typescript": "4.9.5"}, "homepage": "."}