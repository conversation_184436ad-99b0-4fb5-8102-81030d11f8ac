{"name": "transformer-app", "version": "0.1.0", "private": true, "dependencies": {"@codemirror/lang-json": "^6.0.1", "@contentful/app-sdk": "^4.22.1", "@contentful/f36-components": "4.48.0", "@contentful/f36-multiselect": "^4.26.0", "@contentful/f36-tokens": "4.0.2", "@contentful/field-editor-boolean": "^1.3.1", "@contentful/field-editor-date": "^1.5.1", "@contentful/field-editor-rich-text": "^3.9.5", "@contentful/react-apps-toolkit": "1.2.16", "@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@firebase/messaging": "^0.12.10", "@reduxjs/toolkit": "^2.2.7", "@tiptap/extension-color": "^2.1.11", "@tiptap/extension-highlight": "^2.1.12", "@tiptap/extension-image": "^2.6.6", "@tiptap/extension-link": "^2.1.12", "@tiptap/extension-table": "^2.1.11", "@tiptap/extension-table-cell": "^2.1.11", "@tiptap/extension-table-header": "^2.1.11", "@tiptap/extension-table-row": "^2.1.11", "@tiptap/extension-text-align": "^2.1.12", "@tiptap/extension-text-style": "^2.1.11", "@tiptap/extension-underline": "^2.1.11", "@tiptap/pm": "^2.1.11", "@tiptap/react": "^2.1.11", "@tiptap/starter-kit": "^2.1.11", "@types/firebase": "^3.2.1", "@uiw/react-codemirror": "^4.22.2", "antd": "^5.23.0", "array-move": "^4.0.0", "axios": "^1.7.3", "codemirror": "^6.0.1", "constate": "^3.3.2", "contentful-management": "^11.27.2", "contentful-ui-extensions-sdk": "^4.29.0", "echarts": "^5.5.0", "emotion": "10.0.27", "firebase": "^10.12.4", "firebase-admin": "^12.2.0", "framer-motion": "^11.11.10", "lodash": "^4.17.21", "papaparse": "^5.4.1", "react": "18.2.0", "react-dom": "18.2.0", "react-error-boundary": "^4.0.11", "react-icons": "^4.11.0", "react-papaparse": "^4.4.0", "react-redux": "^9.1.2", "react-scripts": "5.0.1", "react-sortable-hoc": "^2.0.0", "react-tooltip": "^5.28.0", "sass": "^1.68.0", "xlsx": "^0.18.5"}, "scripts": {"start": "cross-env BROWSER=none PORT=3001 react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "winBuild": "set \"GENERATE_SOURCEMAP=false\" && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "create-app-definition": "contentful-app-scripts create-app-definition", "upload": "yarn build && contentful-app-scripts upload --bundle-dir ./build", "pretty": "prettier --write \"src/**/*.{ts,tsx,js,json}\"", "upload-ci": "contentful-app-scripts upload --ci --bundle-dir ./build --organization-id $CONTENTFUL_ORG_ID --definition-id $CONTENTFUL_APP_DEF_ID --token $CONTENTFUL_ACCESS_TOKEN"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@contentful/app-scripts": "1.10.2", "@testing-library/jest-dom": "5.17.0", "@testing-library/react": "14.0.0", "@tsconfig/create-react-app": "2.0.1", "@types/node": "16.18.39", "@types/react": "18.2.18", "@types/react-dom": "18.2.7", "cross-env": "^7.0.3", "tailwindcss": "^3.4.8", "typescript": "4.9.5"}, "homepage": ".", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}